#!/usr/bin/env python3
"""
QC visualization for strict lip cropping with MediaPipe landmark overlays.
MANDATORY: Must be run and visually inspected before full preprocessing.
"""

import os
import sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
import pandas as pd
import random
from pathlib import Path

# Add src to path
sys.path.append('src')
from preprocess import VideoPreprocessor


def create_qc_visualization(num_clips=10, frames_per_clip=4, output_file="qc_strict_preview.png"):
    """
    Create QC visualization with MediaPipe landmark overlays.
    Process 50 random clips and show 10 best examples.
    """
    
    print("🔍 Starting QC visualization for strict lip cropping...")
    
    # Initialize preprocessor
    import yaml
    with open("configs/training.yaml", 'r') as f:
        config = yaml.safe_load(f)
    preprocessor = VideoPreprocessor(config)
    
    # Load training manifest to get random clips
    train_manifest = pd.read_csv("manifests/train.csv")
    
    # Sample 50 random clips for processing
    sample_size = min(50, len(train_manifest))
    sample_clips = train_manifest.sample(n=sample_size, random_state=42)
    
    print(f"📊 Processing {sample_size} random clips for QC...")
    
    processed_results = []
    
    for idx, row in sample_clips.iterrows():
        s3_key = row['s3_key']
        label = row['phrase']

        # Convert S3 key to local path
        video_path = os.path.join("data_raw", s3_key)

        if not os.path.exists(video_path):
            print(f"⚠️ Video not found: {video_path}")
            continue
        
        try:
            # Load video frames
            frames = preprocessor.load_video_frames(video_path)
            if frames is None or len(frames) == 0:
                print(f"⚠️ No frames loaded from: {video_path}")
                continue

            print(f"✅ Processing {video_path} ({len(frames)} frames)")
            
            # Process frames with strict cropping
            frame_results = []
            for frame in frames[:frames_per_clip]:  # Only process first few frames for QC
                crop, is_valid, has_landmarks, confidence, adjustment_info = preprocessor.extract_strict_lip_crop(frame)
                
                # Get MediaPipe landmarks for overlay (if available)
                landmarks = None
                if preprocessor.mediapipe_available and preprocessor.face_mesh is not None:
                    landmarks = get_mediapipe_landmarks(preprocessor.face_mesh, crop)
                else:
                    # For Python 3.13 without MediaPipe, we'll show crops without landmarks
                    landmarks = None
                
                frame_results.append({
                    'crop': crop,
                    'is_valid': is_valid,
                    'has_landmarks': has_landmarks,
                    'confidence': confidence,
                    'adjustment': adjustment_info,
                    'landmarks': landmarks
                })
            
            if frame_results:
                avg_confidence = np.mean([r['confidence'] for r in frame_results])
                valid_frames = sum(1 for r in frame_results if r['is_valid'])
                
                processed_results.append({
                    'video_path': video_path,
                    'label': label,
                    'frames': frame_results,
                    'avg_confidence': avg_confidence,
                    'valid_frames': valid_frames,
                    'total_frames': len(frame_results)
                })
        
        except Exception as e:
            print(f"⚠️ Error processing {video_path}: {e}")
            continue
    
    if not processed_results:
        print("❌ No clips could be processed!")
        return False
    
    # Sort by confidence and select best examples
    processed_results.sort(key=lambda x: x['avg_confidence'], reverse=True)
    best_clips = processed_results[:num_clips]
    
    print(f"✅ Processed {len(processed_results)} clips, showing top {len(best_clips)}")
    
    # Create visualization
    create_landmark_overlay_grid(best_clips, frames_per_clip, output_file)
    
    # Print statistics
    print_qc_statistics(processed_results)
    
    return True


def get_mediapipe_landmarks(face_mesh, crop):
    """Extract MediaPipe landmarks from crop"""
    try:
        # Convert to RGB if needed
        if len(crop.shape) == 3 and crop.shape[2] == 3:
            rgb_crop = crop
        else:
            rgb_crop = cv2.cvtColor(crop, cv2.COLOR_BGR2RGB)
        
        # Convert to uint8 if needed
        if rgb_crop.dtype == np.float32:
            rgb_crop = (rgb_crop * 255).astype(np.uint8)
        
        # Run MediaPipe
        results = face_mesh.process(rgb_crop)
        
        if results.multi_face_landmarks:
            return results.multi_face_landmarks[0]
        
    except Exception:
        pass
    
    return None


def create_landmark_overlay_grid(best_clips, frames_per_clip, output_file):
    """Create grid visualization with landmark overlays"""
    
    num_clips = len(best_clips)
    
    # Create figure
    fig, axes = plt.subplots(num_clips, frames_per_clip, figsize=(16, 4*num_clips))
    title = 'QC: Strict Lip Cropping'
    if any(clip_data['frames'][0].get('landmarks') for clip_data in best_clips if clip_data['frames']):
        title += ' with MediaPipe Landmarks\n(Red dots = lip landmarks)'
    else:
        title += '\n(MediaPipe not available - showing crops only)'

    fig.suptitle(title, fontsize=16, fontweight='bold')
    
    # Handle single row case
    if num_clips == 1:
        axes = [axes]
    
    for clip_idx, clip_data in enumerate(best_clips):
        for frame_idx in range(frames_per_clip):
            if frame_idx < len(clip_data['frames']):
                frame_data = clip_data['frames'][frame_idx]
                crop = frame_data['crop']
                landmarks = frame_data['landmarks']
                
                # Convert crop for display
                if crop.dtype == np.float32:
                    display_crop = (crop * 255).astype(np.uint8)
                else:
                    display_crop = crop.copy()
                
                # Overlay landmarks
                if landmarks is not None:
                    display_crop = overlay_lip_landmarks(display_crop, landmarks)
                
                # Display
                if num_clips == 1:
                    ax = axes[frame_idx]
                else:
                    ax = axes[clip_idx, frame_idx]
                
                ax.imshow(display_crop)
                ax.set_title(f'Clip {clip_idx+1}, Frame {frame_idx+1}\n'
                           f'Valid: {frame_data["is_valid"]}, Conf: {frame_data["confidence"]:.2f}\n'
                           f'Adj: {frame_data["adjustment"]}', fontsize=8)
                ax.axis('off')
            else:
                # Empty subplot
                if num_clips == 1:
                    ax = axes[frame_idx]
                else:
                    ax = axes[clip_idx, frame_idx]
                ax.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"💾 QC visualization saved to: {output_file}")


def overlay_lip_landmarks(image, landmarks):
    """Overlay lip landmarks on image"""
    h, w = image.shape[:2]
    
    # Define lip landmark indices
    lip_indices = [
        # Upper lip outer
        61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
        # Lower lip outer  
        61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321,
        # Inner lips
        78, 95, 88, 178, 87, 14, 317, 402, 318, 324,
        # Mouth corners
        61, 291
    ]
    
    # Remove duplicates
    lip_indices = list(set([idx for idx in lip_indices if idx < 468]))
    
    # Draw landmarks
    for idx in lip_indices:
        if idx < len(landmarks.landmark):
            landmark = landmarks.landmark[idx]
            x = int(landmark.x * w)
            y = int(landmark.y * h)
            
            # Draw red dot
            cv2.circle(image, (x, y), 2, (255, 0, 0), -1)
    
    return image


def print_qc_statistics(results):
    """Print QC statistics"""
    total_clips = len(results)
    valid_clips = sum(1 for r in results if r['valid_frames'] > 0)
    avg_confidence = np.mean([r['avg_confidence'] for r in results])
    
    adjustments = {}
    for result in results:
        for frame in result['frames']:
            adj = frame['adjustment']
            adjustments[adj] = adjustments.get(adj, 0) + 1
    
    print(f"\n📊 QC Statistics:")
    print("=" * 50)
    print(f"Total clips processed: {total_clips}")
    print(f"Clips with valid frames: {valid_clips} ({valid_clips/total_clips*100:.1f}%)")
    print(f"Average confidence: {avg_confidence:.3f}")
    print(f"\nAdjustment types:")
    for adj, count in adjustments.items():
        print(f"  {adj}: {count}")
    print("=" * 50)


if __name__ == "__main__":
    success = create_qc_visualization()
    
    if success:
        print("\n🎯 QC visualization complete!")
        print("📋 NEXT STEPS:")
        print("1. Open 'qc_strict_preview.png' and visually inspect the lip crops")
        print("2. Verify that red landmarks are properly positioned on lips")
        print("3. Check that crops are tight and centered on mouth region")
        print("4. If satisfied, proceed with full preprocessing")
        print("5. If not satisfied, adjust cropping parameters and re-run QC")
    else:
        print("❌ QC visualization failed!")
        sys.exit(1)
