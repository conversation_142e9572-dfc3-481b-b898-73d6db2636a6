# ICU Lipreading 4-Stage Gated Preprocessing Pipeline
# Each stage must complete with visual QC before proceeding to the next

.PHONY: stage1 stage2 stage3 stage4 qc1 qc2 qc3 qc4 unlock2 unlock3 unlock4 clean help

# Default configuration
CONFIG ?= configs/preproc.toml
LIMIT ?= 

# Stage 1: ROI Cropping + Visual QC
stage1:
	@echo "=========================================="
	@echo "STAGE 1: ROI CROPPING"
	@echo "=========================================="
	python -u scripts/stage1_crop.py --config $(CONFIG) $(if $(LIMIT),--limit $(LIMIT))
	@echo ""
	@echo "Generating QC gallery..."
	python -u -m preproc.qc_gallery --config $(CONFIG) --stage 1
	@echo "READY_FOR_REVIEW" > reports/STAGE1_READY.txt
	@echo ""
	@echo "=========================================="
	@echo "STAGE 1 COMPLETE"
	@echo "=========================================="
	@echo "✓ Processing complete"
	@echo "✓ QC gallery generated: reports/stage1_gallery.html"
	@echo "✓ Ready file created: reports/STAGE1_READY.txt"
	@echo ""
	@echo "NEXT STEPS:"
	@echo "1. Review reports/stage1_gallery.html"
	@echo "2. Check acceptance criteria (center fraction ≥85%, failure rate <5%)"
	@echo "3. If satisfied, run: make unlock2"
	@echo "4. Then run: make stage2"

# QC gallery generation (can be run separately)
qc1:
	python -u -m preproc.qc_gallery --config $(CONFIG) --stage 1

# Stage 2: MediaPipe Facial Landmarks (GATED)
stage2:
	@echo "Checking for Stage 2 unlock..."
	@test -f reports/UNLOCK_STAGE2.txt || (echo "❌ Stage 2 not unlocked. Run 'make unlock2' first." && exit 1)
	@echo "✓ Stage 2 unlocked, proceeding..."
	@echo "=========================================="
	@echo "STAGE 2: MEDIAPIPE FACIAL LANDMARKS"
	@echo "=========================================="
	python -u scripts/stage2_landmarks.py --config $(CONFIG) $(if $(LIMIT),--limit $(LIMIT))
	python -u -m preproc.qc_gallery --config $(CONFIG) --stage 2
	@echo "READY_FOR_REVIEW" > reports/STAGE2_READY.txt
	@echo ""
	@echo "=========================================="
	@echo "STAGE 2 COMPLETE"
	@echo "=========================================="
	@echo "✓ Landmark extraction complete"
	@echo "✓ QC gallery generated: reports/stage2_gallery.html"
	@echo "✓ Ready file created: reports/STAGE2_READY.txt"
	@echo ""
	@echo "NEXT STEPS:"
	@echo "1. Review reports/stage2_gallery.html"
	@echo "2. Check landmark detection success rates"
	@echo "3. If satisfied, run: make unlock3"
	@echo "4. Then run: make stage3"

qc2:
	python -u -m preproc.qc_gallery --config $(CONFIG) --stage 2

# Stage 3: YOLO + SAM Segmentation (GATED)
stage3:
	@echo "Checking for Stage 3 unlock..."
	@test -f reports/UNLOCK_STAGE3.txt || (echo "❌ Stage 3 not unlocked. Run 'make unlock3' first." && exit 1)
	@echo "✓ Stage 3 unlocked, proceeding..."
	@echo "=========================================="
	@echo "STAGE 3: YOLO + SAM SEGMENTATION"
	@echo "=========================================="
	python -u scripts/stage3_segment.py --config $(CONFIG) $(if $(LIMIT),--limit $(LIMIT))
	python -u -m preproc.qc_gallery --config $(CONFIG) --stage 3
	@echo "READY_FOR_REVIEW" > reports/STAGE3_READY.txt
	@echo ""
	@echo "=========================================="
	@echo "STAGE 3 COMPLETE"
	@echo "=========================================="
	@echo "✓ Segmentation complete"
	@echo "✓ QC gallery generated: reports/stage3_gallery.html"
	@echo "✓ Ready file created: reports/STAGE3_READY.txt"
	@echo ""
	@echo "NEXT STEPS:"
	@echo "1. Review reports/stage3_gallery.html"
	@echo "2. Check mask coverage statistics"
	@echo "3. If satisfied, run: make unlock4"
	@echo "4. Then run: make stage4"

qc3:
	python -u -m preproc.qc_gallery --config $(CONFIG) --stage 3

# Stage 4: Quality Filtering (GATED)
stage4:
	@echo "Checking for Stage 4 unlock..."
	@test -f reports/UNLOCK_STAGE4.txt || (echo "❌ Stage 4 not unlocked. Run 'make unlock4' first." && exit 1)
	@echo "✓ Stage 4 unlocked, proceeding..."
	@echo "=========================================="
	@echo "STAGE 4: QUALITY FILTERING"
	@echo "=========================================="
	python -u scripts/stage4_filter.py --config $(CONFIG) $(if $(LIMIT),--limit $(LIMIT))
	python -u -m preproc.qc_gallery --config $(CONFIG) --stage 4
	@echo "READY_FOR_REVIEW" > reports/STAGE4_READY.txt
	@echo ""
	@echo "=========================================="
	@echo "STAGE 4 COMPLETE - PIPELINE FINISHED"
	@echo "=========================================="
	@echo "✓ Quality filtering complete"
	@echo "✓ Final dataset ready: data/STAGE4_filtered/"
	@echo "✓ QC gallery generated: reports/stage4_gallery.html"
	@echo "✓ Ready file created: reports/STAGE4_READY.txt"
	@echo ""
	@echo "FINAL DATASET READY FOR TRAINING!"

qc4:
	python -u -m preproc.qc_gallery --config $(CONFIG) --stage 4

# Unlock commands (manual gates)
unlock2:
	@echo "Unlocking Stage 2..."
	@echo "ok" > reports/UNLOCK_STAGE2.txt
	@echo "✓ Stage 2 unlocked. You can now run: make stage2"

unlock3:
	@echo "Unlocking Stage 3..."
	@echo "ok" > reports/UNLOCK_STAGE3.txt
	@echo "✓ Stage 3 unlocked. You can now run: make stage3"

unlock4:
	@echo "Unlocking Stage 4..."
	@echo "ok" > reports/UNLOCK_STAGE4.txt
	@echo "✓ Stage 4 unlocked. You can now run: make stage4"

# Utility commands
clean:
	@echo "Cleaning all pipeline outputs..."
	rm -rf data/STAGE*_*/
	rm -f reports/STAGE*_READY.txt
	rm -f reports/UNLOCK_STAGE*.txt
	rm -f reports/stage*_gallery.html
	rm -f reports/stage*_summary.json
	rm -f reports/stage*_failures.csv
	@echo "✓ All pipeline outputs cleaned"

status:
	@echo "=========================================="
	@echo "PIPELINE STATUS"
	@echo "=========================================="
	@echo "Stage 1 Ready: $$(test -f reports/STAGE1_READY.txt && echo '✓' || echo '✗')"
	@echo "Stage 2 Unlocked: $$(test -f reports/UNLOCK_STAGE2.txt && echo '✓' || echo '✗')"
	@echo "Stage 2 Ready: $$(test -f reports/STAGE2_READY.txt && echo '✓' || echo '✗')"
	@echo "Stage 3 Unlocked: $$(test -f reports/UNLOCK_STAGE3.txt && echo '✓' || echo '✗')"
	@echo "Stage 3 Ready: $$(test -f reports/STAGE3_READY.txt && echo '✓' || echo '✗')"
	@echo "Stage 4 Unlocked: $$(test -f reports/UNLOCK_STAGE4.txt && echo '✓' || echo '✗')"
	@echo "Stage 4 Ready: $$(test -f reports/STAGE4_READY.txt && echo '✓' || echo '✗')"
	@echo ""
	@echo "Data directories:"
	@echo "Stage 1: $$(test -d data/STAGE1_cropped && echo '✓' || echo '✗') data/STAGE1_cropped/"
	@echo "Stage 2: $$(test -d data/STAGE2_landmarks && echo '✓' || echo '✗') data/STAGE2_landmarks/"
	@echo "Stage 3: $$(test -d data/STAGE3_segmented && echo '✓' || echo '✗') data/STAGE3_segmented/"
	@echo "Stage 4: $$(test -d data/STAGE4_filtered && echo '✓' || echo '✗') data/STAGE4_filtered/"

help:
	@echo "ICU Lipreading 4-Stage Gated Preprocessing Pipeline"
	@echo ""
	@echo "USAGE:"
	@echo "  make stage1              # Run Stage 1: ROI Cropping"
	@echo "  make unlock2             # Unlock Stage 2 after reviewing Stage 1"
	@echo "  make stage2              # Run Stage 2: MediaPipe Landmarks"
	@echo "  make unlock3             # Unlock Stage 3 after reviewing Stage 2"
	@echo "  make stage3              # Run Stage 3: YOLO + SAM Segmentation"
	@echo "  make unlock4             # Unlock Stage 4 after reviewing Stage 3"
	@echo "  make stage4              # Run Stage 4: Quality Filtering"
	@echo ""
	@echo "QC COMMANDS:"
	@echo "  make qc1                 # Generate Stage 1 QC gallery"
	@echo "  make qc2                 # Generate Stage 2 QC gallery"
	@echo "  make qc3                 # Generate Stage 3 QC gallery"
	@echo "  make qc4                 # Generate Stage 4 QC gallery"
	@echo ""
	@echo "UTILITY COMMANDS:"
	@echo "  make status              # Show pipeline status"
	@echo "  make clean               # Clean all outputs"
	@echo "  make help                # Show this help"
	@echo ""
	@echo "OPTIONS:"
	@echo "  CONFIG=path/to/config    # Use custom config file"
	@echo "  LIMIT=N                  # Process only N videos (for testing)"
	@echo ""
	@echo "EXAMPLE WORKFLOW:"
	@echo "  make stage1              # Process and review"
	@echo "  make unlock2             # Manual approval"
	@echo "  make stage2              # Process and review"
	@echo "  make unlock3             # Manual approval"
	@echo "  make stage3              # Process and review"
	@echo "  make unlock4             # Manual approval"
	@echo "  make stage4              # Final processing"
