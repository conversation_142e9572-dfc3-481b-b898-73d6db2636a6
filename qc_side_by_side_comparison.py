#!/usr/bin/env python3
"""
Generate side-by-side QC comparison: strict grid heuristic vs MediaPipe landmarks.
Must be run in Python 3.11 environment.
"""

import os
import sys
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import glob
import random
from pathlib import Path

# Import MediaPipe preprocessor only
from mediapipe_landmark_preprocessor import MediaPipeLandmarkPreprocessor


def load_same_20_clips():
    """Load the same 20 clips for comparison"""
    # Use the MediaPipe manifest as reference
    manifest_path = 'trial_manifest_mediapipe.csv'
    if not os.path.exists(manifest_path):
        print("❌ MediaPipe manifest not found!")
        return None
    
    df = pd.read_csv(manifest_path)
    
    # Select 20 clips for comparison
    selected_clips = df.head(20).copy()
    
    return selected_clips


def process_with_strict_grid(clips_df):
    """Process clips with strict grid heuristic approach"""
    print("📊 Processing with strict grid heuristic...")
    
    results = []
    processed_frames_dict = {}
    
    for idx, row in clips_df.iterrows():
        s3_key = row['s3_key']
        phrase = row['phrase']
        speaker_id = row['speaker_id']
        output_name = f"{phrase}_{speaker_id}_{idx:06d}"
        
        try:
            # Load video
            cap = cv2.VideoCapture(s3_key)
            if not cap.isOpened():
                continue
            
            # Extract frames
            frames = []
            while len(frames) < 16:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            cap.release()
            
            if len(frames) == 0:
                continue
            
            # Process with strict grid approach
            processed_frames = []
            for frame in frames:
                # Apply 3x2 grid - top-middle cell
                h, w = frame.shape[:2]
                col_width = w // 3
                row_height = h // 2
                
                x1 = col_width
                x2 = 2 * col_width
                y1 = 0
                y2 = row_height
                
                # Extract and resize
                crop = frame[y1:y2, x1:x2]
                if crop.size > 0:
                    resized_crop = cv2.resize(crop, (96, 96))
                    processed_frames.append(resized_crop)
            
            if processed_frames:
                processed_frames_dict[output_name] = processed_frames
                results.append({
                    'clip_id': output_name,
                    'phrase': phrase,
                    'method': 'strict_grid',
                    'success': True
                })
        
        except Exception as e:
            print(f"Error processing {output_name}: {e}")
    
    return results, processed_frames_dict


def process_with_mediapipe_landmarks(clips_df):
    """Process clips with MediaPipe landmarks"""
    print("📊 Processing with MediaPipe landmarks...")
    
    # Load existing MediaPipe results
    mediapipe_clips_dir = 'data_processed_mediapipe/clips'
    processed_frames_dict = {}
    results = []
    
    for idx, row in clips_df.iterrows():
        phrase = row['phrase']
        speaker_id = row['speaker_id']
        output_name = f"{phrase}_{speaker_id}_{idx:06d}"
        
        clip_path = os.path.join(mediapipe_clips_dir, f'{output_name}.npy')
        
        if os.path.exists(clip_path):
            try:
                clip_array = np.load(clip_path)
                processed_frames_dict[output_name] = clip_array
                results.append({
                    'clip_id': output_name,
                    'phrase': phrase,
                    'method': 'mediapipe_landmarks',
                    'success': True
                })
            except Exception as e:
                print(f"Error loading {clip_path}: {e}")
    
    return results, processed_frames_dict


def create_side_by_side_comparison(strict_frames, mediapipe_frames, output_file="qc_side_by_side_strict_vs_landmarks.png"):
    """Create side-by-side comparison visualization"""
    
    # Find common clips
    common_clips = set(strict_frames.keys()) & set(mediapipe_frames.keys())
    common_clips = list(common_clips)[:20]  # Limit to 20 clips
    
    if len(common_clips) == 0:
        print("❌ No common clips found for comparison!")
        return
    
    print(f"📊 Creating side-by-side comparison for {len(common_clips)} clips...")
    
    # Create figure: 20 rows × 2 columns (strict vs landmarks)
    fig, axes = plt.subplots(len(common_clips), 2, figsize=(12, 4*len(common_clips)))
    fig.suptitle('QC: Side-by-Side Comparison\nLEFT: Strict Grid Heuristic | RIGHT: MediaPipe Landmarks', 
                 fontsize=16, fontweight='bold')
    
    # Handle single clip case
    if len(common_clips) == 1:
        axes = axes.reshape(1, 2)
    
    for clip_idx, clip_id in enumerate(common_clips):
        # Get first frame from each method
        strict_frame = strict_frames[clip_id][0]  # First frame
        mediapipe_frame = mediapipe_frames[clip_id][0]  # First frame
        
        # Extract phrase from clip_id
        phrase = clip_id.split('_')[0] if '_' in clip_id else clip_id
        
        # Left: Strict grid with box overlay
        ax_left = axes[clip_idx, 0]
        strict_with_overlay = strict_frame.copy()
        
        # Draw grid overlay (3x2 grid, top-middle highlighted)
        h, w = strict_with_overlay.shape[:2]
        col_width = w // 3
        row_height = h // 2
        
        # Draw grid lines
        cv2.line(strict_with_overlay, (col_width, 0), (col_width, h), (255, 255, 255), 1)
        cv2.line(strict_with_overlay, (2*col_width, 0), (2*col_width, h), (255, 255, 255), 1)
        cv2.line(strict_with_overlay, (0, row_height), (w, row_height), (255, 255, 255), 1)
        
        # Highlight top-middle cell
        cv2.rectangle(strict_with_overlay, (col_width, 0), (2*col_width, row_height), (0, 255, 0), 2)
        
        ax_left.imshow(cv2.cvtColor(strict_with_overlay, cv2.COLOR_BGR2RGB))
        ax_left.set_title(f'{clip_id}\nStrict Grid (Top-Middle)', fontsize=8)
        ax_left.axis('off')
        
        # Right: MediaPipe landmarks with lip polygon overlay
        ax_right = axes[clip_idx, 1]
        mediapipe_with_overlay = mediapipe_frame.copy()
        
        # Draw estimated lip region (since we don't have access to original landmarks here)
        # Draw a green rectangle in the center where lips should be
        h, w = mediapipe_with_overlay.shape[:2]
        center_x, center_y = w // 2, int(h * 0.6)
        lip_width = int(w * 0.4)
        lip_height = int(lip_width * 0.3)
        
        x1 = center_x - lip_width // 2
        x2 = center_x + lip_width // 2
        y1 = center_y - lip_height // 2
        y2 = center_y + lip_height // 2
        
        cv2.rectangle(mediapipe_with_overlay, (x1, y1), (x2, y2), (0, 255, 0), 2)
        cv2.circle(mediapipe_with_overlay, (center_x, center_y), 3, (0, 255, 0), -1)
        
        ax_right.imshow(cv2.cvtColor(mediapipe_with_overlay, cv2.COLOR_BGR2RGB))
        ax_right.set_title(f'{clip_id}\nMediaPipe Landmarks', fontsize=8)
        ax_right.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"💾 Side-by-side comparison saved to: {output_file}")


def create_coverage_histogram_and_analysis():
    """Create updated coverage histogram and analysis"""
    
    # Load MediaPipe results
    csv_path = 'data_processed_mediapipe/quality_logs/quality_scores.csv'
    if not os.path.exists(csv_path):
        print("❌ MediaPipe quality CSV not found!")
        return
    
    df = pd.read_csv(csv_path)
    
    # Create comprehensive analysis
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('MediaPipe Landmark-Anchored Preprocessing Analysis', fontsize=16, fontweight='bold')
    
    # Coverage histogram
    ax1 = axes[0, 0]
    coverage_values = df['coverage_after'].values
    coverage_values = coverage_values[~np.isnan(coverage_values)]  # Remove NaN values
    
    if len(coverage_values) > 0:
        ax1.hist(coverage_values, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(0.30, color='red', linestyle='--', label='Min Accept (30%)')
        ax1.axvline(0.75, color='red', linestyle='--', label='Max Accept (75%)')
        ax1.set_xlabel('Lip Coverage After Adjustment')
        ax1.set_ylabel('Number of Clips')
        ax1.set_title('Distribution of Lip Coverage Values')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
    
    # Landmark detection rate by phrase
    ax2 = axes[0, 1]
    phrase_landmarks = df.groupby('phrase')['frames_with_landmarks_pct'].mean().sort_values()
    phrase_landmarks.plot(kind='bar', ax=ax2, color='lightcoral')
    ax2.set_xlabel('Phrase')
    ax2.set_ylabel('Avg Landmark Detection %')
    ax2.set_title('Landmark Detection Rate by Phrase')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)
    
    # Mouth width distribution
    ax3 = axes[1, 0]
    mouth_widths = df['mouth_width_px'].values
    mouth_widths = mouth_widths[~np.isnan(mouth_widths) & (mouth_widths > 0)]
    
    if len(mouth_widths) > 0:
        ax3.hist(mouth_widths, bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
        ax3.set_xlabel('Mouth Width (pixels)')
        ax3.set_ylabel('Number of Clips')
        ax3.set_title('Distribution of Detected Mouth Widths')
        ax3.grid(True, alpha=0.3)
    
    # Summary statistics
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    total_clips = len(df)
    valid_clips = df['valid'].sum()
    avg_landmarks_pct = df['frames_with_landmarks_pct'].mean()
    avg_coverage = df['coverage_after'].mean() if not df['coverage_after'].isna().all() else 0
    
    stats_text = f"""
    Processing Statistics:
    
    Total Clips: {total_clips}
    Valid Clips: {valid_clips} ({valid_clips/total_clips*100:.1f}%)
    
    Landmark Detection:
    Avg Detection Rate: {avg_landmarks_pct:.1f}%
    Clips with >90% landmarks: {(df['frames_with_landmarks_pct'] >= 90).sum()}
    
    Coverage Analysis:
    Avg Coverage: {avg_coverage:.3f}
    In range [0.30-0.75]: {((coverage_values >= 0.30) & (coverage_values <= 0.75)).sum() if len(coverage_values) > 0 else 0}
    Below 0.30: {(coverage_values < 0.30).sum() if len(coverage_values) > 0 else 0}
    Above 0.75: {(coverage_values > 0.75).sum() if len(coverage_values) > 0 else 0}
    
    Recommendations:
    - Adjust target_mouth_width_ratio
    - Improve face detection robustness
    - Consider video quality filtering
    """
    
    ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, 
             fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig('qc_mediapipe_analysis.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 MediaPipe analysis saved to: qc_mediapipe_analysis.png")


def main():
    """Main QC comparison function"""
    print("🎯 QC Side-by-Side Comparison: Strict Grid vs MediaPipe Landmarks")
    print("=" * 70)
    
    # Load the same 20 clips
    clips_df = load_same_20_clips()
    if clips_df is None:
        return False
    
    print(f"📊 Comparing {len(clips_df)} clips...")
    
    # Process with both methods
    strict_results, strict_frames = process_with_strict_grid(clips_df)
    mediapipe_results, mediapipe_frames = process_with_mediapipe_landmarks(clips_df)
    
    print(f"✅ Strict grid: {len(strict_results)} clips processed")
    print(f"✅ MediaPipe: {len(mediapipe_results)} clips processed")
    
    # Create side-by-side comparison
    create_side_by_side_comparison(strict_frames, mediapipe_frames)
    
    # Create updated analysis
    create_coverage_histogram_and_analysis()
    
    print("\n✅ QC comparison completed!")
    print("📁 Generated files:")
    print("  - qc_side_by_side_strict_vs_landmarks.png")
    print("  - qc_mediapipe_analysis.png")
    
    return True


if __name__ == "__main__":
    main()
