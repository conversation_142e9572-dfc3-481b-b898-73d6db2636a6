#!/usr/bin/env python3
"""
True MediaPipe landmark-anchored lip cropping preprocessor.
Runs in Python 3.11 environment with MediaPipe support.
"""

import os
import sys
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import glob
import random
import yaml
from pathlib import Path
from tqdm import tqdm

# MediaPipe imports
import mediapipe as mp

# YOLO imports
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False


class MediaPipeLandmarkPreprocessor:
    """True MediaPipe landmark-anchored, scale-controlled lip cropping"""
    
    def __init__(self, config_path='configs/training.yaml', processed_data_dir='data_processed_mediapipe'):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.preprocessing_config = self.config['data']
        self.img_size = self.preprocessing_config['img_size']
        self.processed_data_dir = processed_data_dir
        
        # Create output directories
        os.makedirs(processed_data_dir, exist_ok=True)
        os.makedirs(os.path.join(processed_data_dir, 'clips'), exist_ok=True)
        os.makedirs(os.path.join(processed_data_dir, 'quality_logs'), exist_ok=True)
        
        # Initialize MediaPipe FaceMesh
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # Initialize YOLO if available
        self.yolo = None
        if YOLO_AVAILABLE:
            try:
                yolo_path = 'models/yolov8n.pt'
                if os.path.exists(yolo_path):
                    self.yolo = YOLO(yolo_path)
                    print("✅ YOLO model loaded")
            except Exception as e:
                print(f"⚠️ YOLO loading failed: {e}")
        
        # MediaPipe landmark indices
        # Outer lip contour for coverage calculation
        self.outer_lip_indices = [61, 146, 91, 181, 84, 17, 314, 405, 321, 375, 291]
        # Inner lip contour for refined coverage
        self.inner_lip_indices = [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308]
        # Mouth corners for width calculation
        self.left_mouth_corner = 61
        self.right_mouth_corner = 291
        
        # Quality tracking
        self.quality_records = []
        self.coverage_values = []
        self.failure_cases = []
        
        print("✅ MediaPipe FaceMesh landmark preprocessor initialized")
    
    def extract_lip_landmarks_and_metrics(self, frame):
        """Extract true MediaPipe lip landmarks, mouth width, and centroid"""
        try:
            # Apply histogram equalization for better face detection
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            equalized = cv2.equalizeHist(gray)
            enhanced_frame = cv2.cvtColor(equalized, cv2.COLOR_GRAY2BGR)

            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(enhanced_frame, cv2.COLOR_BGR2RGB)

            # Process with MediaPipe
            results = self.face_mesh.process(rgb_frame)
            
            if not results.multi_face_landmarks:
                return None, None, None
            
            face_landmarks = results.multi_face_landmarks[0]
            h, w = rgb_frame.shape[:2]
            
            # Extract outer lip points
            outer_lip_points = []
            for idx in self.outer_lip_indices:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = landmark.x * w
                    y = landmark.y * h
                    outer_lip_points.append((x, y))
            
            if len(outer_lip_points) < 4:
                return None, None, None
            
            # Calculate mouth width (distance between mouth corners)
            if (self.left_mouth_corner < len(face_landmarks.landmark) and 
                self.right_mouth_corner < len(face_landmarks.landmark)):
                
                left_corner = face_landmarks.landmark[self.left_mouth_corner]
                right_corner = face_landmarks.landmark[self.right_mouth_corner]
                
                left_x, left_y = left_corner.x * w, left_corner.y * h
                right_x, right_y = right_corner.x * w, right_corner.y * h
                
                mouth_width = np.sqrt((right_x - left_x)**2 + (right_y - left_y)**2)
            else:
                # Fallback: bounding box width of outer lip points
                outer_points_array = np.array(outer_lip_points)
                mouth_width = np.max(outer_points_array[:, 0]) - np.min(outer_points_array[:, 0])
            
            # Calculate lip centroid from outer lip points
            lip_centroid = np.mean(outer_lip_points, axis=0)
            
            return face_landmarks, mouth_width, lip_centroid
            
        except Exception as e:
            return None, None, None
    
    def apply_temporal_smoothing(self, values, window_size):
        """Apply median smoothing across frames"""
        smoothed = []
        valid_values = [v for v in values if v is not None]
        
        if not valid_values:
            return values
        
        median_value = np.median(valid_values, axis=0) if len(np.array(valid_values).shape) > 1 else np.median(valid_values)
        
        for i, value in enumerate(values):
            if value is not None:
                # Use median of surrounding frames
                start_idx = max(0, i - window_size // 2)
                end_idx = min(len(values), i + window_size // 2 + 1)
                window_values = [v for v in values[start_idx:end_idx] if v is not None]
                
                if window_values:
                    if len(np.array(window_values).shape) > 1:
                        smoothed.append(np.median(window_values, axis=0))
                    else:
                        smoothed.append(np.median(window_values))
                else:
                    smoothed.append(median_value)
            else:
                # Use last valid value or median
                if smoothed and smoothed[-1] is not None:
                    smoothed.append(smoothed[-1])
                else:
                    smoothed.append(median_value)
        
        return smoothed
    
    def calculate_lip_coverage(self, landmarks, crop_bbox, frame_shape):
        """Calculate true lip coverage using MediaPipe polygon"""
        if landmarks is None:
            return 0.0
        
        try:
            h, w = frame_shape[:2]
            x1, y1, x2, y2 = crop_bbox
            crop_w = x2 - x1
            crop_h = y2 - y1
            
            if crop_w <= 0 or crop_h <= 0:
                return 0.0
            
            # Extract outer lip points in original frame coordinates
            outer_lip_points = []
            for idx in self.outer_lip_indices:
                if idx < len(landmarks.landmark):
                    landmark = landmarks.landmark[idx]
                    x = landmark.x * w
                    y = landmark.y * h
                    outer_lip_points.append((x, y))
            
            if len(outer_lip_points) < 3:
                return 0.0
            
            # Convert to crop coordinates and scale to 96x96
            crop_lip_points = []
            for x, y in outer_lip_points:
                if x1 <= x <= x2 and y1 <= y <= y2:
                    crop_x = (x - x1) / crop_w * self.img_size
                    crop_y = (y - y1) / crop_h * self.img_size
                    crop_lip_points.append((int(crop_x), int(crop_y)))
            
            if len(crop_lip_points) < 3:
                return 0.0
            
            # Create convex hull and calculate area
            hull_points = cv2.convexHull(np.array(crop_lip_points, dtype=np.int32))
            outer_lip_area = cv2.contourArea(hull_points)
            
            # Extract inner lip points for more accurate coverage
            inner_lip_points = []
            for idx in self.inner_lip_indices:
                if idx < len(landmarks.landmark):
                    landmark = landmarks.landmark[idx]
                    x = landmark.x * w
                    y = landmark.y * h
                    if x1 <= x <= x2 and y1 <= y <= y2:
                        crop_x = (x - x1) / crop_w * self.img_size
                        crop_y = (y - y1) / crop_h * self.img_size
                        inner_lip_points.append((int(crop_x), int(crop_y)))
            
            # Subtract inner lip area if available
            inner_lip_area = 0
            if len(inner_lip_points) >= 3:
                inner_hull = cv2.convexHull(np.array(inner_lip_points, dtype=np.int32))
                inner_lip_area = cv2.contourArea(inner_hull)
            
            # Net lip area (outer - inner)
            net_lip_area = max(0, outer_lip_area - inner_lip_area)
            
            # Total crop area
            crop_area = self.img_size * self.img_size
            
            # Coverage ratio
            coverage = net_lip_area / crop_area
            
            return min(1.0, max(0.0, coverage))
            
        except Exception as e:
            return 0.0
    
    def create_landmark_anchored_crop(self, frame, landmarks, mouth_width, lip_centroid):
        """Create landmark-anchored crop with auto-adjustment based on true coverage"""
        h, w = frame.shape[:2]
        
        # Get config parameters (with tightened defaults for lips filling frame)
        target_ratio = self.preprocessing_config.get('target_mouth_width_ratio', 0.90)  # mouth ≈ 90% of crop width
        aspect_ratio_y = self.preprocessing_config.get('aspect_ratio_y', 0.75)          # slightly shorter height around lips
        pad_pct = self.preprocessing_config.get('pad_pct', 0.02)                       # very small padding
        coverage_min = self.preprocessing_config.get('coverage_accept_min', 0.35)      # accept 0.35–0.70
        coverage_max = self.preprocessing_config.get('coverage_accept_max', 0.70)
        target_coverage = self.preprocessing_config.get('target_coverage', 0.45)       # target coverage for auto-adjust
        max_iterations = self.preprocessing_config.get('max_adjust_iterations', 3)     # up to 3 iterations for auto-adjust
        
        # Calculate initial crop dimensions
        crop_width = mouth_width / target_ratio
        crop_height = crop_width * aspect_ratio_y
        
        # Center on lip centroid
        center_x, center_y = lip_centroid
        
        # Initial crop bounds
        x1 = int(center_x - crop_width / 2)
        x2 = int(center_x + crop_width / 2)
        y1 = int(center_y - crop_height / 2)
        y2 = int(center_y + crop_height / 2)
        
        # Add padding
        pad_x = int(crop_width * pad_pct)
        pad_y = int(crop_height * pad_pct)
        
        x1_pad = max(0, x1 - pad_x)
        x2_pad = min(w, x2 + pad_x)
        y1_pad = max(0, y1 - pad_y)
        y2_pad = min(h, y2 + pad_y)
        
        # Extract initial crop
        initial_crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]
        
        if initial_crop.size == 0:
            return None, None
        
        # Calculate initial coverage
        coverage_before = self.calculate_lip_coverage(landmarks, (x1_pad, y1_pad, x2_pad, y2_pad), frame.shape)
        
        # Coverage-driven auto-adjustment (up to max_iterations)
        final_crop_bbox = (x1_pad, y1_pad, x2_pad, y2_pad)
        coverage_after = coverage_before
        adjustments_made = 0

        for attempt in range(max_iterations):
            if coverage_min <= coverage_after <= coverage_max:
                break  # Coverage is acceptable

            # Adjust crop width based on coverage using sqrt formula
            if coverage_after < coverage_min:
                # Shrink box: crop_width *= sqrt(current_coverage/target_coverage)
                adjustment_factor = np.sqrt(coverage_after / target_coverage)
                crop_width *= adjustment_factor
            elif coverage_after > coverage_max:
                # Loosen box: crop_width /= sqrt(current_coverage/target_coverage)
                adjustment_factor = np.sqrt(coverage_after / target_coverage)
                crop_width /= adjustment_factor
            
            # Recalculate crop bounds
            crop_height = crop_width * aspect_ratio_y
            
            x1 = int(center_x - crop_width / 2)
            x2 = int(center_x + crop_width / 2)
            y1 = int(center_y - crop_height / 2)
            y2 = int(center_y + crop_height / 2)
            
            # Add padding
            pad_x = int(crop_width * pad_pct)
            pad_y = int(crop_height * pad_pct)
            
            x1_pad = max(0, x1 - pad_x)
            x2_pad = min(w, x2 + pad_x)
            y1_pad = max(0, y1 - pad_y)
            y2_pad = min(h, y2 + pad_y)
            
            # Extract adjusted crop
            adjusted_crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]
            
            if adjusted_crop.size > 0:
                final_crop_bbox = (x1_pad, y1_pad, x2_pad, y2_pad)
                coverage_after = self.calculate_lip_coverage(landmarks, final_crop_bbox, frame.shape)
                adjustments_made += 1
            else:
                break
        
        # Extract final crop and resize
        x1_pad, y1_pad, x2_pad, y2_pad = final_crop_bbox
        final_crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]
        resized_crop = cv2.resize(final_crop, (self.img_size, self.img_size))
        
        # Create metrics
        metrics = {
            'mouth_width_px': mouth_width,
            'crop_w_px': x2_pad - x1_pad,
            'target_ratio': target_ratio,
            'coverage_before': coverage_before,
            'coverage_after': coverage_after,
            'n_adjust': adjustments_made,
            'valid': coverage_min <= coverage_after <= coverage_max
        }
        
        return resized_crop, metrics

    def fallback_crop_with_yolo_grid(self, frame):
        """Fallback: YOLO face → top-middle grid → try MediaPipe again"""
        h, w = frame.shape[:2]

        # Try YOLO face detection
        face_bbox = None
        if self.yolo is not None:
            try:
                results = self.yolo(frame, conf=0.5, classes=[0], verbose=False)
                if results and results[0].boxes:
                    boxes = results[0].boxes.xyxy.cpu().numpy()
                    if len(boxes) > 0:
                        box = boxes[0]  # Take first detection
                        x1, y1, x2, y2 = map(int, box)
                        face_bbox = (x1, y1, x2, y2)
            except Exception as e:
                pass

        if face_bbox is not None:
            # Apply 3x2 grid within face bbox - top-middle cell
            fx1, fy1, fx2, fy2 = face_bbox
            face_w = fx2 - fx1
            face_h = fy2 - fy1

            col_width = face_w // 3
            row_height = face_h // 2

            # Top-middle cell
            x1 = fx1 + col_width
            x2 = fx1 + 2 * col_width
            y1 = fy1
            y2 = fy1 + row_height

            # Ensure bounds
            x1 = max(0, min(x1, w-1))
            x2 = max(x1+1, min(x2, w))
            y1 = max(0, min(y1, h-1))
            y2 = max(y1+1, min(y2, h))

            # Extract crop
            crop = frame[y1:y2, x1:x2]

            if crop.size > 0:
                resized_crop = cv2.resize(crop, (self.img_size, self.img_size))

                # Try MediaPipe again on the cropped region
                landmarks, mouth_width, lip_centroid = self.extract_lip_landmarks_and_metrics(resized_crop)

                metrics = {
                    'mouth_width_px': mouth_width if mouth_width else 0,
                    'crop_w_px': x2 - x1,
                    'target_ratio': 0.0,
                    'coverage_before': 0.0,
                    'coverage_after': 0.0,
                    'n_adjust': 0,
                    'valid': landmarks is not None
                }

                return resized_crop, metrics

        # Final fallback: center crop
        crop_size = min(h, w) // 2
        center_h, center_w = h // 2, w // 2
        center_crop = frame[center_h-crop_size//2:center_h+crop_size//2,
                          center_w-crop_size//2:center_w+crop_size//2]

        if center_crop.size > 0:
            resized_crop = cv2.resize(center_crop, (self.img_size, self.img_size))
        else:
            resized_crop = np.zeros((self.img_size, self.img_size, 3), dtype=np.uint8)

        metrics = {
            'mouth_width_px': 0,
            'crop_w_px': crop_size,
            'target_ratio': 0.0,
            'coverage_before': 0.0,
            'coverage_after': 0.0,
            'n_adjust': 0,
            'valid': False
        }

        return resized_crop, metrics

    def process_single_video(self, video_path, output_name):
        """Process a single video with true MediaPipe landmark-anchored approach"""
        try:
            # Load video
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return {'success': False, 'error': 'video_load_failed'}

            # Extract frames
            frames = []
            while len(frames) < self.preprocessing_config['frames_per_clip']:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)

            cap.release()

            if len(frames) == 0:
                return {'success': False, 'error': 'no_frames_extracted'}

            # Step 1: Extract landmarks for all frames
            raw_landmarks = []
            mouth_widths = []
            lip_centroids = []

            for frame in frames:
                landmarks, mouth_width, lip_centroid = self.extract_lip_landmarks_and_metrics(frame)
                raw_landmarks.append(landmarks)
                mouth_widths.append(mouth_width if mouth_width is not None else None)
                lip_centroids.append(lip_centroid if lip_centroid is not None else None)

            # Step 2: Apply temporal smoothing
            smooth_window = self.preprocessing_config['smooth_window']
            smoothed_mouth_widths = self.apply_temporal_smoothing(mouth_widths, smooth_window)
            smoothed_centroids = self.apply_temporal_smoothing(lip_centroids, smooth_window)

            # Step 3: Process each frame
            processed_frames = []
            frame_metrics = []
            frames_with_landmarks = 0

            for i, frame in enumerate(frames):
                landmarks = raw_landmarks[i]
                mouth_width = smoothed_mouth_widths[i]
                lip_centroid = smoothed_centroids[i]

                if landmarks is not None and mouth_width is not None and lip_centroid is not None:
                    # Primary path: landmark-anchored cropping
                    crop, metrics = self.create_landmark_anchored_crop(frame, landmarks, mouth_width, lip_centroid)

                    if crop is not None:
                        processed_frames.append(crop)
                        frame_metrics.append(metrics)
                        frames_with_landmarks += 1

                        # Store coverage for analysis
                        self.coverage_values.append(metrics['coverage_after'])
                    else:
                        # Fallback
                        crop, metrics = self.fallback_crop_with_yolo_grid(frame)
                        processed_frames.append(crop)
                        frame_metrics.append(metrics)
                else:
                    # Fallback path
                    crop, metrics = self.fallback_crop_with_yolo_grid(frame)
                    processed_frames.append(crop)
                    frame_metrics.append(metrics)

            # Step 4: Calculate clip-level metrics
            frames_with_landmarks_pct = (frames_with_landmarks / len(frames) * 100) if frames else 0
            avg_coverage_after = np.mean([m['coverage_after'] for m in frame_metrics])
            is_valid = frames_with_landmarks_pct >= self.preprocessing_config['min_frames_with_landmarks_pct']

            # Save processed clip
            output_path = os.path.join(self.processed_data_dir, 'clips', f'{output_name}.npy')
            clip_array = np.array(processed_frames)
            np.save(output_path, clip_array)

            # Store failure cases for analysis
            if not is_valid or avg_coverage_after < self.preprocessing_config['coverage_accept_min']:
                self.failure_cases.append({
                    'clip_id': output_name,
                    'frames_with_landmarks_pct': frames_with_landmarks_pct,
                    'avg_coverage_after': avg_coverage_after,
                    'sample_frame': processed_frames[0] if processed_frames else None
                })

            return {
                'success': True,
                'output_path': output_path,
                'frames_with_landmarks_pct': frames_with_landmarks_pct,
                'avg_coverage_after': avg_coverage_after,
                'valid': is_valid,
                'frame_metrics': frame_metrics
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}
