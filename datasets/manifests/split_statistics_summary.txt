
CLEAN DATASET STATISTICS
==================================================
Total videos: 1,677
Train: 1,173 (69.9%)
Val: 252 (15.0%)
Test: 252 (15.0%)

PHRASE DISTRIBUTION:
--------------------
Train:
  doctor: 167
  glasses: 186
  help: 136
  i_need_to_move: 170
  my_mouth_is_dry: 156
  phone: 168
  pillow: 190
Val:
  doctor: 36
  glasses: 40
  help: 29
  i_need_to_move: 36
  my_mouth_is_dry: 34
  phone: 36
  pillow: 41
Test:
  doctor: 36
  glasses: 40
  help: 29
  i_need_to_move: 36
  my_mouth_is_dry: 34
  phone: 36
  pillow: 41

DEMOGRAPHIC DISTRIBUTION:
-------------------------
Age Groups:
  Train: {'18to39': np.int64(346), '40to64': np.int64(261), '65plus': np.int64(566)}
  Val: {'18to39': np.int64(78), '40to64': np.int64(58), '65plus': np.int64(116)}
  Test: {'18to39': np.int64(72), '40to64': np.int64(61), '65plus': np.int64(119)}
Gender:
  Train: {'female': np.int64(855), 'male': np.int64(318)}
  Val: {'female': np.int64(176), 'male': np.int64(76)}
  Test: {'female': np.int64(186), 'male': np.int64(66)}
Ethnicity:
  Train: {'aboriginal': np.int64(32), 'asian': np.int64(38), 'caucasian': np.int64(952), 'not_specified': np.int64(151)}
  Val: {'aboriginal': np.int64(6), 'asian': np.int64(4), 'caucasian': np.int64(201), 'not_specified': np.int64(41)}
  Test: {'aboriginal': np.int64(3), 'asian': np.int64(17), 'caucasian': np.int64(204), 'not_specified': np.int64(28)}

CRITICAL DEMOGRAPHIC VERIFICATION:
-----------------------------------
Male 18-39 in training set: 192 samples
✅ REQUIREMENT MET: Male 18-39 demographic present in training


FLAGGED DATASET STATISTICS
==================================================
Total videos: 599
Train: 419 (69.9%)
Val: 90 (15.0%)
Test: 90 (15.0%)

PHRASE DISTRIBUTION:
--------------------
Train:
  doctor: 68
  glasses: 44
  help: 105
  i_need_to_move: 5
  my_mouth_is_dry: 7
  phone: 112
  pillow: 78
Val:
  doctor: 15
  glasses: 9
  help: 22
  i_need_to_move: 1
  my_mouth_is_dry: 2
  phone: 24
  pillow: 17
Test:
  doctor: 15
  glasses: 9
  help: 22
  i_need_to_move: 1
  my_mouth_is_dry: 2
  phone: 24
  pillow: 17

DEMOGRAPHIC DISTRIBUTION:
-------------------------
Age Groups:
  Train: {'18to39': np.int64(92), '40to64': np.int64(107), '65plus': np.int64(220)}
  Val: {'18to39': np.int64(22), '40to64': np.int64(15), '65plus': np.int64(53)}
  Test: {'18to39': np.int64(25), '40to64': np.int64(16), '65plus': np.int64(49)}
Gender:
  Train: {'female': np.int64(290), 'male': np.int64(129)}
  Val: {'female': np.int64(66), 'male': np.int64(24)}
  Test: {'female': np.int64(58), 'male': np.int64(32)}
Ethnicity:
  Train: {'aboriginal': np.int64(3), 'asian': np.int64(63), 'caucasian': np.int64(316), 'not_specified': np.int64(37)}
  Val: {'aboriginal': np.int64(1), 'asian': np.int64(18), 'caucasian': np.int64(65), 'not_specified': np.int64(6)}
  Test: {'asian': np.int64(13), 'caucasian': np.int64(65), 'not_specified': np.int64(12)}

CRITICAL DEMOGRAPHIC VERIFICATION:
-----------------------------------
Male 18-39 in training set: 53 samples
✅ REQUIREMENT MET: Male 18-39 demographic present in training