DATASET BALANCING ANALYSIS REPORT
==================================================
Generated: 2025-09-08 15:35:22

CURRENT DISTRIBUTION SUMMARY
------------------------------

TRAIN Split:
  Current total: 1,173
  Target per phrase: 190
  Phrase distribution:
    doctor: 167 → 190 (+23)
    glasses: 186 → 190 (+4)
    help: 136 → 190 (+54)
    i_need_to_move: 170 → 190 (+20)
    my_mouth_is_dry: 156 → 190 (+34)
    phone: 168 → 190 (+22)
    pillow: 190 → 190 (+0)

VAL Split:
  Current total: 252
  Target per phrase: 41
  Phrase distribution:
    doctor: 36 → 41 (+5)
    glasses: 40 → 41 (+1)
    help: 29 → 41 (+12)
    i_need_to_move: 36 → 41 (+5)
    my_mouth_is_dry: 34 → 41 (+7)
    phone: 36 → 41 (+5)
    pillow: 41 → 41 (+0)

TEST Split:
  Current total: 252
  Target per phrase: 41
  Phrase distribution:
    doctor: 36 → 41 (+5)
    glasses: 40 → 41 (+1)
    help: 29 → 41 (+12)
    i_need_to_move: 36 → 41 (+5)
    my_mouth_is_dry: 34 → 41 (+7)
    phone: 36 → 41 (+5)
    pillow: 41 → 41 (+0)

OVERALL SUMMARY:
  Original dataset: 1,677 videos
  Balanced dataset: 1,904 videos
  Total augmentations needed: 227
  Expansion factor: 1.14x


AUGMENTATION STRATEGY
-------------------------

TRAIN Split Strategy:
  doctor: +23 augmentations using 1 techniques
    - brightness_adj: 23 augmentations
  glasses: +4 augmentations using 1 techniques
    - brightness_adj: 4 augmentations
  help: +54 augmentations using 1 techniques
    - brightness_adj: 54 augmentations
  i_need_to_move: +20 augmentations using 1 techniques
    - brightness_adj: 20 augmentations
  my_mouth_is_dry: +34 augmentations using 1 techniques
    - brightness_adj: 34 augmentations
  phone: +22 augmentations using 1 techniques
    - brightness_adj: 22 augmentations
  pillow: No augmentation needed

VAL Split Strategy:
  doctor: +5 augmentations using 1 techniques
    - brightness_adj: 5 augmentations
  glasses: +1 augmentations using 1 techniques
    - brightness_adj: 1 augmentations
  help: +12 augmentations using 1 techniques
    - brightness_adj: 12 augmentations
  i_need_to_move: +5 augmentations using 1 techniques
    - brightness_adj: 5 augmentations
  my_mouth_is_dry: +7 augmentations using 1 techniques
    - brightness_adj: 7 augmentations
  phone: +5 augmentations using 1 techniques
    - brightness_adj: 5 augmentations
  pillow: No augmentation needed

TEST Split Strategy:
  doctor: +5 augmentations using 1 techniques
    - brightness_adj: 5 augmentations
  glasses: +1 augmentations using 1 techniques
    - brightness_adj: 1 augmentations
  help: +12 augmentations using 1 techniques
    - brightness_adj: 12 augmentations
  i_need_to_move: +5 augmentations using 1 techniques
    - brightness_adj: 5 augmentations
  my_mouth_is_dry: +7 augmentations using 1 techniques
    - brightness_adj: 7 augmentations
  phone: +5 augmentations using 1 techniques
    - brightness_adj: 5 augmentations
  pillow: No augmentation needed


DEMOGRAPHIC PRESERVATION STRATEGY
-----------------------------------
Augmentation will maintain proportional demographic representation:
- Age groups: Proportional to original distribution
- Gender: Proportional to original distribution
- Ethnicity: Proportional to original distribution
- Combinations: All existing combinations preserved


AUGMENTATION TECHNIQUES
-------------------------
1. brightness_adj: ±10-15% brightness adjustment
2. temporal_jitter: ±2-3 frames at start/end
3. minor_zoom: ±5-8% zoom factor
4. gaussian_blur: σ=0.5-1.0 blur
5. horizontal_flip: Mirror flip (if appropriate)