#!/usr/bin/env python3
"""
Run Stage-3 Segmentation on the full dataset
"""

import subprocess
import sys

def main():
    print("🚀 Starting Stage-3 Segmentation on full dataset...")
    
    cmd = [
        sys.executable, "-u", "scripts/segment_yolo_sam.py",
        "--input_dir", "data/stage1M_motion_refined_full",
        "--output_dir", "data/stage3_segmented", 
        "--gallery", "reports/stage3_gallery.html",
        "--summary", "reports/stage3_summary.csv",
        "--manifest", "data/manifests/stage3_manifest.csv",
        "--yolo_model", "yolov8n.pt",
        "--sam_model", "vit_b",
        "--size", "96",
        "--upsample", "2.0", 
        "--motion_percentile", "95",
        "--min_mask_coverage", "0.015",
        "--max_mask_coverage", "0.25",
        "--min_frames", "12",
        "--stride", "2",
        "--workers", "4"
    ]
    
    print("Command:", " ".join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ Stage-3 segmentation completed successfully!")
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"❌ Stage-3 segmentation failed with return code {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
        return 1

if __name__ == '__main__':
    exit(main())
