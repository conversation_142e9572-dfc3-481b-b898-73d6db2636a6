#!/usr/bin/env python3
"""
Trial run: Process 100 random videos with landmark-driven approach.
Generate CSV and QC preview for inspection before full preprocessing.
"""

import os
import sys
import pandas as pd
import glob
import random
from pathlib import Path

# Add src to path
sys.path.append('src')

from simple_landmark_preprocessor import SimpleLandmarkPreprocessor


def create_trial_manifest(num_samples=100):
    """
    Create a trial manifest with 100 random samples across all phrases and speakers.
    """
    print("🔍 Creating trial manifest with 100 random samples...")
    
    # Find all video files
    video_pattern = "../top 10 dataset 6.9.25/**/*.webm"
    all_videos = glob.glob(video_pattern, recursive=True)
    
    if not all_videos:
        print("❌ No video files found!")
        return None
    
    print(f"📊 Found {len(all_videos)} total video files")
    
    # Parse video information
    video_info = []
    for video_path in all_videos:
        try:
            # Extract information from path
            path_parts = video_path.split('/')
            if len(path_parts) >= 6:
                filename = path_parts[-1]
                phrase = path_parts[-6]
                
                # Parse filename for speaker info
                filename_parts = filename.replace('.webm', '').split('__')
                if len(filename_parts) >= 5:
                    speaker_id = filename_parts[1]
                    age_group = filename_parts[2]
                    gender = filename_parts[3]
                    ethnicity = filename_parts[4]
                    
                    video_info.append({
                        'video_path': video_path,
                        'phrase': phrase,
                        'speaker_id': speaker_id,
                        'age_group': age_group,
                        'gender': gender,
                        'ethnicity': ethnicity,
                        'filename': filename
                    })
        except Exception as e:
            print(f"⚠️ Error parsing {video_path}: {e}")
            continue
    
    if not video_info:
        print("❌ No valid video information extracted!")
        return None
    
    print(f"✅ Parsed {len(video_info)} valid videos")
    
    # Create DataFrame for easier sampling
    df = pd.DataFrame(video_info)
    
    # Print distribution
    print("\n📊 Dataset distribution:")
    print(f"Phrases: {df['phrase'].nunique()}")
    print(f"Speakers: {df['speaker_id'].nunique()}")
    print(f"Age groups: {df['age_group'].nunique()}")
    print(f"Genders: {df['gender'].nunique()}")
    print(f"Ethnicities: {df['ethnicity'].nunique()}")
    
    # Sample 100 videos with stratification by phrase
    sampled_videos = []
    phrases = df['phrase'].unique()
    samples_per_phrase = max(1, num_samples // len(phrases))
    
    for phrase in phrases:
        phrase_videos = df[df['phrase'] == phrase]
        n_samples = min(samples_per_phrase, len(phrase_videos))
        phrase_sample = phrase_videos.sample(n=n_samples, random_state=42)
        sampled_videos.append(phrase_sample)
    
    # Combine samples
    trial_df = pd.concat(sampled_videos, ignore_index=True)
    
    # If we have fewer than 100, sample more randomly
    if len(trial_df) < num_samples:
        remaining_videos = df[~df['video_path'].isin(trial_df['video_path'])]
        additional_samples = min(num_samples - len(trial_df), len(remaining_videos))
        if additional_samples > 0:
            additional_df = remaining_videos.sample(n=additional_samples, random_state=42)
            trial_df = pd.concat([trial_df, additional_df], ignore_index=True)
    
    # Shuffle the final selection
    trial_df = trial_df.sample(frac=1, random_state=42).reset_index(drop=True)
    
    print(f"\n✅ Selected {len(trial_df)} videos for trial run")
    print("Trial distribution by phrase:")
    for phrase, count in trial_df['phrase'].value_counts().items():
        print(f"  {phrase}: {count}")
    
    # Create manifest in the expected format
    manifest_data = []
    for idx, row in trial_df.iterrows():
        # Convert to relative path from project root
        relative_path = os.path.relpath(row['video_path'], '.')
        
        manifest_data.append({
            's3_key': relative_path,  # Use relative path as s3_key for local processing
            'phrase': row['phrase'],
            'speaker_id': row['speaker_id']
        })
    
    # Save trial manifest
    manifest_df = pd.DataFrame(manifest_data)
    manifest_path = 'trial_manifest.csv'
    manifest_df.to_csv(manifest_path, index=False)
    
    print(f"💾 Trial manifest saved to: {manifest_path}")
    
    return manifest_path


def run_trial_preprocessing():
    """
    Run preprocessing on the trial manifest.
    """
    print("\n🚀 Starting trial preprocessing with landmark-driven approach...")
    
    # Create trial manifest
    manifest_path = create_trial_manifest(100)
    if not manifest_path:
        return False
    
    # Initialize simplified preprocessor
    try:
        preprocessor = SimpleLandmarkPreprocessor(
            processed_data_dir='data_processed_trial',
            img_size=96,
            yolo_conf=0.5
        )
        
        print("✅ Preprocessor initialized successfully")
        
        # Process the trial manifest
        preprocessor.process_from_manifest(manifest_path)
        
        print("✅ Trial preprocessing completed!")
        
        # Check results
        quality_csv_path = os.path.join('data_processed_trial', 'quality_logs', 'quality_scores.csv')
        if os.path.exists(quality_csv_path):
            df = pd.read_csv(quality_csv_path)
            
            print(f"\n📊 Trial Results Summary:")
            print("=" * 50)
            print(f"Total clips processed: {len(df)}")
            
            if 'valid_crop' in df.columns:
                high_quality_count = len(df[df['valid_crop'] == True])
                print(f"High quality clips (≥90%): {high_quality_count} ({high_quality_count/len(df)*100:.1f}%)")
                print(f"Low quality clips (<90%): {len(df) - high_quality_count} ({(len(df) - high_quality_count)/len(df)*100:.1f}%)")
            
            if 'landmark_coverage_pct' in df.columns:
                avg_coverage = df['landmark_coverage_pct'].mean()
                print(f"Average landmark coverage: {avg_coverage:.1f}%")
            
            print("=" * 50)
            
            # Show phrase distribution
            if 'phrase' in df.columns:
                print("Results by phrase:")
                phrase_stats = df.groupby('phrase').agg({
                    'valid_crop': 'sum',
                    'landmark_coverage_pct': 'mean'
                }).round(1)
                print(phrase_stats)
        
        return True
        
    except Exception as e:
        print(f"❌ Trial preprocessing failed: {e}")
        return False


def main():
    """Main function"""
    print("🎯 Landmark-Driven Lip Cropping - Trial Run")
    print("=" * 60)
    print("Processing 100 random samples for quality assessment...")
    print("=" * 60)
    
    # Run trial preprocessing
    success = run_trial_preprocessing()
    
    if success:
        print("\n🎉 Trial run completed successfully!")
        print("\n📋 NEXT STEPS:")
        print("1. Check 'data_processed_trial/quality_logs/quality_scores.csv' for detailed results")
        print("2. Run QC visualization: python qc_landmark_cropping.py")
        print("3. Visually inspect 'qc_landmark_preview.png' for landmark accuracy")
        print("4. Verify that at least 10 clips show correct lip landmarks (green dots)")
        print("5. If satisfied, proceed with full dataset preprocessing")
        print("6. If not satisfied, adjust parameters and re-run trial")
        
        # Also run QC visualization automatically
        print("\n🔍 Running QC visualization...")
        try:
            import subprocess
            result = subprocess.run(['python', 'qc_landmark_cropping.py'], 
                                  capture_output=True, text=True, cwd='.')
            if result.returncode == 0:
                print("✅ QC visualization completed successfully!")
                print("📁 Check 'qc_landmark_preview.png' for visual inspection")
            else:
                print(f"⚠️ QC visualization had issues: {result.stderr}")
        except Exception as e:
            print(f"⚠️ Could not run QC visualization automatically: {e}")
            print("Please run manually: python qc_landmark_cropping.py")
    else:
        print("\n❌ Trial run failed!")
        print("Please check the error messages above and fix any issues.")
        sys.exit(1)


if __name__ == "__main__":
    main()
