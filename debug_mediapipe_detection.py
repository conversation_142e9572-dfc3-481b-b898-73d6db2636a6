#!/usr/bin/env python3
"""
Debug MediaPipe face/landmark detection issues.
Must be run in Python 3.11 environment.
"""

import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
import glob

# MediaPipe imports
import mediapipe as mp


def debug_single_video(video_path, max_frames=10):
    """Debug landmark detection on a single video"""
    print(f"\n🔍 Debugging: {os.path.basename(video_path)}")
    
    # Load video
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("❌ Could not open video")
        return
    
    # Get video info
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📊 Video info: {width}x{height}, {fps:.1f}fps, {frame_count} frames")
    
    # Try different MediaPipe configurations
    configs = [
        {
            'name': 'Default',
            'static_image_mode': False,
            'max_num_faces': 1,
            'refine_landmarks': True,
            'min_detection_confidence': 0.5,
            'min_tracking_confidence': 0.5
        },
        {
            'name': 'Relaxed',
            'static_image_mode': False,
            'max_num_faces': 1,
            'refine_landmarks': True,
            'min_detection_confidence': 0.3,
            'min_tracking_confidence': 0.3
        },
        {
            'name': 'Very Relaxed',
            'static_image_mode': False,
            'max_num_faces': 1,
            'refine_landmarks': True,
            'min_detection_confidence': 0.1,
            'min_tracking_confidence': 0.1
        },
        {
            'name': 'Static Mode',
            'static_image_mode': True,
            'max_num_faces': 1,
            'refine_landmarks': True,
            'min_detection_confidence': 0.3,
            'min_tracking_confidence': 0.3
        }
    ]
    
    # Test each configuration
    for config in configs:
        print(f"\n🧪 Testing {config['name']} configuration...")
        
        # Initialize MediaPipe with this config
        mp_face_mesh = mp.solutions.face_mesh
        face_mesh = mp_face_mesh.FaceMesh(**{k: v for k, v in config.items() if k != 'name'})
        
        # Reset video to beginning
        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        landmarks_detected = 0
        frames_tested = 0
        
        for i in range(min(max_frames, frame_count)):
            ret, frame = cap.read()
            if not ret:
                break
            
            frames_tested += 1
            
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Process with MediaPipe
            results = face_mesh.process(rgb_frame)
            
            if results.multi_face_landmarks:
                landmarks_detected += 1
                print(f"  ✅ Frame {i}: Landmarks detected ({len(results.multi_face_landmarks)} faces)")
                
                # Save first successful detection for visualization
                if landmarks_detected == 1:
                    save_debug_frame(frame, results, f"debug_frame_{config['name'].lower().replace(' ', '_')}.png")
            else:
                print(f"  ❌ Frame {i}: No landmarks detected")
        
        detection_rate = (landmarks_detected / frames_tested * 100) if frames_tested > 0 else 0
        print(f"  📊 {config['name']} detection rate: {detection_rate:.1f}% ({landmarks_detected}/{frames_tested})")
        
        face_mesh.close()
        
        # If we found landmarks, no need to test more configs
        if landmarks_detected > 0:
            print(f"✅ Found working configuration: {config['name']}")
            break
    
    cap.release()


def save_debug_frame(frame, results, filename):
    """Save frame with detected landmarks for debugging"""
    try:
        # Draw landmarks on frame
        mp_drawing = mp.solutions.drawing_utils
        mp_face_mesh = mp.solutions.face_mesh
        
        annotated_frame = frame.copy()
        
        for face_landmarks in results.multi_face_landmarks:
            # Draw face mesh
            mp_drawing.draw_landmarks(
                annotated_frame,
                face_landmarks,
                mp_face_mesh.FACEMESH_CONTOURS,
                landmark_drawing_spec=None,
                connection_drawing_spec=mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=1)
            )
            
            # Highlight lip landmarks
            h, w = annotated_frame.shape[:2]
            
            # Outer lip indices
            outer_lip_indices = [61, 146, 91, 181, 84, 17, 314, 405, 321, 375, 291]
            
            for idx in outer_lip_indices:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = int(landmark.x * w)
                    y = int(landmark.y * h)
                    cv2.circle(annotated_frame, (x, y), 3, (0, 0, 255), -1)  # Red circles for lips
        
        # Save annotated frame
        cv2.imwrite(filename, annotated_frame)
        print(f"  💾 Debug frame saved: {filename}")
        
    except Exception as e:
        print(f"  ⚠️ Could not save debug frame: {e}")


def test_yolo_face_detection():
    """Test YOLO face detection as alternative"""
    print("\n🤖 Testing YOLO face detection...")
    
    try:
        from ultralytics import YOLO
        
        yolo_path = 'models/yolov8n.pt'
        if os.path.exists(yolo_path):
            yolo = YOLO(yolo_path)
            print("✅ YOLO model loaded")
            
            # Test on a sample video
            manifest_path = 'trial_manifest_tightened.csv'
            if os.path.exists(manifest_path):
                import pandas as pd
                df = pd.read_csv(manifest_path)
                
                for idx, row in df.head(3).iterrows():
                    video_path = row['s3_key']
                    print(f"\n📹 Testing YOLO on: {os.path.basename(video_path)}")
                    
                    cap = cv2.VideoCapture(video_path)
                    if not cap.isOpened():
                        continue
                    
                    faces_detected = 0
                    frames_tested = 0
                    
                    for i in range(5):  # Test first 5 frames
                        ret, frame = cap.read()
                        if not ret:
                            break
                        
                        frames_tested += 1
                        
                        # Run YOLO detection
                        results = yolo(frame, conf=0.3, classes=[0], verbose=False)  # class 0 = person
                        
                        if results and results[0].boxes:
                            boxes = results[0].boxes.xyxy.cpu().numpy()
                            confidences = results[0].boxes.conf.cpu().numpy()
                            
                            if len(boxes) > 0:
                                faces_detected += 1
                                best_conf = np.max(confidences)
                                print(f"  ✅ Frame {i}: Face detected (conf={best_conf:.3f})")
                            else:
                                print(f"  ❌ Frame {i}: No face detected")
                        else:
                            print(f"  ❌ Frame {i}: No face detected")
                    
                    cap.release()
                    
                    detection_rate = (faces_detected / frames_tested * 100) if frames_tested > 0 else 0
                    print(f"  📊 YOLO face detection rate: {detection_rate:.1f}% ({faces_detected}/{frames_tested})")
        
        else:
            print("❌ YOLO model not found")
    
    except ImportError:
        print("❌ YOLO not available")
    except Exception as e:
        print(f"❌ YOLO error: {e}")


def analyze_video_properties():
    """Analyze video properties that might affect detection"""
    print("\n📊 Analyzing video properties...")
    
    manifest_path = 'trial_manifest_tightened.csv'
    if not os.path.exists(manifest_path):
        print("❌ Manifest not found")
        return
    
    import pandas as pd
    df = pd.read_csv(manifest_path)
    
    video_stats = []
    
    for idx, row in df.head(10).iterrows():
        video_path = row['s3_key']
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            continue
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Sample a few frames to check brightness/contrast
        brightness_values = []
        for i in range(min(5, frame_count)):
            ret, frame = cap.read()
            if ret:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                brightness_values.append(np.mean(gray))
        
        cap.release()
        
        avg_brightness = np.mean(brightness_values) if brightness_values else 0
        
        video_stats.append({
            'video': os.path.basename(video_path),
            'width': width,
            'height': height,
            'fps': fps,
            'frames': frame_count,
            'brightness': avg_brightness
        })
    
    # Print summary
    if video_stats:
        print(f"📊 Video Statistics Summary:")
        print(f"  Resolution range: {min(s['width'] for s in video_stats)}x{min(s['height'] for s in video_stats)} to {max(s['width'] for s in video_stats)}x{max(s['height'] for s in video_stats)}")
        print(f"  FPS range: {min(s['fps'] for s in video_stats):.1f} to {max(s['fps'] for s in video_stats):.1f}")
        print(f"  Frame count range: {min(s['frames'] for s in video_stats)} to {max(s['frames'] for s in video_stats)}")
        print(f"  Brightness range: {min(s['brightness'] for s in video_stats):.1f} to {max(s['brightness'] for s in video_stats):.1f}")
        
        # Check for potential issues
        low_res_videos = [s for s in video_stats if s['width'] < 480 or s['height'] < 480]
        dark_videos = [s for s in video_stats if s['brightness'] < 100]
        
        if low_res_videos:
            print(f"  ⚠️ {len(low_res_videos)} videos have low resolution (<480p)")
        if dark_videos:
            print(f"  ⚠️ {len(dark_videos)} videos are dark (brightness <100)")


def main():
    """Main debug function"""
    print("🔍 MediaPipe Landmark Detection Debug")
    print("=" * 50)
    
    # Check MediaPipe availability
    try:
        import mediapipe as mp
        print("✅ MediaPipe available")
    except ImportError:
        print("❌ MediaPipe not available! Please run in Python 3.11 environment")
        return False
    
    # Analyze video properties
    analyze_video_properties()
    
    # Test YOLO face detection
    test_yolo_face_detection()
    
    # Debug MediaPipe on sample videos
    manifest_path = 'trial_manifest_tightened.csv'
    if os.path.exists(manifest_path):
        import pandas as pd
        df = pd.read_csv(manifest_path)
        
        # Test on first few videos
        for idx, row in df.head(3).iterrows():
            video_path = row['s3_key']
            debug_single_video(video_path, max_frames=5)
    
    print("\n✅ Debug analysis completed!")
    print("📁 Check for generated debug frames: debug_frame_*.png")
    
    return True


if __name__ == "__main__":
    main()
