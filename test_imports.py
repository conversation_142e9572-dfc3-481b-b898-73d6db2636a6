#!/usr/bin/env python3
"""
Test imports for the segmentation script
"""

print("🔍 Testing imports...")

try:
    print("📦 Importing basic modules...")
    import argparse
    import cv2
    import numpy as np
    import os
    import csv
    import json
    import multiprocessing as mp
    from pathlib import Path
    from tqdm import tqdm
    from typing import List, Tuple, Optional, Dict, Any
    import torch
    from jinja2 import Template
    import base64
    import warnings
    warnings.filterwarnings('ignore')
    print("✅ Basic modules imported successfully")
    
    print("📦 Importing YOLO...")
    from ultralytics import YOLO
    print("✅ YOLO imported successfully")
    
    print("📦 Importing SAM...")
    import segment_anything as sam
    from segment_anything import SamPredictor, sam_model_registry
    print("✅ SAM imported successfully")
    
    print("🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)
