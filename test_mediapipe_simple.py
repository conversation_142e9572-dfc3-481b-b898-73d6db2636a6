#!/usr/bin/env python3
"""
Simple test to get MediaPipe working with the actual video dataset.
Must be run in Python 3.11 environment.
"""

import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
import glob

# MediaPipe imports
import mediapipe as mp


def test_mediapipe_on_working_videos():
    """Test MediaPipe with different preprocessing approaches"""
    
    # Find working videos
    video_pattern = "../top 10 dataset 6.9.25/**/*.webm"
    all_videos = glob.glob(video_pattern, recursive=True)
    
    if not all_videos:
        print("❌ No videos found")
        return
    
    # Test on first few videos
    test_videos = all_videos[:5]
    
    for video_path in test_videos:
        print(f"\n🎯 Testing: {os.path.basename(video_path)}")
        
        # Test different approaches
        approaches = [
            ("Original", lambda frame: frame),
            ("Upscaled 2x", lambda frame: cv2.resize(frame, (frame.shape[1]*2, frame.shape[0]*2))),
            ("Upscaled 3x", lambda frame: cv2.resize(frame, (frame.shape[1]*3, frame.shape[0]*3))),
            ("Enhanced Contrast", lambda frame: cv2.convertScaleAbs(frame, alpha=1.5, beta=30)),
            ("Histogram Equalized", lambda frame: cv2.cvtColor(cv2.equalizeHist(cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)), cv2.COLOR_GRAY2BGR)),
        ]
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            continue
        
        # Get first frame
        ret, frame = cap.read()
        cap.release()
        
        if not ret or frame is None:
            continue
        
        print(f"  Original frame: {frame.shape}")
        
        # Test each approach
        for approach_name, preprocess_func in approaches:
            print(f"\n  🧪 Testing {approach_name}...")
            
            try:
                # Preprocess frame
                processed_frame = preprocess_func(frame)
                print(f"    Processed frame: {processed_frame.shape}")
                
                # Initialize MediaPipe with relaxed settings
                mp_face_mesh = mp.solutions.face_mesh
                face_mesh = mp_face_mesh.FaceMesh(
                    static_image_mode=True,
                    max_num_faces=1,
                    refine_landmarks=True,
                    min_detection_confidence=0.1,
                    min_tracking_confidence=0.1
                )
                
                # Convert to RGB
                rgb_frame = cv2.cvtColor(processed_frame, cv2.COLOR_BGR2RGB)
                
                # Process with MediaPipe
                results = face_mesh.process(rgb_frame)
                
                if results.multi_face_landmarks:
                    print(f"    ✅ {approach_name}: Found {len(results.multi_face_landmarks)} face(s)")
                    
                    # Extract lip landmarks
                    face_landmarks = results.multi_face_landmarks[0]
                    h, w = rgb_frame.shape[:2]
                    
                    # Mouth corners
                    left_corner = face_landmarks.landmark[61]
                    right_corner = face_landmarks.landmark[291]
                    
                    left_x, left_y = left_corner.x * w, left_corner.y * h
                    right_x, right_y = right_corner.x * w, right_corner.y * h
                    
                    mouth_width = np.sqrt((right_x - left_x)**2 + (right_y - left_y)**2)
                    print(f"    📏 Mouth width: {mouth_width:.1f} pixels")
                    
                    # Save annotated frame
                    annotated_frame = processed_frame.copy()
                    
                    # Draw mouth corners
                    cv2.circle(annotated_frame, (int(left_x), int(left_y)), 5, (0, 255, 0), -1)
                    cv2.circle(annotated_frame, (int(right_x), int(right_y)), 5, (0, 255, 0), -1)
                    cv2.line(annotated_frame, (int(left_x), int(left_y)), (int(right_x), int(right_y)), (0, 255, 0), 2)
                    
                    # Draw outer lip contour
                    outer_lip_indices = [61, 146, 91, 181, 84, 17, 314, 405, 321, 375, 291]
                    lip_points = []
                    for idx in outer_lip_indices:
                        if idx < len(face_landmarks.landmark):
                            landmark = face_landmarks.landmark[idx]
                            x = int(landmark.x * w)
                            y = int(landmark.y * h)
                            lip_points.append((x, y))
                            cv2.circle(annotated_frame, (x, y), 2, (255, 0, 0), -1)
                    
                    # Draw lip polygon
                    if len(lip_points) > 2:
                        lip_points_array = np.array(lip_points, dtype=np.int32)
                        cv2.polylines(annotated_frame, [lip_points_array], True, (255, 0, 0), 2)
                    
                    # Save result
                    filename = f"mediapipe_success_{approach_name.lower().replace(' ', '_')}_{os.path.basename(video_path).replace('.webm', '')}.png"
                    cv2.imwrite(filename, annotated_frame)
                    print(f"    💾 Saved: {filename}")
                    
                    # This approach worked, try creating a crop
                    create_landmark_crop(processed_frame, face_landmarks, approach_name, video_path)
                    
                else:
                    print(f"    ❌ {approach_name}: No landmarks detected")
                
                face_mesh.close()
                
            except Exception as e:
                print(f"    ❌ {approach_name}: Error - {e}")
        
        # Only test first video that has some success
        break


def create_landmark_crop(frame, landmarks, approach_name, video_path):
    """Create a landmark-anchored crop"""
    try:
        h, w = frame.shape[:2]
        
        # Extract outer lip points
        outer_lip_indices = [61, 146, 91, 181, 84, 17, 314, 405, 321, 375, 291]
        lip_points = []
        for idx in outer_lip_indices:
            if idx < len(landmarks.landmark):
                landmark = landmarks.landmark[idx]
                x = landmark.x * w
                y = landmark.y * h
                lip_points.append((x, y))
        
        if len(lip_points) < 3:
            return
        
        # Calculate lip centroid
        lip_centroid = np.mean(lip_points, axis=0)
        
        # Calculate mouth width
        left_corner = landmarks.landmark[61]
        right_corner = landmarks.landmark[291]
        left_x, left_y = left_corner.x * w, left_corner.y * h
        right_x, right_y = right_corner.x * w, right_corner.y * h
        mouth_width = np.sqrt((right_x - left_x)**2 + (right_y - left_y)**2)
        
        # Create tight crop (90% mouth width ratio)
        target_ratio = 0.90
        crop_width = mouth_width / target_ratio
        crop_height = crop_width * 0.75  # Aspect ratio
        
        # Add small padding
        pad_pct = 0.02
        pad_x = int(crop_width * pad_pct)
        pad_y = int(crop_height * pad_pct)
        
        # Crop bounds
        center_x, center_y = lip_centroid
        x1 = int(center_x - crop_width / 2) - pad_x
        x2 = int(center_x + crop_width / 2) + pad_x
        y1 = int(center_y - crop_height / 2) - pad_y
        y2 = int(center_y + crop_height / 2) + pad_y
        
        # Clamp to image bounds
        x1 = max(0, x1)
        x2 = min(w, x2)
        y1 = max(0, y1)
        y2 = min(h, y2)
        
        # Extract crop
        crop = frame[y1:y2, x1:x2]
        
        if crop.size > 0:
            # Resize to 96x96
            resized_crop = cv2.resize(crop, (96, 96))
            
            # Calculate coverage
            crop_lip_points = []
            crop_w = x2 - x1
            crop_h = y2 - y1
            
            for x, y in lip_points:
                if x1 <= x <= x2 and y1 <= y <= y2:
                    crop_x = (x - x1) / crop_w * 96
                    crop_y = (y - y1) / crop_h * 96
                    crop_lip_points.append((int(crop_x), int(crop_y)))
            
            coverage = 0.0
            if len(crop_lip_points) >= 3:
                hull_points = cv2.convexHull(np.array(crop_lip_points, dtype=np.int32))
                lip_area = cv2.contourArea(hull_points)
                coverage = lip_area / (96 * 96)
            
            print(f"    📊 Crop: {crop.shape} -> 96x96, coverage: {coverage:.3f}")
            
            # Draw lip polygon on crop
            crop_with_overlay = resized_crop.copy()
            if len(crop_lip_points) >= 3:
                hull_points = cv2.convexHull(np.array(crop_lip_points, dtype=np.int32))
                cv2.polylines(crop_with_overlay, [hull_points], True, (0, 255, 0), 2)
                cv2.fillPoly(crop_with_overlay, [hull_points], (0, 255, 0), cv2.FILLED)
                crop_with_overlay = cv2.addWeighted(resized_crop, 0.7, crop_with_overlay, 0.3, 0)
            
            # Save crop
            filename = f"crop_{approach_name.lower().replace(' ', '_')}_{os.path.basename(video_path).replace('.webm', '')}.png"
            
            # Create side-by-side comparison
            comparison = np.hstack([resized_crop, crop_with_overlay])
            cv2.imwrite(filename, comparison)
            print(f"    💾 Crop saved: {filename}")
            
    except Exception as e:
        print(f"    ❌ Crop creation failed: {e}")


def main():
    """Main test function"""
    print("🎯 Simple MediaPipe Test on Real Videos")
    print("=" * 50)
    
    # Check MediaPipe availability
    try:
        import mediapipe as mp
        print("✅ MediaPipe available")
    except ImportError:
        print("❌ MediaPipe not available! Please run in Python 3.11 environment")
        return False
    
    # Test MediaPipe on working videos
    test_mediapipe_on_working_videos()
    
    print("\n✅ Simple MediaPipe test completed!")
    print("📁 Check for generated files: mediapipe_success_*.png and crop_*.png")
    
    return True


if __name__ == "__main__":
    main()
