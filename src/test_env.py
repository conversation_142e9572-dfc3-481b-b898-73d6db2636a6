import torch
import cv2
from ultralytics import YOLO
import mediapipe as mp
import numpy as np

print("✅ Torch version:", torch.__version__)
print("✅ OpenCV version:", cv2.__version__)

# Quick YOLO load test
model = YOLO("yolov8n.pt")
print("✅ YOLO loaded successfully")

# Quick MediaPipe test
mp_face_mesh = mp.solutions.face_mesh.FaceMesh()
print("✅ MediaPipe loaded successfully")

print("🎉 All core libraries are working!")
