"""
Training Script for ICU Lipreading Model
Mobile3DTiny + BiGRU with early stopping and comprehensive logging
"""

import os
import time
import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import OneCycleLR, StepLR
from sklearn.metrics import accuracy_score, f1_score, classification_report
import argparse
from tqdm import tqdm

from utils import setup_logging, set_seed, load_config, get_device, create_directories
from dataset import create_data_loaders
from model import create_model


class EarlyStopping:
    """Early stopping utility"""
    
    def __init__(self, patience: int = 5, mode: str = 'max', min_delta: float = 0.001):
        self.patience = patience
        self.mode = mode
        self.min_delta = min_delta
        self.counter = 0
        self.best_score = None
        self.early_stop = False
        
        self.monitor_op = np.greater if mode == 'max' else np.less
        self.min_delta *= 1 if mode == 'max' else -1
    
    def __call__(self, score: float) -> bool:
        if self.best_score is None:
            self.best_score = score
        elif self.monitor_op(score, self.best_score + self.min_delta):
            self.best_score = score
            self.counter = 0
        else:
            self.counter += 1
            if self.counter >= self.patience:
                self.early_stop = True
        
        return self.early_stop


class LipreadingTrainer:
    """Training manager for lipreading model"""
    
    def __init__(self, config: dict):
        self.config = config
        self.logger = setup_logging(
            config.get('logging', {}).get('level', 'INFO'),
            config.get('logging', {}).get('log_dir', './logs')
        )
        
        # Training configuration
        self.num_epochs = config['training'].get('epochs', config['training'].get('num_epochs', 25))
        self.learning_rate = float(config['training'].get('lr', config['training'].get('learning_rate', 0.003)))
        self.weight_decay = float(config['training']['weight_decay'])
        self.batch_size = int(config['training']['batch_size'])
        
        # Early stopping
        early_stop_config = config['training']['early_stopping']
        self.early_stopping = EarlyStopping(
            patience=early_stop_config['patience'],
            mode=early_stop_config['mode'],
            min_delta=0.001
        )
        
        # Paths
        self.checkpoint_dir = config['training']['checkpoint_dir']
        create_directories([self.checkpoint_dir])
        
        # Device
        self.device = get_device()
        
        # Initialize components
        self._setup_data_loaders()
        self._setup_model()
        self._setup_optimizer_and_scheduler()
        self._setup_loss_function()
        
        # Training tracking
        self.train_history = []
        self.val_history = []
        self.best_val_score = 0.0
        self.best_epoch = 0
    
    def _setup_data_loaders(self):
        """Setup train, validation, and test data loaders"""
        self.logger.info("📊 Setting up data loaders...")
        
        self.train_loader, self.val_loader, self.test_loader = create_data_loaders(
            self.config, batch_size=self.batch_size
        )
        
        self.logger.info(f"✅ Data loaders ready:")
        self.logger.info(f"  Train: {len(self.train_loader)} batches")
        self.logger.info(f"  Val:   {len(self.val_loader)} batches")
        self.logger.info(f"  Test:  {len(self.test_loader)} batches")
    
    def _setup_model(self):
        """Setup model and move to device"""
        self.logger.info("🤖 Setting up model...")
        
        self.model = create_model(self.config)
        self.model.to(self.device)
        
        # Print model summary
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        self.logger.info(f"  Total parameters: {total_params:,}")
        self.logger.info(f"  Trainable parameters: {trainable_params:,}")
    
    def _setup_optimizer_and_scheduler(self):
        """Setup optimizer and learning rate scheduler"""
        self.logger.info("⚙️ Setting up optimizer and scheduler...")
        
        # Optimizer
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay
        )
        
        # Scheduler
        scheduler_config = self.config['training']['scheduler']
        scheduler_type = scheduler_config['type']
        
        if scheduler_type == 'OneCycleLR':
            self.scheduler = OneCycleLR(
                self.optimizer,
                max_lr=scheduler_config['max_lr'],
                epochs=self.num_epochs,
                steps_per_epoch=len(self.train_loader),
                pct_start=scheduler_config['pct_start']
            )
            self.scheduler_step_per_batch = True
        else:
            # Default to StepLR
            self.scheduler = StepLR(self.optimizer, step_size=10, gamma=0.5)
            self.scheduler_step_per_batch = False
        
        self.logger.info(f"  Optimizer: Adam (lr={self.learning_rate})")
        self.logger.info(f"  Scheduler: {scheduler_type}")
    
    def _setup_loss_function(self):
        """Setup loss function with class weights if enabled"""
        if self.config['training']['use_class_weights']:
            # Get class weights from training dataset
            class_weights = self.train_loader.dataset.get_class_weights()
            class_weights = class_weights.to(self.device)
            self.criterion = nn.CrossEntropyLoss(weight=class_weights)
            self.logger.info("📊 Using weighted CrossEntropyLoss")
        else:
            self.criterion = nn.CrossEntropyLoss()
            self.logger.info("📊 Using standard CrossEntropyLoss")
    
    def train_epoch(self) -> dict:
        """Train for one epoch"""
        self.model.train()
        
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        
        pbar = tqdm(self.train_loader, desc="Training", leave=False)
        
        for batch_idx, (clips, labels, metadata) in enumerate(pbar):
            # Move to device
            clips = clips.to(self.device)
            labels = labels.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(clips)
            loss = self.criterion(outputs, labels)
            
            # Backward pass
            loss.backward()
            self.optimizer.step()
            
            # Step scheduler if per-batch
            if self.scheduler_step_per_batch:
                self.scheduler.step()
            
            # Track metrics
            total_loss += loss.item()
            predictions = torch.argmax(outputs, dim=1)
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
            
            # Update progress bar
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'lr': f'{self.optimizer.param_groups[0]["lr"]:.6f}'
            })
        
        # Calculate metrics
        avg_loss = total_loss / len(self.train_loader)
        accuracy = accuracy_score(all_labels, all_predictions)
        macro_f1 = f1_score(all_labels, all_predictions, average='macro')
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'macro_f1': macro_f1
        }
    
    def validate_epoch(self) -> dict:
        """Validate for one epoch"""
        self.model.eval()
        
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        
        with torch.no_grad():
            for clips, labels, metadata in tqdm(self.val_loader, desc="Validation", leave=False):
                # Move to device
                clips = clips.to(self.device)
                labels = labels.to(self.device)
                
                # Forward pass
                outputs = self.model(clips)
                loss = self.criterion(outputs, labels)
                
                # Track metrics
                total_loss += loss.item()
                predictions = torch.argmax(outputs, dim=1)
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        # Calculate metrics
        avg_loss = total_loss / len(self.val_loader)
        accuracy = accuracy_score(all_labels, all_predictions)
        macro_f1 = f1_score(all_labels, all_predictions, average='macro')
        
        # Per-class metrics
        target_phrases = self.config['data']['target_phrases']
        class_report = classification_report(
            all_labels, all_predictions, 
            target_names=target_phrases,
            output_dict=True,
            zero_division=0
        )
        
        return {
            'loss': avg_loss,
            'accuracy': accuracy,
            'macro_f1': macro_f1,
            'class_report': class_report,
            'predictions': all_predictions,
            'labels': all_labels
        }
    
    def save_checkpoint(self, epoch: int, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_score': self.best_val_score,
            'config': self.config
        }
        
        # Save latest checkpoint
        checkpoint_path = os.path.join(self.checkpoint_dir, 'latest_checkpoint.pth')
        torch.save(checkpoint, checkpoint_path)
        
        # Save best checkpoint
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, 'best_model.pth')
            torch.save(checkpoint, best_path)
            self.logger.info(f"💾 Saved best model (epoch {epoch}, macro-F1: {self.best_val_score:.4f})")
    
    def print_epoch_results(self, epoch: int, train_metrics: dict, val_metrics: dict, epoch_time: float):
        """Print formatted epoch results"""
        print(f"\nEpoch {epoch+1}/{self.num_epochs} ({epoch_time:.1f}s)")
        print("-" * 60)
        print(f"Train - Loss: {train_metrics['loss']:.4f}, Acc: {train_metrics['accuracy']:.4f}, F1: {train_metrics['macro_f1']:.4f}")
        print(f"Val   - Loss: {val_metrics['loss']:.4f}, Acc: {val_metrics['accuracy']:.4f}, F1: {val_metrics['macro_f1']:.4f}")
        
        # Per-class recall
        class_report = val_metrics['class_report']
        target_phrases = self.config['data']['target_phrases']
        
        print("\nPer-class Recall:")
        for phrase in target_phrases:
            if phrase in class_report:
                recall = class_report[phrase]['recall']
                print(f"  {phrase:20} {recall:.3f}")
        
        print(f"\nLR: {self.optimizer.param_groups[0]['lr']:.6f}")
    
    def train(self):
        """Main training loop"""
        self.logger.info("🚀 Starting training...")
        
        for epoch in range(self.num_epochs):
            epoch_start_time = time.time()
            
            # Train epoch
            train_metrics = self.train_epoch()
            
            # Validate epoch
            val_metrics = self.validate_epoch()
            
            # Step scheduler if per-epoch
            if not self.scheduler_step_per_batch:
                self.scheduler.step()
            
            epoch_time = time.time() - epoch_start_time
            
            # Track history
            self.train_history.append(train_metrics)
            self.val_history.append(val_metrics)
            
            # Print results
            self.print_epoch_results(epoch, train_metrics, val_metrics, epoch_time)
            
            # Check for best model
            current_score = val_metrics['macro_f1']
            is_best = current_score > self.best_val_score
            
            if is_best:
                self.best_val_score = current_score
                self.best_epoch = epoch
            
            # Save checkpoint
            self.save_checkpoint(epoch, is_best)
            
            # Early stopping check
            if self.early_stopping(current_score):
                self.logger.info(f"🛑 Early stopping triggered at epoch {epoch+1}")
                break
        
        # Save training history
        self._save_training_history()
        
        self.logger.info(f"🎉 Training completed!")
        self.logger.info(f"Best validation macro-F1: {self.best_val_score:.4f} (epoch {self.best_epoch+1})")
    
    def _save_training_history(self):
        """Save training history to CSV"""
        history_data = []
        
        for epoch, (train_metrics, val_metrics) in enumerate(zip(self.train_history, self.val_history)):
            history_data.append({
                'epoch': epoch + 1,
                'train_loss': train_metrics['loss'],
                'train_accuracy': train_metrics['accuracy'],
                'train_macro_f1': train_metrics['macro_f1'],
                'val_loss': val_metrics['loss'],
                'val_accuracy': val_metrics['accuracy'],
                'val_macro_f1': val_metrics['macro_f1']
            })
        
        history_df = pd.DataFrame(history_data)
        history_path = os.path.join(self.checkpoint_dir, 'training_history.csv')
        history_df.to_csv(history_path, index=False)
        
        self.logger.info(f"📊 Training history saved to {history_path}")


def main():
    parser = argparse.ArgumentParser(description='Train ICU Lipreading Model')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--resume', type=str, help='Resume from checkpoint')
    parser.add_argument('--epochs', type=int, help='Override number of epochs')
    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)

    # Override epochs if specified
    if args.epochs:
        config['training']['epochs'] = args.epochs
        config['training']['num_epochs'] = args.epochs  # Backward compatibility

    # Set seed for reproducibility
    set_seed(config.get('seed', 42))

    # Create trainer and start training
    trainer = LipreadingTrainer(config)
    trainer.train()


if __name__ == "__main__":
    main()
