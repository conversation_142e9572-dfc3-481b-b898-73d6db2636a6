"""
Mobile3DTiny + BiGRU Model for ICU Lipreading
Lightweight 3D CNN backbone with bidirectional GRU head
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional


class DepthwiseSeparableConv3D(nn.Module):
    """3D Depthwise Separable Convolution for efficiency"""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3, 
                 stride: int = 1, padding: int = 1, bias: bool = False):
        super().__init__()
        
        # Depthwise convolution
        self.depthwise = nn.Conv3d(
            in_channels, in_channels, kernel_size=kernel_size,
            stride=stride, padding=padding, groups=in_channels, bias=bias
        )
        
        # Pointwise convolution
        self.pointwise = nn.Conv3d(
            in_channels, out_channels, kernel_size=1, bias=bias
        )
        
        self.bn = nn.BatchNorm3d(out_channels)
        self.relu = nn.ReLU6(inplace=True)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.depthwise(x)
        x = self.pointwise(x)
        x = self.bn(x)
        x = self.relu(x)
        return x


class InvertedResidual3D(nn.Module):
    """3D Inverted Residual Block (MobileNetV2 style)"""
    
    def __init__(self, in_channels: int, out_channels: int, stride: int = 1, 
                 expand_ratio: int = 6):
        super().__init__()
        
        self.stride = stride
        self.use_residual = stride == 1 and in_channels == out_channels
        
        hidden_dim = in_channels * expand_ratio
        
        layers = []
        
        # Expand
        if expand_ratio != 1:
            layers.extend([
                nn.Conv3d(in_channels, hidden_dim, kernel_size=1, bias=False),
                nn.BatchNorm3d(hidden_dim),
                nn.ReLU6(inplace=True)
            ])
        
        # Depthwise
        layers.extend([
            nn.Conv3d(hidden_dim, hidden_dim, kernel_size=3, stride=stride,
                     padding=1, groups=hidden_dim, bias=False),
            nn.BatchNorm3d(hidden_dim),
            nn.ReLU6(inplace=True)
        ])
        
        # Project
        layers.extend([
            nn.Conv3d(hidden_dim, out_channels, kernel_size=1, bias=False),
            nn.BatchNorm3d(out_channels)
        ])
        
        self.conv = nn.Sequential(*layers)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if self.use_residual:
            return x + self.conv(x)
        else:
            return self.conv(x)


class Mobile3DTiny(nn.Module):
    """Lightweight 3D CNN backbone inspired by MobileNet"""
    
    def __init__(self, input_channels: int = 3, width_mult: float = 0.5, 
                 depth_mult: float = 0.5):
        super().__init__()
        
        # Scale channels based on width multiplier
        def make_divisible(v, divisor=8):
            new_v = max(divisor, int(v + divisor / 2) // divisor * divisor)
            if new_v < 0.9 * v:
                new_v += divisor
            return new_v
        
        # Initial convolution
        init_channels = make_divisible(32 * width_mult)
        self.conv1 = nn.Sequential(
            nn.Conv3d(input_channels, init_channels, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm3d(init_channels),
            nn.ReLU6(inplace=True)
        )
        
        # Inverted residual blocks configuration
        # [expand_ratio, channels, num_blocks, stride]
        block_configs = [
            [1, 16, 1, 1],   # No expansion for first block
            [6, 24, 2, 2],   # Downsample spatially
            [6, 32, 3, 2],   # Downsample spatially  
            [6, 64, 2, 1],   # Keep spatial size
        ]
        
        # Scale based on depth multiplier
        for config in block_configs:
            config[2] = max(1, int(config[2] * depth_mult))
            config[1] = make_divisible(config[1] * width_mult)
        
        # Build inverted residual blocks
        self.blocks = nn.ModuleList()
        in_channels = init_channels
        
        for expand_ratio, out_channels, num_blocks, stride in block_configs:
            for i in range(num_blocks):
                block_stride = stride if i == 0 else 1
                self.blocks.append(
                    InvertedResidual3D(in_channels, out_channels, block_stride, expand_ratio)
                )
                in_channels = out_channels
        
        # Final convolution
        final_channels = make_divisible(128 * width_mult)
        self.conv_final = nn.Sequential(
            nn.Conv3d(in_channels, final_channels, kernel_size=1, bias=False),
            nn.BatchNorm3d(final_channels),
            nn.ReLU6(inplace=True)
        )
        
        self.feature_dim = final_channels
        
        # Global average pooling (spatial only, keep temporal)
        self.global_pool = nn.AdaptiveAvgPool3d((None, 1, 1))  # Keep T, pool H,W
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: Input tensor of shape (B, C, T, H, W)
        Returns:
            features: Tensor of shape (B, T, feature_dim)
        """
        # Initial convolution
        x = self.conv1(x)  # (B, C', T, H, W)
        
        # Inverted residual blocks
        for block in self.blocks:
            x = block(x)
        
        # Final convolution
        x = self.conv_final(x)  # (B, feature_dim, T, H', W')
        
        # Global average pooling (spatial only)
        x = self.global_pool(x)  # (B, feature_dim, T, 1, 1)
        
        # Reshape for RNN: (B, T, feature_dim)
        x = x.squeeze(-1).squeeze(-1).transpose(1, 2)
        
        return x


class BiGRUHead(nn.Module):
    """Bidirectional GRU head for sequence classification"""
    
    def __init__(self, input_dim: int, hidden_size: int = 128, num_layers: int = 2,
                 num_classes: int = 10, dropout: float = 0.2):
        super().__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # Bidirectional GRU
        self.gru = nn.GRU(
            input_size=input_dim,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=True
        )
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Dropout(dropout),
            nn.Linear(hidden_size * 2, hidden_size),  # *2 for bidirectional
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, num_classes)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: Input tensor of shape (B, T, input_dim)
        Returns:
            logits: Tensor of shape (B, num_classes)
        """
        # GRU forward pass
        gru_out, _ = self.gru(x)  # (B, T, hidden_size * 2)
        
        # Use the last time step output
        last_output = gru_out[:, -1, :]  # (B, hidden_size * 2)
        
        # Classification
        logits = self.classifier(last_output)  # (B, num_classes)
        
        return logits


class Mobile3DTinyBiGRU(nn.Module):
    """Complete model: Mobile3DTiny backbone + BiGRU head"""
    
    def __init__(self, config: dict):
        super().__init__()
        
        # Extract configuration
        model_config = config['model']
        
        self.num_classes = model_config['num_classes']
        input_channels = model_config['input_channels']
        dropout = model_config['dropout']
        
        # Mobile3D configuration
        mobile3d_config = model_config['mobile3d']
        width_mult = mobile3d_config['width_mult']
        depth_mult = mobile3d_config['depth_mult']
        
        # BiGRU configuration
        bigru_config = model_config['bigru']
        hidden_size = bigru_config['hidden_size']
        num_layers = bigru_config['num_layers']
        bigru_dropout = bigru_config['dropout']
        
        # Build backbone
        self.backbone = Mobile3DTiny(
            input_channels=input_channels,
            width_mult=width_mult,
            depth_mult=depth_mult
        )
        
        # Build head
        self.head = BiGRUHead(
            input_dim=self.backbone.feature_dim,
            hidden_size=hidden_size,
            num_layers=num_layers,
            num_classes=self.num_classes,
            dropout=bigru_dropout
        )
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize model weights"""
        for m in self.modules():
            if isinstance(m, nn.Conv3d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm3d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.GRU):
                for name, param in m.named_parameters():
                    if 'weight' in name:
                        nn.init.orthogonal_(param)
                    elif 'bias' in name:
                        nn.init.constant_(param, 0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: Input tensor of shape (B, C, T, H, W)
        Returns:
            logits: Tensor of shape (B, num_classes)
        """
        # Extract features with backbone
        features = self.backbone(x)  # (B, T, feature_dim)
        
        # Classify with head
        logits = self.head(features)  # (B, num_classes)
        
        return logits
    
    def get_num_parameters(self) -> int:
        """Get total number of trainable parameters"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)


def create_model(config: dict) -> Mobile3DTinyBiGRU:
    """Factory function to create model from config"""
    model = Mobile3DTinyBiGRU(config)
    
    num_params = model.get_num_parameters()
    print(f"🤖 Created {config['model']['name']} with {num_params:,} parameters")
    
    return model


# Example usage and testing
if __name__ == "__main__":
    from utils import load_config
    
    # Load config
    config = load_config('configs/training.yaml')
    
    # Create model
    model = create_model(config)
    
    # Test forward pass
    batch_size = 4
    channels = 3
    frames = 16
    height = 96
    width = 96
    
    dummy_input = torch.randn(batch_size, channels, frames, height, width)
    
    print(f"Input shape: {dummy_input.shape}")
    
    with torch.no_grad():
        output = model(dummy_input)
        print(f"Output shape: {output.shape}")
        print(f"Output logits range: [{output.min():.3f}, {output.max():.3f}]")
    
    print("✅ Model test completed successfully!")
