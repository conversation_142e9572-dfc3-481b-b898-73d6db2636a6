"""
Preprocessing Pipeline for ICU Lipreading
YOLO Face Detection → SAM Lip Segmentation → Standardization
"""

import os
import cv2
import numpy as np
import pandas as pd
import tempfile
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import argparse
from tqdm import tqdm
import torch
try:
    from ultralytics import YOLO
except ImportError:
    YOLO = None

try:
    from segment_anything import SamPredictor, sam_model_registry
except ImportError:
    SamPredictor = None
    sam_model_registry = None

try:
    import insightface
    from insightface.app import FaceAnalysis
except ImportError:
    insightface = None
    FaceAnalysis = None

from utils import (
    setup_logging, set_seed, load_config, S3Manager,
    create_directories, get_video_info, calculate_motion_score,
    calculate_blur_score, save_frames_as_npy
)


class VideoPreprocessor:
    """Preprocess videos using YOLO→SAM pipeline"""

    def __init__(self, config: Dict, mode: str = 'landmark_anchored'):
        self.config = config
        self.mode = mode
        self.logger = setup_logging(
            config.get('logging', {}).get('level', 'INFO'),
            config.get('logging', {}).get('log_dir', './logs')
        )
        
        # Configuration
        self.img_size = config['data']['img_size']
        self.frame_length = config['data']['frames_per_clip']
        self.fps_target = config['data']['fps_target']

        # Set mode-specific output directory
        base_dir = config['data']['data_processed_dir']
        if mode == 'top_middle_supercrop_v2':
            self.processed_data_dir = f"{base_dir}_supercrop_v2"
        elif mode == 'yolo_sam_lips':
            self.processed_data_dir = f"{base_dir}_yolo_sam"
        elif mode == 'lowerface_roi_retinaface_sam':
            self.processed_data_dir = f"{base_dir}_lowerface_roi"
        else:
            self.processed_data_dir = base_dir
        
        # Quality thresholds
        self.min_face_area = config['segmentation']['quality']['min_face_area']
        self.min_motion_threshold = config['segmentation']['quality']['min_motion_threshold']
        self.max_blur_threshold = config['segmentation']['quality']['max_blur_threshold']
        
        # Initialize S3 manager
        self.s3_manager = S3Manager(config['data']['s3_bucket'])
        
        # Create output directories
        create_directories([
            self.processed_data_dir,
            os.path.join(self.processed_data_dir, 'clips'),
            os.path.join(self.processed_data_dir, 'quality_logs')
        ])
        
        # Initialize models
        self._load_models()

        # Quality tracking
        self.quality_stats = {
            'total_processed': 0,
            'successful': 0,
            'invalid_crop': 0,
            'no_landmarks': 0,
            'low_confidence': 0,
            'processing_error': 0
        }
    
    def _load_models(self):
        """Load YOLO, SAM, and MediaPipe models"""
        self.logger.info("🤖 Loading models...")

        # Load YOLO (optional for quality assessment)
        try:
            if YOLO is not None:
                yolo_model = self.config['segmentation']['yolo']['model']
                self.yolo = YOLO(yolo_model)
                self.yolo_conf = self.config['segmentation']['yolo']['conf_threshold']
                self.logger.info("✅ YOLO loaded")
            else:
                self.logger.warning("⚠️ YOLO not available (ultralytics not installed)")
                self.yolo = None
        except Exception as e:
            self.logger.warning(f"⚠️ YOLO loading failed: {e}")
            self.yolo = None

        # Load SAM (optional, skip if not available or for supercrop_v2 mode)
        if self.mode == 'top_middle_supercrop_v2':
            self.logger.info("ℹ️ SAM not needed for supercrop_v2 mode")
            self.sam_predictor = None
        elif SamPredictor is None or sam_model_registry is None:
            self.logger.warning("⚠️ SAM not available (segment_anything not installed)")
            self.sam_predictor = None
        else:
            sam_type = self.config['segmentation']['sam']['model_type']
            sam_checkpoint = self.config['segmentation']['sam']['checkpoint_path']

            if not os.path.exists(sam_checkpoint):
                self.logger.warning(f"⚠️ SAM checkpoint not found: {sam_checkpoint}")
                self.sam_predictor = None
            else:
                try:
                    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                    from utils import load_sam_model
                    self.sam_predictor = load_sam_model(sam_type, sam_checkpoint, device)
                    self.logger.info("✅ SAM model loaded")
                except Exception as e:
                    self.logger.warning(f"⚠️ SAM loading failed: {e}")
                    self.sam_predictor = None

        # Load MediaPipe FaceMesh
        try:
            import mediapipe as mp
            self.mp_face_mesh = mp.solutions.face_mesh
            self.face_mesh = self.mp_face_mesh.FaceMesh(
                static_image_mode=True,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
            self.logger.info("✅ MediaPipe FaceMesh loaded")
            self.mediapipe_available = True
        except ImportError:
            self.logger.warning("⚠️ MediaPipe not available - install with Python 3.11")
            self.face_mesh = None
            self.mediapipe_available = False
        except Exception as e:
            self.logger.warning(f"⚠️ MediaPipe loading failed: {e}")
            self.face_mesh = None
            self.mediapipe_available = False

        # Load RetinaFace (for lowerface_roi_retinaface_sam mode)
        self.retinaface_app = None
        if self.mode == 'lowerface_roi_retinaface_sam':
            try:
                if FaceAnalysis is not None:
                    self.retinaface_app = FaceAnalysis(providers=['CPUExecutionProvider'])
                    self.retinaface_app.prepare(ctx_id=0, det_size=(640, 640))
                    self.logger.info("✅ RetinaFace (insightface) loaded")
                else:
                    self.logger.warning("⚠️ insightface not available - will use YOLO fallback")
            except Exception as e:
                self.logger.warning(f"⚠️ RetinaFace loading failed: {e} - will use YOLO fallback")
                self.retinaface_app = None

        self.logger.info("✅ Models loaded successfully")

        # Initialize quality tracking CSV
        self.quality_records = []
    
    def extract_frames_from_video(self, video_path: str) -> Optional[np.ndarray]:
        """Extract frames from video file"""
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            self.logger.error(f"❌ Cannot open video: {video_path}")
            return None

        frames = []
        frame_count = 0
        max_frames = 200  # Limit to prevent memory issues

        while frame_count < max_frames:
            ret, frame = cap.read()
            if not ret:
                break

            # Ensure frame has consistent shape
            if frame is None or frame.size == 0:
                continue

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Ensure all frames have the same shape
            if len(frames) > 0 and frame_rgb.shape != frames[0].shape:
                # Resize to match first frame
                frame_rgb = cv2.resize(frame_rgb, (frames[0].shape[1], frames[0].shape[0]))

            frames.append(frame_rgb)
            frame_count += 1

        cap.release()

        if not frames:
            return None

        try:
            return np.array(frames)
        except ValueError as e:
            self.logger.error(f"❌ Error creating frame array: {e}")
            return None
    
    def extract_landmark_anchored_lip_crop(self, frames: list) -> tuple:
        """
        Extract lip crops using landmark-anchored, scale-controlled approach with temporal stability.

        1. Use MediaPipe FaceMesh to get lip landmarks and mouth width
        2. Target coverage: mouth width should occupy 60-75% of crop width after resize to 96x96
        3. Apply temporal smoothing across frames
        4. Auto-adjust crop size based on lip coverage
        5. Fallback to YOLO+grid if MediaPipe fails

        Returns (processed_frames, validation_results, crop_metrics)
        """
        # Configuration from training.yaml (hardcoded for now)
        config = {
            'target_mouth_width_ratio': 0.70,
            'aspect_ratio_y': 1.0,
            'pad_pct': 0.10,
            'coverage_accept_min': 0.30,
            'coverage_accept_max': 0.75,
            'smooth_window': 5,
            'min_frames_with_landmarks_pct': 90
        }

        processed_frames = []
        validation_results = []
        frame_metrics = []

        # Step 1: Extract lip landmarks and compute mouth width for each frame
        raw_landmarks = []
        mouth_widths = []
        lip_centroids = []

        for frame_idx, frame in enumerate(frames):
            landmarks, mouth_width, lip_centroid = self._extract_lip_landmarks_and_metrics(frame)
            raw_landmarks.append(landmarks)
            mouth_widths.append(mouth_width if mouth_width > 0 else None)
            lip_centroids.append(lip_centroid)

        # Step 2: Apply temporal smoothing
        smoothed_mouth_widths = self._apply_temporal_smoothing(mouth_widths, config['smooth_window'])
        smoothed_centroids = self._apply_centroid_smoothing(lip_centroids, config['smooth_window'])

        # Step 3: Process each frame with smoothed parameters
        for frame_idx, frame in enumerate(frames):
            landmarks = raw_landmarks[frame_idx]
            mouth_width = smoothed_mouth_widths[frame_idx]
            lip_centroid = smoothed_centroids[frame_idx]

            if landmarks is not None and mouth_width is not None and lip_centroid is not None:
                # Primary path: landmark-anchored cropping
                crop, metrics = self._create_landmark_anchored_crop(
                    frame, landmarks, mouth_width, lip_centroid, config
                )

                validation_results.append({
                    'valid': metrics['valid'],
                    'confidence': metrics['confidence'],
                    'has_landmarks': True,
                    'adjustment': metrics['method'],
                    'coverage_before': metrics['coverage_before'],
                    'coverage_after': metrics['coverage_after'],
                    'adjustments_made': metrics['adjustments_made']
                })

                frame_metrics.append(metrics)
                processed_frames.append(crop)

            else:
                # Fallback path: YOLO + grid approach
                crop, fallback_metrics = self._fallback_crop_with_validation(frame, config)

                validation_results.append({
                    'valid': False,
                    'confidence': 0.3,
                    'has_landmarks': False,
                    'adjustment': 'fallback_no_landmarks',
                    'coverage_before': 0.0,
                    'coverage_after': 0.0,
                    'adjustments_made': 0
                })

                frame_metrics.append(fallback_metrics)
                processed_frames.append(crop)

        # Step 4: Compute clip-level metrics
        frames_with_landmarks = sum(1 for r in validation_results if r['has_landmarks'])
        frames_with_landmarks_pct = (frames_with_landmarks / len(frames) * 100) if frames else 0

        # Determine if clip is valid based on landmark coverage threshold
        is_valid_clip = frames_with_landmarks_pct >= config['min_frames_with_landmarks_pct']

        clip_metrics = {
            'frames_with_landmarks_pct': frames_with_landmarks_pct,
            'valid_clip': is_valid_clip,
            'avg_mouth_width': np.mean([m for m in mouth_widths if m is not None]) if any(m for m in mouth_widths if m is not None) else 0,
            'avg_coverage_after': np.mean([m['coverage_after'] for m in frame_metrics]),
            'frame_metrics': frame_metrics
        }

        return processed_frames, validation_results, clip_metrics

    def _extract_lip_landmarks_and_metrics(self, frame: np.ndarray) -> tuple:
        """
        Extract lip landmarks and compute mouth width and centroid.
        Returns (landmarks, mouth_width, lip_centroid) or (None, None, None) if failed
        """
        if not self.mediapipe_available or self.face_mesh is None:
            return None, None, None

        try:
            # Convert to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB) if len(frame.shape) == 3 else frame
            if rgb_frame.dtype == np.float32:
                rgb_frame = (rgb_frame * 255).astype(np.uint8)

            # Run MediaPipe
            results = self.face_mesh.process(rgb_frame)

            if not results.multi_face_landmarks:
                return None, None, None

            face_landmarks = results.multi_face_landmarks[0]
            h, w = rgb_frame.shape[:2]

            # Define lip landmark indices (MediaPipe 468-point model)
            # Outer lip contour for mouth width calculation
            outer_lip_indices = [61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318, 402, 317, 14, 87, 178, 88, 95]
            # Mouth corners for width calculation
            left_mouth_corner = 61
            right_mouth_corner = 291

            # Extract lip points
            lip_points = []
            for idx in outer_lip_indices:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = landmark.x * w
                    y = landmark.y * h
                    lip_points.append((x, y))

            if len(lip_points) < 4:
                return None, None, None

            # Calculate mouth width (distance between mouth corners)
            if left_mouth_corner < len(face_landmarks.landmark) and right_mouth_corner < len(face_landmarks.landmark):
                left_corner = face_landmarks.landmark[left_mouth_corner]
                right_corner = face_landmarks.landmark[right_mouth_corner]

                left_x, left_y = left_corner.x * w, left_corner.y * h
                right_x, right_y = right_corner.x * w, right_corner.y * h

                mouth_width = np.sqrt((right_x - left_x)**2 + (right_y - left_y)**2)
            else:
                # Fallback: use bounding box width of lip points
                lip_points_array = np.array(lip_points)
                mouth_width = np.max(lip_points_array[:, 0]) - np.min(lip_points_array[:, 0])

            # Calculate lip centroid
            lip_points_array = np.array(lip_points)
            lip_centroid = np.mean(lip_points_array, axis=0)

            return face_landmarks, mouth_width, lip_centroid

        except Exception as e:
            self.logger.debug(f"Landmark extraction failed: {e}")
            return None, None, None

    def _apply_temporal_smoothing(self, mouth_widths: list, window_size: int) -> list:
        """Apply median smoothing to mouth widths across frames"""
        smoothed = []
        valid_widths = [w for w in mouth_widths if w is not None]

        if not valid_widths:
            return mouth_widths

        median_width = np.median(valid_widths)

        for i, width in enumerate(mouth_widths):
            if width is not None:
                # Use median of surrounding frames
                start_idx = max(0, i - window_size // 2)
                end_idx = min(len(mouth_widths), i + window_size // 2 + 1)
                window_widths = [w for w in mouth_widths[start_idx:end_idx] if w is not None]

                if window_widths:
                    smoothed.append(np.median(window_widths))
                else:
                    smoothed.append(median_width)
            else:
                # Use last valid width or median
                if smoothed and smoothed[-1] is not None:
                    smoothed.append(smoothed[-1])
                else:
                    smoothed.append(median_width)

        return smoothed

    def _apply_centroid_smoothing(self, centroids: list, window_size: int) -> list:
        """Apply smoothing to lip centroids across frames"""
        smoothed = []
        valid_centroids = [c for c in centroids if c is not None]

        if not valid_centroids:
            return centroids

        median_centroid = np.median(valid_centroids, axis=0)

        for i, centroid in enumerate(centroids):
            if centroid is not None:
                # Use median of surrounding frames
                start_idx = max(0, i - window_size // 2)
                end_idx = min(len(centroids), i + window_size // 2 + 1)
                window_centroids = [c for c in centroids[start_idx:end_idx] if c is not None]

                if window_centroids:
                    smoothed.append(np.median(window_centroids, axis=0))
                else:
                    smoothed.append(median_centroid)
            else:
                # Use last valid centroid or median
                if smoothed and smoothed[-1] is not None:
                    smoothed.append(smoothed[-1])
                else:
                    smoothed.append(median_centroid)

        return smoothed

    def _validate_geometric_lip_crop(self, face_bbox, crop_bbox, face_confidence) -> tuple:
        """
        Validate geometric lip crop based on face detection confidence and geometric constraints.
        Returns (is_valid, confidence)
        """
        try:
            if face_bbox is None:
                return False, 0.0

            fx1, fy1, fx2, fy2 = face_bbox
            cx1, cy1, cx2, cy2 = crop_bbox

            face_w = fx2 - fx1
            face_h = fy2 - fy1
            crop_w = cx2 - cx1
            crop_h = cy2 - cy1

            # Base confidence from face detection
            confidence = face_confidence

            # Geometric validation checks

            # 1. Check if crop is reasonably positioned within face
            crop_center_x = (cx1 + cx2) // 2
            crop_center_y = (cy1 + cy2) // 2
            face_center_x = (fx1 + fx2) // 2
            face_center_y = (fy1 + fy2) // 2

            # Crop should be in lower half of face (lips are in lower face)
            if crop_center_y < face_center_y:
                confidence *= 0.7  # Penalty for crop in upper face

            # Crop should be horizontally centered relative to face
            horizontal_offset = abs(crop_center_x - face_center_x) / face_w
            if horizontal_offset > 0.3:  # More than 30% offset from face center
                confidence *= (1.0 - horizontal_offset)

            # 2. Check crop size relative to face size
            crop_to_face_ratio_w = crop_w / face_w
            crop_to_face_ratio_h = crop_h / face_h

            # Ideal lip crop should be 30-60% of face width and 10-25% of face height
            if not (0.2 <= crop_to_face_ratio_w <= 0.8):
                confidence *= 0.8  # Penalty for unusual width ratio

            if not (0.08 <= crop_to_face_ratio_h <= 0.4):
                confidence *= 0.8  # Penalty for unusual height ratio

            # 3. Check minimum crop size (avoid too small crops)
            min_crop_size = 24
            if crop_w < min_crop_size or crop_h < min_crop_size:
                confidence *= 0.5  # Heavy penalty for too small crops

            # 4. Face detection confidence threshold
            if face_confidence < 0.5:
                confidence *= 0.6  # Penalty for low face detection confidence

            # Consider valid if confidence >= 90% (high threshold for landmark-driven approach)
            is_valid = confidence >= 0.9

            return is_valid, confidence

        except Exception as e:
            self.logger.debug(f"Geometric validation failed: {e}")
            return False, 0.0

    def _create_landmark_anchored_crop(self, frame: np.ndarray, landmarks, mouth_width: float,
                                     lip_centroid: np.ndarray, config: dict) -> tuple:
        """
        Create landmark-anchored crop with scale control and auto-adjustment.
        Returns (crop, metrics)
        """
        h, w = frame.shape[:2]

        # Calculate initial crop dimensions based on mouth width
        target_ratio = config['target_mouth_width_ratio']
        aspect_ratio_y = config['aspect_ratio_y']
        pad_pct = config['pad_pct']

        # Target crop width: mouth_width / target_ratio
        crop_width = mouth_width / target_ratio
        crop_height = crop_width * aspect_ratio_y

        # Center crop on lip centroid
        center_x, center_y = lip_centroid

        # Initial crop bounds
        x1 = int(center_x - crop_width / 2)
        x2 = int(center_x + crop_width / 2)
        y1 = int(center_y - crop_height / 2)
        y2 = int(center_y + crop_height / 2)

        # Add padding
        pad_x = int(crop_width * pad_pct)
        pad_y = int(crop_height * pad_pct)

        x1_pad = max(0, x1 - pad_x)
        x2_pad = min(w, x2 + pad_x)
        y1_pad = max(0, y1 - pad_y)
        y2_pad = min(h, y2 + pad_y)

        # Extract initial crop
        initial_crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]

        if initial_crop.size == 0:
            # Fallback to center crop
            crop_size = min(h, w) // 2
            center_h, center_w = h // 2, w // 2
            fallback_crop = frame[center_h-crop_size//2:center_h+crop_size//2,
                                center_w-crop_size//2:center_w+crop_size//2]
            resized_crop = cv2.resize(fallback_crop, (self.img_size, self.img_size))

            return resized_crop, {
                'valid': False,
                'confidence': 0.0,
                'method': 'fallback_bounds_error',
                'coverage_before': 0.0,
                'coverage_after': 0.0,
                'adjustments_made': 0,
                'mouth_width_px': mouth_width,
                'crop_w_px': 0,
                'target_ratio': target_ratio
            }

        # Resize to 96x96 for coverage calculation
        resized_crop = cv2.resize(initial_crop, (self.img_size, self.img_size))

        # Calculate initial lip coverage
        coverage_before = self._calculate_lip_coverage(landmarks, (x1_pad, y1_pad, x2_pad, y2_pad), frame.shape)

        # Auto-adjust crop size based on coverage
        final_crop = resized_crop
        coverage_after = coverage_before
        adjustments_made = 0
        final_crop_w_px = x2_pad - x1_pad

        coverage_min = config['coverage_accept_min']
        coverage_max = config['coverage_accept_max']

        if coverage_before < coverage_min or coverage_before > coverage_max:
            # Need to adjust crop size
            for attempt in range(3):  # Up to 3 adjustment attempts
                if coverage_after < coverage_min:
                    # Crop too loose - reduce crop width by 10%
                    crop_width *= 0.9
                elif coverage_after > coverage_max:
                    # Crop too tight - increase crop width by 10%
                    crop_width *= 1.1

                # Recalculate crop bounds
                x1 = int(center_x - crop_width / 2)
                x2 = int(center_x + crop_width / 2)
                y1 = int(center_y - crop_height / 2)
                y2 = int(center_y + crop_height / 2)

                # Add padding
                pad_x = int(crop_width * pad_pct)
                pad_y = int(crop_height * pad_pct)

                x1_pad = max(0, x1 - pad_x)
                x2_pad = min(w, x2 + pad_x)
                y1_pad = max(0, y1 - pad_y)
                y2_pad = min(h, y2 + pad_y)

                # Extract adjusted crop
                adjusted_crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]

                if adjusted_crop.size > 0:
                    final_crop = cv2.resize(adjusted_crop, (self.img_size, self.img_size))
                    coverage_after = self._calculate_lip_coverage(landmarks, (x1_pad, y1_pad, x2_pad, y2_pad), frame.shape)
                    final_crop_w_px = x2_pad - x1_pad
                    adjustments_made += 1

                    # Check if coverage is now acceptable
                    if coverage_min <= coverage_after <= coverage_max:
                        break
                else:
                    break

        # Determine validity
        is_valid = coverage_min <= coverage_after <= coverage_max
        confidence = min(1.0, coverage_after / coverage_max) if coverage_after <= coverage_max else max(0.0, 1.0 - (coverage_after - coverage_max))

        metrics = {
            'valid': is_valid,
            'confidence': confidence,
            'method': 'landmark_anchored',
            'coverage_before': coverage_before,
            'coverage_after': coverage_after,
            'adjustments_made': adjustments_made,
            'mouth_width_px': mouth_width,
            'crop_w_px': final_crop_w_px,
            'target_ratio': target_ratio
        }

        return final_crop, metrics

    def _calculate_lip_coverage(self, landmarks, crop_bbox: tuple, frame_shape: tuple) -> float:
        """
        Calculate lip coverage as percentage of crop area occupied by lip polygon.
        Returns coverage ratio (0.0 to 1.0)
        """
        if landmarks is None:
            return 0.0

        try:
            h, w = frame_shape[:2]
            x1, y1, x2, y2 = crop_bbox
            crop_w = x2 - x1
            crop_h = y2 - y1

            if crop_w <= 0 or crop_h <= 0:
                return 0.0

            # Define lip landmark indices for coverage calculation
            lip_indices = [61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318, 402, 317, 14, 87, 178, 88, 95]

            # Extract lip points in original frame coordinates
            lip_points = []
            for idx in lip_indices:
                if idx < len(landmarks.landmark):
                    landmark = landmarks.landmark[idx]
                    x = landmark.x * w
                    y = landmark.y * h
                    lip_points.append((x, y))

            if len(lip_points) < 3:
                return 0.0

            # Convert to crop coordinates
            crop_lip_points = []
            for x, y in lip_points:
                if x1 <= x <= x2 and y1 <= y <= y2:
                    crop_x = (x - x1) / crop_w * self.img_size
                    crop_y = (y - y1) / crop_h * self.img_size
                    crop_lip_points.append((int(crop_x), int(crop_y)))

            if len(crop_lip_points) < 3:
                return 0.0

            # Create convex hull of lip points
            hull_points = cv2.convexHull(np.array(crop_lip_points, dtype=np.int32))

            # Calculate area of convex hull
            lip_area = cv2.contourArea(hull_points)

            # Total crop area
            crop_area = self.img_size * self.img_size

            # Coverage ratio
            coverage = lip_area / crop_area

            return min(1.0, max(0.0, coverage))

        except Exception as e:
            self.logger.debug(f"Coverage calculation failed: {e}")
            return 0.0

    def _fallback_crop_with_validation(self, frame: np.ndarray, config: dict) -> tuple:
        """
        Fallback cropping when MediaPipe fails: YOLO + grid approach
        Returns (crop, metrics)
        """
        h, w = frame.shape[:2]

        # Try YOLO face detection first
        face_bbox = None
        if self.yolo is not None:
            try:
                results = self.yolo(frame, conf=self.yolo_conf, classes=[0], verbose=False)
                if results and results[0].boxes:
                    boxes = results[0].boxes.xyxy.cpu().numpy()
                    if len(boxes) > 0:
                        box = boxes[0]  # Take first detection
                        x1, y1, x2, y2 = map(int, box)
                        face_bbox = (x1, y1, x2, y2)
            except Exception as e:
                self.logger.debug(f"YOLO fallback failed: {e}")

        if face_bbox is not None:
            # Apply 3x2 grid within face bbox - top-middle cell
            fx1, fy1, fx2, fy2 = face_bbox
            face_w = fx2 - fx1
            face_h = fy2 - fy1

            col_width = face_w // 3
            row_height = face_h // 2

            # Top-middle cell
            x1 = fx1 + col_width
            x2 = fx1 + 2 * col_width
            y1 = fy1
            y2 = fy1 + row_height

            # Ensure bounds
            x1 = max(0, min(x1, w-1))
            x2 = max(x1+1, min(x2, w))
            y1 = max(0, min(y1, h-1))
            y2 = max(y1+1, min(y2, h))

            # Extract crop
            crop = frame[y1:y2, x1:x2]

            if crop.size > 0:
                resized_crop = cv2.resize(crop, (self.img_size, self.img_size))

                # Try MediaPipe again on the cropped region
                landmarks, _, _ = self._extract_lip_landmarks_and_metrics(resized_crop)

                if landmarks is not None:
                    method = "fallback_face_grid_with_landmarks"
                    confidence = 0.6
                else:
                    method = "fallback_face_grid_no_landmarks"
                    confidence = 0.3

                metrics = {
                    'valid': landmarks is not None,
                    'confidence': confidence,
                    'method': method,
                    'coverage_before': 0.0,
                    'coverage_after': 0.0,
                    'adjustments_made': 0,
                    'mouth_width_px': 0,
                    'crop_w_px': x2 - x1,
                    'target_ratio': 0.0
                }

                return resized_crop, metrics

        # Final fallback: center crop
        crop_size = min(h, w) // 2
        center_h, center_w = h // 2, w // 2
        center_crop = frame[center_h-crop_size//2:center_h+crop_size//2,
                          center_w-crop_size//2:center_w+crop_size//2]

        if center_crop.size > 0:
            resized_crop = cv2.resize(center_crop, (self.img_size, self.img_size))
        else:
            # Ultimate fallback: black image
            resized_crop = np.zeros((self.img_size, self.img_size, 3), dtype=np.uint8)

        metrics = {
            'valid': False,
            'confidence': 0.1,
            'method': 'fallback_center_crop',
            'coverage_before': 0.0,
            'coverage_after': 0.0,
            'adjustments_made': 0,
            'mouth_width_px': 0,
            'crop_w_px': crop_size,
            'target_ratio': 0.0
        }

        return resized_crop, metrics
    
    def segment_lips_with_sam(self, frame: np.ndarray, face_bbox: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """Segment lips using SAM within face bounding box"""
        if self.sam_predictor is None:
            return None

        x1, y1, x2, y2 = face_bbox

        # Extract face region
        face_crop = frame[y1:y2, x1:x2]

        if face_crop.size == 0:
            return None

        try:
            # Set image for SAM
            self.sam_predictor.set_image(face_crop)

            # Create prompt point in lower center of face (approximate lip location)
            face_h, face_w = face_crop.shape[:2]
            lip_point = np.array([[face_w // 2, int(face_h * 0.75)]])  # Lower center
            lip_label = np.array([1])

            # Generate mask
            masks, scores, _ = self.sam_predictor.predict(
                point_coords=lip_point,
                point_labels=lip_label,
                multimask_output=True
            )

            if len(masks) == 0:
                return None

            # Select best mask
            best_mask = masks[np.argmax(scores)]

            # Apply mask to face crop
            masked_face = face_crop.copy()
            masked_face[~best_mask] = 0  # Set non-lip regions to black

            return masked_face

        except Exception as e:
            self.logger.debug(f"SAM segmentation failed: {e}")
            return None
    
    def standardize_clip(self, frames: np.ndarray) -> Optional[np.ndarray]:
        """Standardize clip to fixed size and frame count"""
        if len(frames) == 0:
            return None
        
        # Temporal standardization: sample to target frame count
        current_frames = len(frames)
        
        if current_frames < self.frame_length:
            # Pad by repeating last frame
            padding_needed = self.frame_length - current_frames
            last_frame = frames[-1:].repeat(padding_needed, axis=0)
            frames = np.concatenate([frames, last_frame], axis=0)
        elif current_frames > self.frame_length:
            # Uniform sampling
            indices = np.linspace(0, current_frames - 1, self.frame_length, dtype=int)
            frames = frames[indices]
        
        # Spatial standardization: resize to target size
        standardized_frames = []
        for frame in frames:
            if frame.shape[:2] != (self.img_size, self.img_size):
                resized = cv2.resize(frame, (self.img_size, self.img_size))
                standardized_frames.append(resized)
            else:
                standardized_frames.append(frame)
        
        standardized_frames = np.array(standardized_frames)
        
        # Pixel normalization: scale to [0, 1]
        standardized_frames = standardized_frames.astype(np.float32) / 255.0
        
        return standardized_frames

    def extract_top_middle_supercrop_v2(self, frames: list) -> tuple:
        """
        Extract lip crops using top-middle supercrop v2 approach.

        Objective: tight 96×96 crops where the lip band is clearly centred and large,
        using strong prior: "Lips are in the TOP-MIDDLE of a 3×3 grid (row 1, col 2)".

        Returns (processed_frames, validation_results, clip_metrics)
        """
        config = self.config['data']['supercrop_v2']

        processed_frames = []
        validation_results = []
        frame_metrics = []

        # Temporal stability tracking
        chosen_centers = []  # (cx, cy) for each frame
        chosen_widths = []   # width for each frame
        frame_scores = []    # lipness scores for each frame

        # Process each frame
        for frame_idx, frame in enumerate(frames):
            # A) Candidate generation
            candidates = self._generate_supercrop_candidates(frame, config)

            # B) Lipness scoring
            best_candidate, best_score = self._score_supercrop_candidates(frame, candidates, config)

            # Store for temporal filtering
            chosen_centers.append((best_candidate['cx'], best_candidate['cy']))
            chosen_widths.append(best_candidate['width'])
            frame_scores.append(best_score)

            # Create initial crop
            crop = self._create_supercrop_from_candidate(frame, best_candidate, config)
            processed_frames.append(crop)

            # Store frame metrics
            frame_metrics.append({
                'frame_idx': frame_idx,
                'cx': best_candidate['cx'],
                'cy': best_candidate['cy'],
                'width': best_candidate['width'],
                'height': best_candidate['height'],
                'score': best_score,
                'edge_energy': best_candidate.get('edge_energy', 0),
                'redness': best_candidate.get('redness', 0),
                'dark_ratio': best_candidate.get('dark_ratio', 0)
            })

        # C) Apply temporal stability
        smoothed_centers, smoothed_widths = self._apply_temporal_stability(
            chosen_centers, chosen_widths, frame_scores, config
        )

        # Recreate crops with smoothed parameters
        final_processed_frames = []
        for frame_idx, frame in enumerate(frames):
            cx, cy = smoothed_centers[frame_idx]
            width = smoothed_widths[frame_idx]
            height = int(width * 0.76)  # Fixed aspect ratio

            candidate = {
                'cx': cx, 'cy': cy, 'width': width, 'height': height,
                'x1': int(cx - width/2), 'x2': int(cx + width/2),
                'y1': int(cy - height/2), 'y2': int(cy + height/2)
            }

            crop = self._create_supercrop_from_candidate(frame, candidate, config)
            final_processed_frames.append(crop)

            # Update frame metrics
            frame_metrics[frame_idx].update({
                'final_cx': cx,
                'final_cy': cy,
                'final_width': width,
                'final_height': height
            })

        # D) Motion filter (clip-level validation)
        motion_score, edge_var, is_valid = self._compute_motion_filter(final_processed_frames, frame_metrics, config)

        # Create validation results
        for frame_idx in range(len(frames)):
            validation_results.append({
                'valid': is_valid,
                'confidence': frame_scores[frame_idx] if frame_scores[frame_idx] > 0.1 else 0.1,
                'has_landmarks': True,  # We always generate crops
                'adjustment': 'supercrop_v2',
                'coverage_before': 0.0,  # Not applicable for this method
                'coverage_after': 0.0,   # Not applicable for this method
                'adjustments_made': 0
            })

        # Compile clip metrics
        clip_metrics = {
            'frame_metrics': frame_metrics,
            'motion_score': motion_score,
            'edge_var': edge_var,
            'valid': is_valid,
            'mean_score': np.mean(frame_scores),
            'min_score': np.min(frame_scores),
            'max_score': np.max(frame_scores)
        }

        return final_processed_frames, validation_results, clip_metrics

    def _generate_supercrop_candidates(self, frame: np.ndarray, config: dict) -> list:
        """
        Generate candidate crop regions for supercrop v2.

        A) Candidate generation (per frame, on the ORIGINAL frame or 2× upscaled if configured)
        1) Define thirds: tw=W/3, th=H/3. Start in the top-middle cell.
        2) Upward bias: search vertical centres cy' in [th*0.20, th*0.70] (i.e., inside the top row)
           with denser sampling ABOVE the cell centre.
        3) Keep cx = W/2, but also try ±0.05*W offsets (left/right) to handle slight horizontal misplacement.
        4) Try 3 widths: {0.30*W, 0.34*W, 0.38*W}. Height = width * 0.76. Clamp to bounds.
        """
        # Optionally upscale frame
        if config.get('upscale_factor', 1.0) > 1.0:
            upscale_factor = config['upscale_factor']
            new_h = int(frame.shape[0] * upscale_factor)
            new_w = int(frame.shape[1] * upscale_factor)
            work_frame = cv2.resize(frame, (new_w, new_h))
        else:
            work_frame = frame.copy()

        h, w = work_frame.shape[:2]

        # Define thirds
        tw = w / 3
        th = h / 3

        candidates = []

        # Vertical search range (within top row, biased upward)
        search_vert_top = config.get('search_vert_top', 0.20)
        search_vert_bottom = config.get('search_vert_bottom', 0.70)
        cy_min = th * search_vert_top
        cy_max = th * search_vert_bottom

        # Denser sampling above center - use more points in upper half
        cy_values = []
        # Upper half (more dense)
        cy_mid = (cy_min + cy_max) / 2
        for i in range(8):  # 8 points in upper half
            cy = cy_min + (cy_mid - cy_min) * i / 7
            cy_values.append(cy)
        # Lower half (less dense)
        for i in range(1, 5):  # 4 points in lower half
            cy = cy_mid + (cy_max - cy_mid) * i / 4
            cy_values.append(cy)

        # Horizontal positions (center ± offset)
        search_horiz_offset = config.get('search_horiz_offset', 0.05)
        cx_values = [
            w / 2,  # Center
            w / 2 - w * search_horiz_offset,  # Left offset
            w / 2 + w * search_horiz_offset   # Right offset
        ]

        # Width fractions to try
        widths_frac = config.get('widths_frac', [0.30, 0.34, 0.38])

        # Generate all combinations
        for cx in cx_values:
            for cy in cy_values:
                for width_frac in widths_frac:
                    width = int(w * width_frac)
                    height = int(width * 0.76)  # Fixed aspect ratio

                    # Calculate bounds
                    x1 = int(cx - width / 2)
                    x2 = int(cx + width / 2)
                    y1 = int(cy - height / 2)
                    y2 = int(cy + height / 2)

                    # Clamp to frame bounds
                    x1 = max(0, x1)
                    x2 = min(w, x2)
                    y1 = max(0, y1)
                    y2 = min(h, y2)

                    # Skip if too small after clamping
                    if (x2 - x1) < 32 or (y2 - y1) < 24:
                        continue

                    candidates.append({
                        'cx': cx,
                        'cy': cy,
                        'width': width,
                        'height': height,
                        'x1': x1,
                        'x2': x2,
                        'y1': y1,
                        'y2': y2,
                        'width_frac': width_frac,
                        'work_frame': work_frame  # Store reference to working frame
                    })

        return candidates

    def _score_supercrop_candidates(self, frame: np.ndarray, candidates: list, config: dict) -> tuple:
        """
        Score candidates using lipness features and return best candidate.

        B) Lipness scoring (choose the best candidate)
        For each candidate crop:
         - E_h: horizontal edge energy in a ±8 px band around its vertical centre (Sobel |dy|).
         - Rg: redness = mean(max(0, R - G)) in the central 40% width × 30% height of the crop.
         - D: dark-slit ratio = fraction of pixels with value < 40 in a ±6 px central band (proxy for lip opening).
         - Score S = 0.55*norm(E_h) + 0.30*norm(Rg) + 0.15*norm(D).
        Pick the candidate with max S. (All features normalised per-frame across candidates.)
        """
        if not candidates:
            return None, 0.0

        edge_band_px = config.get('edge_band_px', 8)
        dark_band_px = config.get('dark_band_px', 6)
        weights = config.get('weights', {'edge': 0.55, 'redness': 0.30, 'dark': 0.15})

        # Calculate features for all candidates
        edge_energies = []
        redness_values = []
        dark_ratios = []

        for candidate in candidates:
            work_frame = candidate['work_frame']
            x1, y1, x2, y2 = candidate['x1'], candidate['y1'], candidate['x2'], candidate['y2']

            # Extract crop
            crop = work_frame[y1:y2, x1:x2]
            if crop.size == 0:
                edge_energies.append(0.0)
                redness_values.append(0.0)
                dark_ratios.append(0.0)
                continue

            crop_h, crop_w = crop.shape[:2]

            # E_h: horizontal edge energy around vertical center
            center_y = crop_h // 2
            band_y1 = max(0, center_y - edge_band_px)
            band_y2 = min(crop_h, center_y + edge_band_px)

            if band_y2 > band_y1:
                edge_region = crop[band_y1:band_y2, :]
                # Convert to grayscale for edge detection
                if len(edge_region.shape) == 3:
                    gray_edge = cv2.cvtColor(edge_region, cv2.COLOR_BGR2GRAY)
                else:
                    gray_edge = edge_region

                # Sobel horizontal edges (dy)
                sobel_y = cv2.Sobel(gray_edge, cv2.CV_64F, 0, 1, ksize=3)
                edge_energy = np.mean(np.abs(sobel_y))
            else:
                edge_energy = 0.0

            edge_energies.append(edge_energy)

            # Rg: redness in central region
            central_w = int(crop_w * 0.4)  # 40% width
            central_h = int(crop_h * 0.3)  # 30% height
            center_x = crop_w // 2
            center_y = crop_h // 2

            central_x1 = max(0, center_x - central_w // 2)
            central_x2 = min(crop_w, center_x + central_w // 2)
            central_y1 = max(0, center_y - central_h // 2)
            central_y2 = min(crop_h, center_y + central_h // 2)

            if central_x2 > central_x1 and central_y2 > central_y1:
                central_region = crop[central_y1:central_y2, central_x1:central_x2]
                if len(central_region.shape) == 3 and central_region.shape[2] >= 3:
                    # BGR format: B=0, G=1, R=2
                    r_channel = central_region[:, :, 2].astype(np.float32)
                    g_channel = central_region[:, :, 1].astype(np.float32)
                    redness = np.mean(np.maximum(0, r_channel - g_channel))
                else:
                    redness = 0.0
            else:
                redness = 0.0

            redness_values.append(redness)

            # D: dark-slit ratio in central band
            dark_band_y1 = max(0, center_y - dark_band_px)
            dark_band_y2 = min(crop_h, center_y + dark_band_px)

            if dark_band_y2 > dark_band_y1:
                dark_region = crop[dark_band_y1:dark_band_y2, :]
                if len(dark_region.shape) == 3:
                    gray_dark = cv2.cvtColor(dark_region, cv2.COLOR_BGR2GRAY)
                else:
                    gray_dark = dark_region

                dark_pixels = np.sum(gray_dark < 40)
                total_pixels = gray_dark.size
                dark_ratio = dark_pixels / total_pixels if total_pixels > 0 else 0.0
            else:
                dark_ratio = 0.0

            dark_ratios.append(dark_ratio)

        # Normalize features across candidates
        def normalize_feature(values):
            values = np.array(values)
            if np.std(values) > 1e-6:
                return (values - np.mean(values)) / np.std(values)
            else:
                return np.zeros_like(values)

        norm_edge = normalize_feature(edge_energies)
        norm_redness = normalize_feature(redness_values)
        norm_dark = normalize_feature(dark_ratios)

        # Calculate composite scores
        scores = (weights['edge'] * norm_edge +
                 weights['redness'] * norm_redness +
                 weights['dark'] * norm_dark)

        # Find best candidate
        best_idx = np.argmax(scores)
        best_candidate = candidates[best_idx].copy()
        best_score = scores[best_idx]

        # Store feature values in candidate
        best_candidate['edge_energy'] = edge_energies[best_idx]
        best_candidate['redness'] = redness_values[best_idx]
        best_candidate['dark_ratio'] = dark_ratios[best_idx]
        best_candidate['norm_edge'] = norm_edge[best_idx]
        best_candidate['norm_redness'] = norm_redness[best_idx]
        best_candidate['norm_dark'] = norm_dark[best_idx]

        return best_candidate, float(best_score)

    def _create_supercrop_from_candidate(self, frame: np.ndarray, candidate: dict, config: dict) -> np.ndarray:
        """
        Create the final crop from a candidate, applying anchor ratio positioning.

        **Anchor ratio**: when building a crop, place the candidate horizontal lip line at 0.42 of the crop height
        (more space below than above so we don't chop the lower lip when lips sit high in the cell).
        """
        # Use original frame (not upscaled work_frame)
        h, w = frame.shape[:2]

        # Get candidate parameters (may be from upscaled coordinates)
        cx, cy = candidate['cx'], candidate['cy']
        width, height = candidate['width'], candidate['height']

        # If we used upscaling, scale coordinates back to original frame
        if config.get('upscale_factor', 1.0) > 1.0:
            scale_factor = 1.0 / config['upscale_factor']
            cx = int(cx * scale_factor)
            cy = int(cy * scale_factor)
            width = int(width * scale_factor)
            height = int(height * scale_factor)

        # Apply anchor ratio: place the lip line at anchor_y_ratio of crop height
        anchor_y_ratio = config.get('anchor_y_ratio', 0.42)

        # Calculate crop bounds with anchor positioning
        # The lip line (cy) should be at anchor_y_ratio from the top of the crop
        crop_y1 = int(cy - height * anchor_y_ratio)
        crop_y2 = crop_y1 + height
        crop_x1 = int(cx - width / 2)
        crop_x2 = crop_x1 + width

        # Clamp to frame bounds
        crop_x1 = max(0, crop_x1)
        crop_x2 = min(w, crop_x2)
        crop_y1 = max(0, crop_y1)
        crop_y2 = min(h, crop_y2)

        # Extract crop
        crop = frame[crop_y1:crop_y2, crop_x1:crop_x2]

        # Resize to target size (96x96)
        if crop.size > 0:
            resized_crop = cv2.resize(crop, (self.img_size, self.img_size))
            return resized_crop
        else:
            # Fallback: return black image
            return np.zeros((self.img_size, self.img_size, 3), dtype=np.uint8)

    def _apply_temporal_stability(self, chosen_centers: list, chosen_widths: list,
                                frame_scores: list, config: dict) -> tuple:
        """
        Apply temporal stability filtering.

        C) Temporal stability
        - Median-filter the chosen centre (cx, cy) and width over a 5-frame window.
        - If a frame produces a very low S (bottom 5%), reuse the previous box.
        """
        if not chosen_centers:
            return chosen_centers, chosen_widths

        # Convert to numpy arrays for easier processing
        centers_array = np.array(chosen_centers)  # Shape: (n_frames, 2)
        widths_array = np.array(chosen_widths)
        scores_array = np.array(frame_scores)

        # Determine low score threshold (bottom 5%)
        score_threshold = np.percentile(scores_array, 5) if len(scores_array) > 0 else 0.0

        # Apply median filtering with window size 5
        window_size = 5
        smoothed_centers = []
        smoothed_widths = []

        for i in range(len(chosen_centers)):
            # Define window around current frame
            start_idx = max(0, i - window_size // 2)
            end_idx = min(len(chosen_centers), i + window_size // 2 + 1)

            # Extract window data
            window_centers = centers_array[start_idx:end_idx]
            window_widths = widths_array[start_idx:end_idx]

            # Apply median filtering
            median_cx = np.median(window_centers[:, 0])
            median_cy = np.median(window_centers[:, 1])
            median_width = np.median(window_widths)

            # Check if current frame has very low score
            if scores_array[i] < score_threshold and i > 0:
                # Reuse previous box
                smoothed_centers.append(smoothed_centers[-1])
                smoothed_widths.append(smoothed_widths[-1])
            else:
                # Use median-filtered values
                smoothed_centers.append((median_cx, median_cy))
                smoothed_widths.append(median_width)

        return smoothed_centers, smoothed_widths

    def _compute_motion_filter(self, processed_frames: list, frame_metrics: list, config: dict) -> tuple:
        """
        Compute motion filter for clip-level validation.

        D) Motion filter (clip-level)
        - Compute motion_score = mean(|frame_t - frame_{t-1}|) inside the FINAL crop across the clip.
        - Also compute edge_var = variance of E_h over time (speech has fluctuations).
        - Mark clip invalid if motion_score < 2.5/255 OR edge_var < small_threshold. Log and exclude from training.
        """
        if len(processed_frames) < 2:
            return 0.0, 0.0, False

        motion_thresh = config.get('motion_thresh', 0.0098)  # 2.5/255
        edge_var_thresh = config.get('edge_var_thresh', 0.002)

        # Compute motion score
        motion_diffs = []
        for i in range(1, len(processed_frames)):
            frame_curr = processed_frames[i].astype(np.float32) / 255.0
            frame_prev = processed_frames[i-1].astype(np.float32) / 255.0

            # Mean absolute difference
            diff = np.mean(np.abs(frame_curr - frame_prev))
            motion_diffs.append(diff)

        motion_score = np.mean(motion_diffs) if motion_diffs else 0.0

        # Compute edge variance
        edge_energies = [fm.get('edge_energy', 0) for fm in frame_metrics]
        edge_var = np.var(edge_energies) if len(edge_energies) > 1 else 0.0

        # Determine validity
        is_valid = (motion_score >= motion_thresh) and (edge_var >= edge_var_thresh)

        return motion_score, edge_var, is_valid

    def extract_yolo_sam_lips(self, frames: list) -> tuple:
        """
        Extract lip crops using YOLO face detection + SAM segmentation.

        Pipeline (per frame):
        1) YOLO face localisation
        2) Derive mouth prompt region from face box (top-middle prior)
        3) SAM segmentation (ViT-B)
        4) Convert mask to crop ("mask→box" with adaptive zoom)
        5) Temporal smoothing
        6) Motion & quality checks (clip level)
        """
        config = self.config['data']['yolo_sam']
        processed_frames = []
        validation_results = []
        frame_metrics = []

        # Process each frame
        for frame_idx, frame in enumerate(frames):
            frame_result = self._process_yolo_sam_frame(frame, frame_idx, config)

            if frame_result['crop'] is not None:
                processed_frames.append(frame_result['crop'])
                validation_results.append({
                    'has_landmarks': frame_result['has_face'],  # Use face detection as landmark equivalent
                    'valid': frame_result['has_mask'],
                    'confidence': frame_result['confidence']
                })
            else:
                # Use fallback (supercrop_v2) if YOLO+SAM fails
                fallback_crop = self._fallback_supercrop(frame, config)
                processed_frames.append(fallback_crop)
                validation_results.append({
                    'has_landmarks': False,
                    'valid': False,
                    'confidence': 0.0
                })
                frame_result['fallback'] = True

            frame_metrics.append(frame_result)

        # Apply temporal smoothing
        processed_frames = self._apply_temporal_smoothing(processed_frames, frame_metrics, config)

        # Calculate clip-level metrics
        clip_metrics = self._calculate_yolo_sam_clip_metrics(processed_frames, frame_metrics, config)

        return processed_frames, validation_results, clip_metrics

    def _process_yolo_sam_frame(self, frame: np.ndarray, frame_idx: int, config: dict) -> dict:
        """Process a single frame with YOLO+SAM pipeline"""
        result = {
            'frame_idx': frame_idx,
            'has_face': False,
            'has_mask': False,
            'face_area': 0,
            'mask_area': 0,
            'mask_width': 0,
            'crop_w': 0,
            'crop_h': 0,
            'confidence': 0.0,
            'crop': None,
            'fallback': False
        }

        try:
            # 1) YOLO face detection
            face_box = self._detect_face_yolo(frame, config)
            if face_box is None:
                return result

            result['has_face'] = True
            result['face_area'] = (face_box[2] - face_box[0]) * (face_box[3] - face_box[1])
            result['confidence'] = face_box[4] if len(face_box) > 4 else 1.0

            # 2) Derive mouth prompt region
            mouth_prompts = self._derive_mouth_prompts(face_box, frame.shape, config)

            # 3) SAM segmentation
            mask = self._segment_lips_sam(frame, mouth_prompts, config)
            if mask is None:
                return result

            result['has_mask'] = True
            result['mask_area'] = np.sum(mask)

            # 4) Convert mask to crop
            crop = self._mask_to_crop(frame, mask, config)
            if crop is None:
                return result

            result['crop'] = crop
            result['crop_w'] = crop.shape[1]
            result['crop_h'] = crop.shape[0]
            result['mask_width'] = self._calculate_mask_width(mask)

            return result

        except Exception as e:
            self.logger.warning(f"Frame {frame_idx} YOLO+SAM failed: {e}")
            return result

    def _detect_face_yolo(self, frame: np.ndarray, config: dict) -> Optional[list]:
        """Detect face using YOLO and return bounding box [x1, y1, x2, y2, conf]"""
        if self.yolo is None:
            return None

        try:
            # Run YOLO detection
            results = self.yolo(frame, conf=config['conf'], iou=config['iou'], classes=[0])  # Person class

            if len(results) == 0 or len(results[0].boxes) == 0:
                return None

            # Get highest confidence detection
            boxes = results[0].boxes
            confidences = boxes.conf.cpu().numpy()
            best_idx = np.argmax(confidences)

            # Extract box coordinates
            box = boxes.xyxy[best_idx].cpu().numpy()  # [x1, y1, x2, y2]
            conf = confidences[best_idx]

            return [int(box[0]), int(box[1]), int(box[2]), int(box[3]), float(conf)]

        except Exception as e:
            self.logger.warning(f"YOLO face detection failed: {e}")
            return None

    def _derive_mouth_prompts(self, face_box: list, frame_shape: tuple, config: dict) -> dict:
        """Derive mouth prompt region from face box using top-middle prior"""
        x1, y1, x2, y2 = face_box[:4]
        face_w = x2 - x1
        face_h = y2 - y1

        # Split face into 3x3 grid
        cell_w = face_w // 3
        cell_h = face_h // 3

        # Top-middle cell (row 0, col 1)
        mouth_x1 = x1 + cell_w
        mouth_y1 = y1
        mouth_x2 = x1 + 2 * cell_w
        mouth_y2 = y1 + cell_h

        # Bias upward by 15% of cell height
        bias_offset = int(cell_h * config['mouth_seed_bias_up'])
        mouth_y1 = max(0, mouth_y1 - bias_offset)
        mouth_y2 = max(0, mouth_y2 - bias_offset)

        # Foreground point (center of mouth seed box)
        fg_x = (mouth_x1 + mouth_x2) // 2
        fg_y = (mouth_y1 + mouth_y2) // 2

        # Background points
        bg_points = [
            # Top-left cell center
            (x1 + cell_w // 2, y1 + cell_h // 2),
            # Top-right cell center
            (x1 + 2 * cell_w + cell_w // 2, y1 + cell_h // 2),
            # Below chin (10% face height under face box)
            (fg_x, min(frame_shape[0] - 1, y2 + int(face_h * 0.1)))
        ]

        # Bounding box prompt (lower 60% of face)
        box_y1 = y1 + int(face_h * (1 - config['face_lower_frac_for_box']))
        box_prompt = [x1, box_y1, x2, y2]

        return {
            'fg_point': (fg_x, fg_y),
            'bg_points': bg_points,
            'box_prompt': box_prompt
        }

    def _segment_lips_sam(self, frame: np.ndarray, prompts: dict, config: dict) -> Optional[np.ndarray]:
        """Segment lips using SAM with face-derived prompts"""
        if self.sam_predictor is None:
            return None

        try:
            # Set image for SAM
            self.sam_predictor.set_image(frame)

            # Prepare prompts
            fg_point = np.array([prompts['fg_point']])
            bg_points = np.array(prompts['bg_points'])
            all_points = np.vstack([fg_point, bg_points])
            point_labels = np.array([1] + [0] * len(bg_points))  # 1 for foreground, 0 for background

            box_prompt = np.array(prompts['box_prompt'])

            # Run SAM prediction
            masks, scores, _ = self.sam_predictor.predict(
                point_coords=all_points,
                point_labels=point_labels,
                box=box_prompt,
                multimask_output=True
            )

            if len(masks) == 0:
                return None

            # Choose best mask based on criteria
            best_mask = self._select_best_lip_mask(masks, scores, prompts, frame.shape, config)

            if best_mask is not None:
                # Post-process mask
                best_mask = self._postprocess_lip_mask(best_mask)

            return best_mask

        except Exception as e:
            self.logger.warning(f"SAM segmentation failed: {e}")
            return None

    def _select_best_lip_mask(self, masks: np.ndarray, scores: np.ndarray, prompts: dict, frame_shape: tuple, config: dict) -> Optional[np.ndarray]:
        """Select the best mask from SAM candidates based on lip-specific criteria"""
        if len(masks) == 0:
            return None

        fg_point = prompts['fg_point']
        face_area = (prompts['box_prompt'][2] - prompts['box_prompt'][0]) * (prompts['box_prompt'][3] - prompts['box_prompt'][1])

        best_mask = None
        best_score = -1

        for i, mask in enumerate(masks):
            # Calculate mask area as percentage of face area
            mask_area = np.sum(mask)
            area_ratio = mask_area / face_area

            # Check if area is in typical lip range (0.5% to 6% of face area)
            if area_ratio < 0.005 or area_ratio > 0.06:
                continue

            # Calculate centroid distance to foreground point
            y_coords, x_coords = np.where(mask)
            if len(y_coords) == 0:
                continue

            centroid_x = np.mean(x_coords)
            centroid_y = np.mean(y_coords)
            distance = np.sqrt((centroid_x - fg_point[0])**2 + (centroid_y - fg_point[1])**2)

            # Combined score: SAM score - distance penalty
            combined_score = scores[i] - (distance / 100.0)  # Normalize distance

            if combined_score > best_score:
                best_score = combined_score
                best_mask = mask

        return best_mask

    def _postprocess_lip_mask(self, mask: np.ndarray) -> np.ndarray:
        """Post-process lip mask with morphological operations"""
        # Morphological close (3x3 kernel)
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask = cv2.morphologyEx(mask.astype(np.uint8), cv2.MORPH_CLOSE, kernel)

        # Remove small components
        num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(mask, connectivity=8)

        if num_labels <= 1:  # Only background
            return mask

        # Find median component size (excluding background)
        component_sizes = stats[1:, cv2.CC_STAT_AREA]  # Skip background (label 0)
        if len(component_sizes) == 0:
            return mask

        median_size = np.median(component_sizes)
        min_size = median_size * 0.25

        # Keep only components larger than threshold
        cleaned_mask = np.zeros_like(mask)
        for i in range(1, num_labels):
            if stats[i, cv2.CC_STAT_AREA] >= min_size:
                cleaned_mask[labels == i] = 1

        return cleaned_mask

    def _mask_to_crop(self, frame: np.ndarray, mask: np.ndarray, config: dict) -> Optional[np.ndarray]:
        """Convert mask to 96x96 crop with adaptive zoom and positioning"""
        # Find tight bounding box around mask
        y_coords, x_coords = np.where(mask)
        if len(y_coords) == 0:
            return None

        x_min, x_max = np.min(x_coords), np.max(x_coords)
        y_min, y_max = np.min(y_coords), np.max(y_coords)

        mask_width = x_max - x_min
        mask_height = y_max - y_min

        # Expand by padding
        pad_w = int(mask_width * config['pad_frac'])
        pad_h = int(mask_height * config['pad_frac'])

        crop_x1 = max(0, x_min - pad_w)
        crop_x2 = min(frame.shape[1], x_max + pad_w)
        crop_y1 = max(0, y_min - pad_h)
        crop_y2 = min(frame.shape[0], y_max + pad_h)

        # Apply adaptive zoom based on mask width ratio
        crop_w = crop_x2 - crop_x1
        crop_h = crop_y2 - crop_y1

        if crop_w > 0 and crop_h > 0:
            mask_width_ratio = mask_width / crop_w

            # Adjust crop size based on mask width ratio
            if mask_width_ratio < config['min_mask_width_ratio']:
                # Shrink crop (max 3 iterations)
                for _ in range(3):
                    shrink_factor = 0.9
                    new_w = int(crop_w * shrink_factor)
                    new_h = int(crop_h * shrink_factor)

                    # Center the smaller crop
                    center_x = (crop_x1 + crop_x2) // 2
                    center_y = (crop_y1 + crop_y2) // 2

                    crop_x1 = max(0, center_x - new_w // 2)
                    crop_x2 = min(frame.shape[1], center_x + new_w // 2)
                    crop_y1 = max(0, center_y - new_h // 2)
                    crop_y2 = min(frame.shape[0], center_y + new_h // 2)

                    crop_w, crop_h = crop_x2 - crop_x1, crop_y2 - crop_y1
                    if crop_w > 0:
                        mask_width_ratio = mask_width / crop_w
                        if mask_width_ratio >= config['min_mask_width_ratio']:
                            break

            elif mask_width_ratio > config['max_mask_width_ratio']:
                # Expand crop (max 2 iterations)
                for _ in range(2):
                    expand_factor = 1.1
                    new_w = int(crop_w * expand_factor)
                    new_h = int(crop_h * expand_factor)

                    # Center the larger crop
                    center_x = (crop_x1 + crop_x2) // 2
                    center_y = (crop_y1 + crop_y2) // 2

                    crop_x1 = max(0, center_x - new_w // 2)
                    crop_x2 = min(frame.shape[1], center_x + new_w // 2)
                    crop_y1 = max(0, center_y - new_h // 2)
                    crop_y2 = min(frame.shape[0], center_y + new_h // 2)

                    crop_w, crop_h = crop_x2 - crop_x1, crop_y2 - crop_y1
                    if crop_w > 0:
                        mask_width_ratio = mask_width / crop_w
                        if mask_width_ratio <= config['max_mask_width_ratio']:
                            break

        # Apply lipline anchoring at 0.42 of crop height
        mask_center_y = (y_min + y_max) // 2
        target_y_in_crop = int(crop_h * config['anchor_y_ratio'])

        # Adjust crop position to place mask center at target position
        y_offset = target_y_in_crop - (mask_center_y - crop_y1)
        crop_y1 = max(0, crop_y1 - y_offset)
        crop_y2 = min(frame.shape[0], crop_y2 - y_offset)

        # Extract and resize crop to 96x96
        crop = frame[crop_y1:crop_y2, crop_x1:crop_x2]
        if crop.size == 0:
            return None

        crop_resized = cv2.resize(crop, (96, 96))
        return crop_resized

    def _calculate_mask_width(self, mask: np.ndarray) -> float:
        """Calculate the width of the mask in pixels"""
        y_coords, x_coords = np.where(mask)
        if len(x_coords) == 0:
            return 0.0
        return float(np.max(x_coords) - np.min(x_coords))

    def _fallback_supercrop(self, frame: np.ndarray, config: dict) -> np.ndarray:
        """Fallback to supercrop_v2 when YOLO+SAM fails"""
        # Use a simplified version of supercrop_v2 for fallback
        h, w = frame.shape[:2]

        # Top-middle region (simplified)
        cell_w, cell_h = w // 3, h // 3
        crop_x1 = cell_w
        crop_x2 = 2 * cell_w
        crop_y1 = 0
        crop_y2 = cell_h

        # Extract and resize
        crop = frame[crop_y1:crop_y2, crop_x1:crop_x2]
        if crop.size == 0:
            # Emergency fallback: center crop
            center_x, center_y = w // 2, h // 2
            size = min(w, h) // 4
            crop = frame[center_y-size:center_y+size, center_x-size:center_x+size]

        if crop.size > 0:
            return cv2.resize(crop, (96, 96))
        else:
            # Last resort: return black frame
            return np.zeros((96, 96, 3), dtype=np.uint8)

    def _apply_temporal_smoothing(self, processed_frames: list, frame_metrics: list, config: dict) -> list:
        """Apply temporal smoothing using median filter over 5-frame window"""
        if len(processed_frames) < config['smooth_window']:
            return processed_frames

        smoothed_frames = []
        window_size = config['smooth_window']
        half_window = window_size // 2

        for i, frame in enumerate(processed_frames):
            if frame_metrics[i].get('has_mask', False):
                # Use original frame if it has a valid mask
                smoothed_frames.append(frame)
            else:
                # Find nearest valid frame for replacement
                valid_frame = None
                for offset in range(1, half_window + 1):
                    # Check backward
                    if i - offset >= 0 and frame_metrics[i - offset].get('has_mask', False):
                        valid_frame = processed_frames[i - offset]
                        break
                    # Check forward
                    if i + offset < len(processed_frames) and frame_metrics[i + offset].get('has_mask', False):
                        valid_frame = processed_frames[i + offset]
                        break

                if valid_frame is not None:
                    smoothed_frames.append(valid_frame)
                else:
                    smoothed_frames.append(frame)  # Keep original if no valid frame found

        return smoothed_frames

    def _calculate_yolo_sam_clip_metrics(self, processed_frames: list, frame_metrics: list, config: dict) -> dict:
        """Calculate clip-level metrics for YOLO+SAM processing"""
        if not frame_metrics:
            return {'valid': False, 'error_reason': 'no_frames'}

        # Count frames with masks
        frames_with_mask = sum(1 for m in frame_metrics if m.get('has_mask', False))
        frames_with_mask_pct = (frames_with_mask / len(frame_metrics)) * 100

        # Calculate motion score
        motion_score = 0.0
        if len(processed_frames) > 1:
            motion_scores = []
            for i in range(1, len(processed_frames)):
                if processed_frames[i] is not None and processed_frames[i-1] is not None:
                    diff = cv2.absdiff(processed_frames[i], processed_frames[i-1])
                    motion_scores.append(np.mean(diff) / 255.0)
            motion_score = np.mean(motion_scores) if motion_scores else 0.0

        # Calculate average lip coverage
        mask_areas = [m.get('mask_area', 0) for m in frame_metrics if m.get('has_mask', False)]
        avg_lip_coverage = np.mean(mask_areas) / (96 * 96) if mask_areas else 0.0  # Normalize to crop size

        # Determine validity
        valid = (frames_with_mask_pct >= config['min_frames_with_mask_pct'] and
                motion_score >= config['motion_thresh'])

        error_reason = None
        if not valid:
            if frames_with_mask_pct < config['min_frames_with_mask_pct']:
                error_reason = 'insufficient_mask_coverage'
            elif motion_score < config['motion_thresh']:
                error_reason = 'insufficient_motion'

        return {
            'valid': valid,
            'error_reason': error_reason,
            'frames_with_mask_pct': frames_with_mask_pct,
            'motion_score': motion_score,
            'lip_coverage': avg_lip_coverage,
            'frame_metrics': frame_metrics,
            'total_frames': len(frame_metrics),
            'frames_with_face': sum(1 for m in frame_metrics if m.get('has_face', False)),
            'frames_with_mask': frames_with_mask,
            'fallback_frames': sum(1 for m in frame_metrics if m.get('fallback', False))
        }

    def extract_lowerface_roi_retinaface_sam(self, frames: list) -> tuple:
        """
        Extract lip crops using lower-face ROI with RetinaFace + SAM pipeline.

        Pipeline (per frame):
        1) Define ROI from spatial prior (top-middle 3x3 cell, expanded)
        2) Preprocess ROI (YCrCb + CLAHE)
        3) RetinaFace detection with landmarks (fallback to YOLO)
        4) SAM segmentation with mouth-specific prompts
        5) Mask validation and crop extraction
        6) Temporal smoothing and quality checks
        """
        config = self.config['data']['lowerface_roi_retinaface_sam']
        processed_frames = []
        validation_results = []
        frame_metrics = []

        # Process each frame
        for frame_idx, frame in enumerate(frames):
            frame_result = self._process_lowerface_roi_frame(frame, frame_idx, config)

            if frame_result['crop'] is not None:
                processed_frames.append(frame_result['crop'])
                validation_results.append({
                    'has_landmarks': frame_result['has_landmarks'],
                    'valid': frame_result['has_mask'],
                    'confidence': frame_result['confidence']
                })
            else:
                # Use fallback (supercrop_v2) if pipeline fails
                fallback_crop = self._fallback_supercrop_v2_roi(frame, config)
                processed_frames.append(fallback_crop)
                validation_results.append({
                    'has_landmarks': False,
                    'valid': False,
                    'confidence': 0.0
                })
                frame_result['fallback'] = True

            frame_metrics.append(frame_result)

        # Apply temporal smoothing
        processed_frames = self._apply_temporal_smoothing_roi(processed_frames, frame_metrics, config)

        # Calculate clip-level metrics
        clip_metrics = self._calculate_lowerface_roi_clip_metrics(processed_frames, frame_metrics, config)

        return processed_frames, validation_results, clip_metrics

    def _process_lowerface_roi_frame(self, frame: np.ndarray, frame_idx: int, config: dict) -> dict:
        """Process a single frame with lower-face ROI + RetinaFace + SAM pipeline"""
        result = {
            'frame_idx': frame_idx,
            'has_face': False,
            'has_landmarks': False,
            'has_mask': False,
            'face_area': 0,
            'mask_area': 0,
            'centroid_y_frac': 0.0,
            'mask_width_ratio': 0.0,
            'confidence': 0.0,
            'crop': None,
            'fallback': False,
            'detector_fallback': False,
            'landmarks_estimated': False
        }

        try:
            # A) Define ROI from spatial prior
            roi_coords, roi_frame = self._extract_roi_from_spatial_prior(frame, config)
            if roi_frame is None:
                return result

            # B) Preprocess ROI (YCrCb + CLAHE)
            preprocessed_roi = self._preprocess_roi(roi_frame, config)

            # C) Partial-face detector with landmarks
            face_data = self._detect_face_with_landmarks_roi(preprocessed_roi, config)
            if face_data is None:
                result['detector_fallback'] = True
                # Use supercrop_v2 fallback for this frame
                return result

            result['has_face'] = True
            result['has_landmarks'] = face_data['has_landmarks']
            result['landmarks_estimated'] = face_data.get('landmarks_estimated', False)
            result['confidence'] = face_data['confidence']

            # D) SAM prompts (mouth-specific)
            sam_prompts = self._create_mouth_specific_sam_prompts(face_data, roi_coords, config)

            # E) SAM segmentation
            mask = self._segment_lips_sam_roi(frame, sam_prompts, config)  # Use original frame for SAM
            if mask is None:
                return result

            # F) Mask validity check
            if not self._validate_lip_mask_roi(mask, face_data, config):
                # Try shifting box down and retry once
                shifted_prompts = self._shift_sam_prompts_down(sam_prompts, face_data, config)
                mask = self._segment_lips_sam_roi(frame, shifted_prompts, config)
                if mask is None or not self._validate_lip_mask_roi(mask, face_data, config):
                    return result

            result['has_mask'] = True
            result['mask_area'] = np.sum(mask)

            # G) Extract crop from mask with target scale
            crop = self._extract_crop_from_mask_roi(frame, mask, face_data, config)
            if crop is None:
                return result

            result['crop'] = crop

            # Calculate additional metrics
            mask_bbox = self._get_mask_bbox(mask)
            if mask_bbox is not None:
                face_bbox = face_data['face_bbox']
                face_h = face_bbox[3] - face_bbox[1]
                face_w = face_bbox[2] - face_bbox[0]

                mask_center_y = (mask_bbox[1] + mask_bbox[3]) / 2
                result['centroid_y_frac'] = (mask_center_y - face_bbox[1]) / face_h

                mask_w = mask_bbox[2] - mask_bbox[0]
                result['mask_width_ratio'] = mask_w / face_w

            return result

        except Exception as e:
            self.logger.debug(f"Frame {frame_idx} processing failed: {e}")
            return result

    def _extract_roi_from_spatial_prior(self, frame: np.ndarray, config: dict) -> tuple:
        """Extract ROI from spatial prior (top-middle 3x3 cell, expanded)"""
        h, w = frame.shape[:2]

        # Define ROI coordinates
        roi_x1 = int(w * config['roi_x'][0])
        roi_x2 = int(w * config['roi_x'][1])
        roi_y1 = int(h * config['roi_y'][0])
        roi_y2 = int(h * config['roi_y'][1])

        # Ensure valid coordinates
        roi_x1 = max(0, roi_x1)
        roi_x2 = min(w, roi_x2)
        roi_y1 = max(0, roi_y1)
        roi_y2 = min(h, roi_y2)

        if roi_x2 <= roi_x1 or roi_y2 <= roi_y1:
            return None, None

        # Extract ROI
        roi_frame = frame[roi_y1:roi_y2, roi_x1:roi_x2]
        roi_coords = (roi_x1, roi_y1, roi_x2, roi_y2)

        return roi_coords, roi_frame

    def _preprocess_roi(self, roi_frame: np.ndarray, config: dict) -> np.ndarray:
        """Preprocess ROI: Convert to YCrCb and apply CLAHE to Y channel"""
        # Convert to YCrCb
        ycrcb = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2YCrCb)

        # Apply CLAHE to Y channel
        clahe = cv2.createCLAHE(
            clipLimit=config['clahe']['clip'],
            tileGridSize=(config['clahe']['tiles'], config['clahe']['tiles'])
        )
        ycrcb[:, :, 0] = clahe.apply(ycrcb[:, :, 0])

        # Convert back to BGR
        preprocessed = cv2.cvtColor(ycrcb, cv2.COLOR_YCrCb2BGR)

        return preprocessed

    def _detect_face_with_landmarks_roi(self, roi_frame: np.ndarray, config: dict) -> Optional[dict]:
        """Detect face with landmarks using RetinaFace, fallback to YOLO"""

        # Try RetinaFace first
        if self.retinaface_app is not None:
            try:
                faces = self.retinaface_app.get(roi_frame)
                if len(faces) > 0:
                    # Use the face with highest confidence
                    face = max(faces, key=lambda x: x.det_score)

                    # Extract face bbox
                    bbox = face.bbox.astype(int)
                    face_bbox = [bbox[0], bbox[1], bbox[2], bbox[3]]

                    # Extract landmarks (5 points: left_eye, right_eye, nose, mouth_left, mouth_right)
                    kps = face.kps.astype(int)
                    landmarks = {
                        'mouth_left': (kps[3][0], kps[3][1]),
                        'mouth_right': (kps[4][0], kps[4][1]),
                        'nose_tip': (kps[2][0], kps[2][1])
                    }

                    return {
                        'face_bbox': face_bbox,
                        'landmarks': landmarks,
                        'has_landmarks': True,
                        'confidence': float(face.det_score),
                        'landmarks_estimated': False
                    }
            except Exception as e:
                self.logger.debug(f"RetinaFace detection failed: {e}")

        # Fallback to YOLO face detection
        if self.yolo is not None:
            try:
                results = self.yolo(roi_frame, conf=config['yolo_conf'], iou=config['yolo_iou'], classes=[0])
                if len(results) > 0 and len(results[0].boxes) > 0:
                    # Get highest confidence detection
                    boxes = results[0].boxes
                    confidences = boxes.conf.cpu().numpy()
                    best_idx = np.argmax(confidences)

                    box = boxes.xyxy[best_idx].cpu().numpy()
                    face_bbox = [int(box[0]), int(box[1]), int(box[2]), int(box[3])]
                    confidence = float(confidences[best_idx])

                    # Estimate landmarks using geometric heuristics
                    landmarks = self._estimate_landmarks_from_face_bbox(face_bbox, roi_frame)

                    return {
                        'face_bbox': face_bbox,
                        'landmarks': landmarks,
                        'has_landmarks': landmarks is not None,
                        'confidence': confidence,
                        'landmarks_estimated': True
                    }
            except Exception as e:
                self.logger.debug(f"YOLO face detection failed: {e}")

        return None

    def _estimate_landmarks_from_face_bbox(self, face_bbox: list, roi_frame: np.ndarray) -> Optional[dict]:
        """Estimate mouth landmarks from face bounding box using edge detection"""
        x1, y1, x2, y2 = face_bbox
        face_h = y2 - y1
        face_w = x2 - x1

        if face_h < 20 or face_w < 20:
            return None

        try:
            # Focus on middle 50% of face height for mouth detection
            search_y1 = y1 + int(face_h * 0.25)
            search_y2 = y1 + int(face_h * 0.75)
            search_x1 = x1 + int(face_w * 0.2)
            search_x2 = x2 - int(face_w * 0.2)

            # Convert to grayscale for edge detection
            gray = cv2.cvtColor(roi_frame, cv2.COLOR_BGR2GRAY)

            # Find row with maximum horizontal edge energy (Sobel |dy|)
            max_edge_energy = 0
            best_y = search_y1 + (search_y2 - search_y1) // 2  # Default to middle

            for y in range(search_y1, search_y2):
                if y >= gray.shape[0]:
                    break
                row = gray[y, search_x1:min(search_x2, gray.shape[1])]
                if len(row) > 1:
                    sobel_y = cv2.Sobel(row.reshape(1, -1), cv2.CV_64F, 0, 1, ksize=3)
                    edge_energy = np.mean(np.abs(sobel_y))
                    if edge_energy > max_edge_energy:
                        max_edge_energy = edge_energy
                        best_y = y

            # Find mouth corners by scanning left/right for gradient sign changes
            mouth_y = best_y
            mouth_left = None
            mouth_right = None

            # Scan from center outward for mouth corners
            center_x = (search_x1 + search_x2) // 2

            # Left corner
            for x in range(center_x, search_x1, -1):
                if x < gray.shape[1] and mouth_y < gray.shape[0]:
                    if x > 0:
                        grad = int(gray[mouth_y, x]) - int(gray[mouth_y, x-1])
                        if abs(grad) > 10:  # Significant gradient change
                            mouth_left = (x, mouth_y)
                            break

            # Right corner
            for x in range(center_x, min(search_x2, gray.shape[1])):
                if x < gray.shape[1] - 1 and mouth_y < gray.shape[0]:
                    grad = int(gray[mouth_y, x+1]) - int(gray[mouth_y, x])
                    if abs(grad) > 10:  # Significant gradient change
                        mouth_right = (x, mouth_y)
                        break

            # Fallback to geometric estimation if edge detection fails
            if mouth_left is None:
                mouth_left = (x1 + int(face_w * 0.35), best_y)
            if mouth_right is None:
                mouth_right = (x2 - int(face_w * 0.35), best_y)

            # Nose tip estimation (upper third of face, center)
            nose_tip = (x1 + face_w // 2, y1 + int(face_h * 0.4))

            return {
                'mouth_left': mouth_left,
                'mouth_right': mouth_right,
                'nose_tip': nose_tip
            }

        except Exception as e:
            self.logger.debug(f"Landmark estimation failed: {e}")
            return None

    def _create_mouth_specific_sam_prompts(self, face_data: dict, roi_coords: tuple, config: dict) -> dict:
        """Create mouth-specific SAM prompts"""
        face_bbox = face_data['face_bbox']
        landmarks = face_data['landmarks']
        roi_x1, roi_y1, roi_x2, roi_y2 = roi_coords

        # Convert ROI coordinates to original frame coordinates
        face_x1 = roi_x1 + face_bbox[0]
        face_y1 = roi_y1 + face_bbox[1]
        face_x2 = roi_x1 + face_bbox[2]
        face_y2 = roi_y1 + face_bbox[3]
        face_h = face_y2 - face_y1

        # Box prompt: lower 60% of face box
        box_y1 = face_y1 + int(face_h * (1 - config['face_lower_frac']))
        box_prompt = [face_x1, box_y1, face_x2, face_y2]

        # Positive points: mouth landmarks
        mouth_left = (roi_x1 + landmarks['mouth_left'][0], roi_y1 + landmarks['mouth_left'][1])
        mouth_right = (roi_x1 + landmarks['mouth_right'][0], roi_y1 + landmarks['mouth_right'][1])

        # Midpoint and 1/3, 2/3 points along mouth line
        mouth_center = ((mouth_left[0] + mouth_right[0]) // 2, (mouth_left[1] + mouth_right[1]) // 2)
        mouth_1_3 = ((2 * mouth_left[0] + mouth_right[0]) // 3, (2 * mouth_left[1] + mouth_right[1]) // 3)
        mouth_2_3 = ((mouth_left[0] + 2 * mouth_right[0]) // 3, (mouth_left[1] + 2 * mouth_right[1]) // 3)

        positive_points = [mouth_center, mouth_1_3, mouth_2_3]

        # Negative points
        nose_tip = (roi_x1 + landmarks['nose_tip'][0], roi_y1 + landmarks['nose_tip'][1])
        chin_point = (mouth_center[0], face_y2 + int(face_h * 0.08))  # Below chin

        # Cheek centers
        face_center_x = (face_x1 + face_x2) // 2
        cheek_y = (mouth_center[1] + nose_tip[1]) // 2
        left_cheek = (face_x1 + (face_center_x - face_x1) // 2, cheek_y)
        right_cheek = (face_x2 - (face_x2 - face_center_x) // 2, cheek_y)

        negative_points = [nose_tip, chin_point, left_cheek, right_cheek]

        return {
            'box_prompt': box_prompt,
            'positive_points': positive_points,
            'negative_points': negative_points,
            'face_bbox_original': [face_x1, face_y1, face_x2, face_y2]
        }

    def _segment_lips_sam_roi(self, frame: np.ndarray, sam_prompts: dict, config: dict) -> Optional[np.ndarray]:
        """Segment lips using SAM with mouth-specific prompts"""
        if self.sam_predictor is None:
            return None

        try:
            # Set image for SAM
            self.sam_predictor.set_image(frame)

            # Prepare prompts
            positive_points = np.array(sam_prompts['positive_points'])
            negative_points = np.array(sam_prompts['negative_points'])
            all_points = np.vstack([positive_points, negative_points])
            point_labels = np.array([1] * len(positive_points) + [0] * len(negative_points))

            box_prompt = np.array(sam_prompts['box_prompt'])

            # Run SAM prediction
            masks, scores, _ = self.sam_predictor.predict(
                point_coords=all_points,
                point_labels=point_labels,
                box=box_prompt,
                multimask_output=True
            )

            if len(masks) == 0:
                return None

            # Select best mask based on criteria
            best_mask = self._select_best_lip_mask_roi(masks, scores, sam_prompts, config)

            if best_mask is not None:
                # Post-process mask
                best_mask = self._postprocess_lip_mask(best_mask)

            return best_mask

        except Exception as e:
            self.logger.debug(f"SAM segmentation failed: {e}")
            return None

    def _select_best_lip_mask_roi(self, masks: np.ndarray, scores: np.ndarray, sam_prompts: dict, config: dict) -> Optional[np.ndarray]:
        """Select best mask based on IoU with lower-half box and distance to mouth center"""
        if len(masks) == 0:
            return None

        face_bbox = sam_prompts['face_bbox_original']
        mouth_center = sam_prompts['positive_points'][0]  # First positive point is mouth center

        # Create lower-half box for IoU calculation
        face_h = face_bbox[3] - face_bbox[1]
        lower_box_y1 = face_bbox[1] + face_h // 2
        lower_box = np.zeros_like(masks[0], dtype=bool)
        lower_box[lower_box_y1:face_bbox[3], face_bbox[0]:face_bbox[2]] = True

        best_mask = None
        best_score = -1

        for i, (mask, sam_score) in enumerate(zip(masks, scores)):
            # Calculate IoU with lower-half box
            intersection = np.sum(mask & lower_box)
            union = np.sum(mask | lower_box)
            iou = intersection / union if union > 0 else 0

            # Calculate distance to mouth center
            mask_coords = np.where(mask)
            if len(mask_coords[0]) == 0:
                continue

            mask_center_y = np.mean(mask_coords[0])
            mask_center_x = np.mean(mask_coords[1])
            distance = np.sqrt((mask_center_x - mouth_center[0])**2 + (mask_center_y - mouth_center[1])**2)

            # Normalize distance by face size
            face_size = max(face_bbox[2] - face_bbox[0], face_bbox[3] - face_bbox[1])
            norm_distance = distance / face_size if face_size > 0 else 1.0

            # Composite score: 60% IoU + 40% (1 - normalized distance)
            composite_score = 0.6 * iou + 0.4 * (1 - norm_distance)

            if composite_score > best_score:
                best_score = composite_score
                best_mask = mask

        return best_mask

    def _validate_lip_mask_roi(self, mask: np.ndarray, face_data: dict, config: dict) -> bool:
        """Validate mask to reject nostrils/chin/shadow"""
        if mask is None or np.sum(mask) == 0:
            return False

        face_bbox = face_data['face_bbox']  # In ROI coordinates
        face_h = face_bbox[3] - face_bbox[1]
        face_w = face_bbox[2] - face_bbox[0]
        face_area = face_h * face_w

        # Get mask properties
        mask_coords = np.where(mask)
        if len(mask_coords[0]) == 0:
            return False

        # Mask bounding box
        mask_y_min, mask_y_max = np.min(mask_coords[0]), np.max(mask_coords[0])
        mask_x_min, mask_x_max = np.min(mask_coords[1]), np.max(mask_coords[1])

        # Mask centroid in face coordinates
        mask_center_y = np.mean(mask_coords[0])
        centroid_y_frac = (mask_center_y - face_bbox[1]) / face_h

        # Mask dimensions
        mask_height = mask_y_max - mask_y_min
        mask_width = mask_x_max - mask_x_min
        aspect_ratio = mask_width / mask_height if mask_height > 0 else 0

        # Mask area fraction of face
        mask_area = np.sum(mask)
        area_frac_of_face = mask_area / face_area

        # Mask bbox width fraction of face
        bbox_width_frac_of_face = mask_width / face_w

        # Validation criteria
        valid_centroid = config['centroid_y_min'] <= centroid_y_frac <= config['centroid_y_max']
        valid_aspect = 1.6 <= aspect_ratio <= 6.0
        valid_area = 0.004 <= area_frac_of_face <= 0.07
        valid_width = 0.25 <= bbox_width_frac_of_face <= 0.65

        return valid_centroid and valid_aspect and valid_area and valid_width

    def _shift_sam_prompts_down(self, sam_prompts: dict, face_data: dict, config: dict) -> dict:
        """Shift SAM box prompt down by 6% of face height for retry"""
        shifted_prompts = sam_prompts.copy()
        face_bbox = face_data['face_bbox']
        face_h = face_bbox[3] - face_bbox[1]
        shift_amount = int(face_h * 0.06)

        # Shift box prompt down
        box_prompt = shifted_prompts['box_prompt'].copy()
        box_prompt[1] += shift_amount  # y1
        box_prompt[3] += shift_amount  # y2
        shifted_prompts['box_prompt'] = box_prompt

        return shifted_prompts

    def _get_mask_bbox(self, mask: np.ndarray) -> Optional[tuple]:
        """Get bounding box of mask"""
        if mask is None or np.sum(mask) == 0:
            return None

        coords = np.where(mask)
        if len(coords[0]) == 0:
            return None

        y_min, y_max = np.min(coords[0]), np.max(coords[0])
        x_min, x_max = np.min(coords[1]), np.max(coords[1])

        return (x_min, y_min, x_max, y_max)

    def _extract_crop_from_mask_roi(self, frame: np.ndarray, mask: np.ndarray, face_data: dict, config: dict) -> Optional[np.ndarray]:
        """Extract crop from mask with target scale and lipline anchoring"""
        mask_bbox = self._get_mask_bbox(mask)
        if mask_bbox is None:
            return None

        x_min, y_min, x_max, y_max = mask_bbox
        mask_w = x_max - x_min
        mask_h = y_max - y_min

        if mask_w <= 0 or mask_h <= 0:
            return None

        # Add padding
        pad_w = int(mask_w * config['pad_frac'])
        pad_h = int(mask_h * config['pad_frac'])

        crop_x1 = max(0, x_min - pad_w)
        crop_x2 = min(frame.shape[1], x_max + pad_w)
        crop_y1 = max(0, y_min - pad_h)
        crop_y2 = min(frame.shape[0], y_max + pad_h)

        crop_w = crop_x2 - crop_x1
        crop_h = crop_y2 - crop_y1

        if crop_w <= 0 or crop_h <= 0:
            return None

        # Enforce mask width ratio target through iterative adjustment
        current_mask_width_ratio = mask_w / crop_w
        target_ratio = config['target_mask_width_ratio']
        min_ratio = config['min_mask_width_ratio']
        max_ratio = config['max_mask_width_ratio']

        # Iterative shrink/expand (≤3 shrinks, ≤2 expands)
        shrink_count = 0
        expand_count = 0

        while (current_mask_width_ratio < min_ratio or current_mask_width_ratio > max_ratio) and \
              (shrink_count < 3 or expand_count < 2):

            if current_mask_width_ratio > max_ratio and shrink_count < 3:
                # Shrink crop
                shrink_amount = int(crop_w * 0.05)
                crop_x1 = max(0, crop_x1 + shrink_amount // 2)
                crop_x2 = min(frame.shape[1], crop_x2 - shrink_amount // 2)
                shrink_count += 1
            elif current_mask_width_ratio < min_ratio and expand_count < 2:
                # Expand crop
                expand_amount = int(crop_w * 0.05)
                crop_x1 = max(0, crop_x1 - expand_amount // 2)
                crop_x2 = min(frame.shape[1], crop_x2 + expand_amount // 2)
                expand_count += 1
            else:
                break

            crop_w = crop_x2 - crop_x1
            if crop_w <= 0:
                break
            current_mask_width_ratio = mask_w / crop_w

        # Anchor lipline at 0.42 of crop height
        mask_center_y = (y_min + y_max) // 2
        target_y_in_crop = int(crop_h * 0.42)
        current_y_in_crop = mask_center_y - crop_y1

        # Adjust crop position
        y_offset = target_y_in_crop - current_y_in_crop
        crop_y1 = max(0, crop_y1 - y_offset)
        crop_y2 = min(frame.shape[0], crop_y2 - y_offset)

        # Extract and resize crop
        crop = frame[crop_y1:crop_y2, crop_x1:crop_x2]
        if crop.size == 0:
            return None

        return cv2.resize(crop, (96, 96))

    def _fallback_supercrop_v2_roi(self, frame: np.ndarray, config: dict) -> np.ndarray:
        """Fallback to supercrop_v2 when ROI pipeline fails"""
        # Use simplified supercrop_v2 approach
        h, w = frame.shape[:2]

        # Top-middle region based on ROI coordinates
        roi_x1 = int(w * config['roi_x'][0])
        roi_x2 = int(w * config['roi_x'][1])
        roi_y1 = int(h * config['roi_y'][0])
        roi_y2 = int(h * config['roi_y'][1])

        # Extract ROI
        crop = frame[roi_y1:roi_y2, roi_x1:roi_x2]
        if crop.size == 0:
            # Emergency fallback: center crop
            center_x, center_y = w // 2, h // 2
            size = min(w, h) // 4
            crop = frame[center_y-size:center_y+size, center_x-size:center_x+size]

        if crop.size > 0:
            return cv2.resize(crop, (96, 96))
        else:
            # Last resort: return black frame
            return np.zeros((96, 96, 3), dtype=np.uint8)

    def _apply_temporal_smoothing_roi(self, processed_frames: list, frame_metrics: list, config: dict) -> list:
        """Apply temporal smoothing with median filter over window"""
        if len(processed_frames) < config['smooth_window']:
            return processed_frames

        smoothed_frames = []
        window_size = config['smooth_window']
        half_window = window_size // 2

        for i, frame in enumerate(processed_frames):
            if frame_metrics[i].get('has_mask', False):
                # Use original frame if it has a valid mask
                smoothed_frames.append(frame)
            else:
                # Find nearest valid frame for replacement
                valid_frame = None
                for offset in range(1, half_window + 1):
                    # Check backward
                    if i - offset >= 0 and frame_metrics[i - offset].get('has_mask', False):
                        valid_frame = processed_frames[i - offset]
                        frame_metrics[i]['reuse'] = True
                        break
                    # Check forward
                    if i + offset < len(processed_frames) and frame_metrics[i + offset].get('has_mask', False):
                        valid_frame = processed_frames[i + offset]
                        frame_metrics[i]['reuse'] = True
                        break

                if valid_frame is not None:
                    smoothed_frames.append(valid_frame)
                else:
                    smoothed_frames.append(frame)  # Keep original if no valid frame found

        return smoothed_frames

    def _calculate_lowerface_roi_clip_metrics(self, processed_frames: list, frame_metrics: list, config: dict) -> dict:
        """Calculate clip-level metrics for lower-face ROI processing"""
        if not frame_metrics:
            return {'valid': False, 'error_reason': 'no_frames'}

        # Count frames with masks
        frames_with_mask = sum(1 for m in frame_metrics if m.get('has_mask', False))
        frames_with_mask_pct = (frames_with_mask / len(frame_metrics)) * 100

        # Count fallback usage
        fallback_frames = sum(1 for m in frame_metrics if m.get('fallback', False))
        used_fallback_frac = (fallback_frames / len(frame_metrics)) * 100

        # Calculate motion score inside final crops
        motion_score = 0.0
        if len(processed_frames) > 1:
            motion_scores = []
            for i in range(1, len(processed_frames)):
                if processed_frames[i] is not None and processed_frames[i-1] is not None:
                    diff = cv2.absdiff(processed_frames[i], processed_frames[i-1])
                    motion_scores.append(np.mean(diff) / 255.0)
            motion_score = np.mean(motion_scores) if motion_scores else 0.0

        # Calculate average mask metrics
        mask_width_ratios = [m.get('mask_width_ratio', 0) for m in frame_metrics if m.get('has_mask', False)]
        mask_width_ratio_mean = np.mean(mask_width_ratios) if mask_width_ratios else 0.0

        centroid_y_fracs = [m.get('centroid_y_frac', 0) for m in frame_metrics if m.get('has_mask', False)]
        centroid_y_frac_mean = np.mean(centroid_y_fracs) if centroid_y_fracs else 0.0

        # Determine validity based on quality thresholds
        valid = (frames_with_mask_pct >= config['min_frames_with_mask_pct'] and
                motion_score >= config['motion_thresh'] and
                used_fallback_frac <= config['max_fallback_frac'])

        error_reason = None
        if not valid:
            if frames_with_mask_pct < config['min_frames_with_mask_pct']:
                error_reason = 'insufficient_mask_coverage'
            elif motion_score < config['motion_thresh']:
                error_reason = 'insufficient_motion'
            elif used_fallback_frac > config['max_fallback_frac']:
                error_reason = 'excessive_fallback_usage'

        return {
            'valid': valid,
            'error_reason': error_reason,
            'frames_with_mask_pct': frames_with_mask_pct,
            'motion_score': motion_score,
            'mask_width_ratio_mean': mask_width_ratio_mean,
            'centroid_y_frac_mean': centroid_y_frac_mean,
            'used_fallback_frac': used_fallback_frac,
            'total_frames': len(frame_metrics),
            'frames_with_face': sum(1 for m in frame_metrics if m.get('has_face', False)),
            'frames_with_mask': frames_with_mask,
            'fallback_frames': fallback_frames,
            'detector_fallback_frames': sum(1 for m in frame_metrics if m.get('detector_fallback', False)),
            'landmarks_estimated_frames': sum(1 for m in frame_metrics if m.get('landmarks_estimated', False)),
            'reuse_frames': sum(1 for m in frame_metrics if m.get('reuse', False))
        }

    def process_single_video(self, s3_key: str, output_name: str) -> Dict[str, any]:
        """Process a single video through the complete pipeline"""
        self.quality_stats['total_processed'] += 1

        # Download video to temporary file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            temp_path = tmp_file.name

        try:
            # Download from S3
            if not self.s3_manager.download_video(s3_key, temp_path):
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'download_failed'}

            # Extract frames
            frames = self.extract_frames_from_video(temp_path)
            if frames is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'frame_extraction_failed'}

            # Process frames based on selected mode
            if self.mode == 'top_middle_supercrop_v2':
                processed_frames, validation_results, clip_metrics = self.extract_top_middle_supercrop_v2(frames)
            elif self.mode == 'yolo_sam_lips':
                processed_frames, validation_results, clip_metrics = self.extract_yolo_sam_lips(frames)
            elif self.mode == 'lowerface_roi_retinaface_sam':
                processed_frames, validation_results, clip_metrics = self.extract_lowerface_roi_retinaface_sam(frames)
            else:
                # Default: landmark-anchored, scale-controlled cropping
                processed_frames, validation_results, clip_metrics = self.extract_landmark_anchored_lip_crop(frames)

            if not processed_frames:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'no_frames_processed'}

            # Convert to numpy array - all frames should now have the same shape
            try:
                processed_frames = np.array(processed_frames)
            except ValueError as e:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': f'frame_array_creation_failed: {e}'}

            # Auto-scoring system (mode-specific)
            frames_with_landmarks = sum(1 for r in validation_results if r['has_landmarks'])
            valid_frames = sum(1 for r in validation_results if r['valid'])
            total_frames = len(validation_results)
            avg_confidence = np.mean([r['confidence'] for r in validation_results]) if validation_results else 0

            if self.mode == 'top_middle_supercrop_v2':
                # Supercrop v2 quality assessment
                is_high_quality = clip_metrics.get('valid', False)
                mean_score = clip_metrics.get('mean_score', 0)
                motion_score = clip_metrics.get('motion_score', 0)
                edge_var = clip_metrics.get('edge_var', 0)

                if not is_high_quality:
                    self.quality_stats['invalid_crop'] += 1
                    error_reason = 'low_motion_or_edge_variance'

                    quality_info = {
                        'success': False,
                        'error': error_reason,
                        'mean_score': mean_score,
                        'motion_score': motion_score,
                        'edge_var': edge_var,
                        'avg_confidence': avg_confidence,
                        'valid_frames': valid_frames,
                        'total_frames': total_frames,
                        'is_high_quality': False,
                        'clip_metrics': clip_metrics
                    }
                else:
                    quality_info = {
                        'success': True,
                        'mean_score': mean_score,
                        'motion_score': motion_score,
                        'edge_var': edge_var,
                        'avg_confidence': avg_confidence,
                        'valid_frames': valid_frames,
                        'total_frames': total_frames,
                        'is_high_quality': True,
                        'clip_metrics': clip_metrics
                    }
            elif self.mode == 'yolo_sam_lips':
                # YOLO+SAM quality assessment
                is_high_quality = clip_metrics.get('valid', False)
                frames_with_mask_pct = clip_metrics.get('frames_with_mask_pct', 0)
                motion_score = clip_metrics.get('motion_score', 0)
                lip_coverage = clip_metrics.get('lip_coverage', 0)

                if not is_high_quality:
                    self.quality_stats['invalid_crop'] += 1
                    error_reason = clip_metrics.get('error_reason', 'low_mask_coverage_or_motion')

                    quality_info = {
                        'success': False,
                        'error': error_reason,
                        'frames_with_mask_pct': frames_with_mask_pct,
                        'motion_score': motion_score,
                        'lip_coverage': lip_coverage,
                        'avg_confidence': avg_confidence,
                        'valid_frames': valid_frames,
                        'total_frames': total_frames,
                        'is_high_quality': False,
                        'clip_metrics': clip_metrics
                    }
                else:
                    quality_info = {
                        'success': True,
                        'frames_with_mask_pct': frames_with_mask_pct,
                        'motion_score': motion_score,
                        'lip_coverage': lip_coverage,
                        'avg_confidence': avg_confidence,
                        'valid_frames': valid_frames,
                        'total_frames': total_frames,
                        'is_high_quality': True,
                        'clip_metrics': clip_metrics
                    }
            else:
                # Landmark-anchored processing
                frames_with_landmarks_pct = clip_metrics['frames_with_landmarks_pct']
                is_high_quality = clip_metrics['valid_clip']
                avg_coverage_after = clip_metrics['avg_coverage_after']
                avg_mouth_width = clip_metrics['avg_mouth_width']

                if not is_high_quality:
                    # Mark as low quality but still process (for logging)
                    self.quality_stats['invalid_crop'] += 1
                    error_reason = 'low_landmark_coverage'

                    # Still process the clip but mark as low quality
                    quality_info = {
                        'success': False,
                        'error': error_reason,
                        'frames_with_landmarks_pct': frames_with_landmarks_pct,
                        'avg_coverage_after': avg_coverage_after,
                        'avg_mouth_width': avg_mouth_width,
                        'avg_confidence': avg_confidence,
                        'valid_frames': valid_frames,
                        'total_frames': total_frames,
                        'is_high_quality': False,
                        'clip_metrics': clip_metrics
                    }
                else:
                    # High quality clip (≥90% landmark coverage)
                    quality_info = {
                        'success': True,
                        'frames_with_landmarks_pct': frames_with_landmarks_pct,
                        'avg_coverage_after': avg_coverage_after,
                        'avg_mouth_width': avg_mouth_width,
                        'avg_confidence': avg_confidence,
                        'valid_frames': valid_frames,
                        'total_frames': total_frames,
                        'is_high_quality': True,
                        'clip_metrics': clip_metrics
                    }

            # Standardize clip
            standardized_clip = self.standardize_clip(processed_frames)
            if standardized_clip is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'standardization_failed'}

            # Save processed clip
            output_path = os.path.join(self.processed_data_dir, 'clips', f"{output_name}.npy")
            save_frames_as_npy(standardized_clip, output_path)

            self.quality_stats['successful'] += 1

            # Update quality info with processing details
            quality_info.update({
                'output_path': output_path,
                'original_frames': len(frames),
                'processed_frames': len(standardized_clip),
                'has_landmarks': frames_with_landmarks > 0
            })

            return quality_info

        except Exception as e:
            self.logger.error(f"❌ Error processing {s3_key}: {e}")
            self.quality_stats['processing_error'] += 1
            return {'success': False, 'error': str(e)}

        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def process_from_manifest(self, manifest_path: str, limit_per_class: Optional[int] = None):
        """Process all videos listed in a manifest file"""
        self.logger.info(f"📋 Processing videos from manifest: {manifest_path}")

        # Load manifest
        df = pd.read_csv(manifest_path)

        # Apply limit per class if specified
        if limit_per_class is not None:
            self.logger.info(f"🔢 Limiting to {limit_per_class} samples per class")
            limited_dfs = []
            for phrase in df['phrase'].unique():
                phrase_df = df[df['phrase'] == phrase].head(limit_per_class)
                limited_dfs.append(phrase_df)
            df = pd.concat(limited_dfs, ignore_index=True)
            self.logger.info(f"📊 Limited dataset size: {len(df)} samples")

        # Process each video
        results = []

        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing videos"):
            s3_key = row['s3_key']
            phrase = row['phrase']
            speaker_id = row['speaker_id']

            # Generate output name
            output_name = f"{phrase}_{speaker_id}_{idx:06d}"

            # Process video
            result = self.process_single_video(s3_key, output_name)

            # Add metadata
            result.update({
                'manifest_idx': idx,
                's3_key': s3_key,
                'phrase': phrase,
                'speaker_id': speaker_id,
                'output_name': output_name
            })

            # Record quality data for CSV (mode-specific)
            if self.mode == 'top_middle_supercrop_v2':
                # Supercrop v2 quality recording
                clip_metrics = result.get('clip_metrics', {})
                frame_metrics = clip_metrics.get('frame_metrics', [])

                # Calculate average metrics across frames
                avg_cx = np.mean([m.get('final_cx', m.get('cx', 0)) for m in frame_metrics]) if frame_metrics else 0
                avg_cy = np.mean([m.get('final_cy', m.get('cy', 0)) for m in frame_metrics]) if frame_metrics else 0
                avg_width = np.mean([m.get('final_width', m.get('width', 0)) for m in frame_metrics]) if frame_metrics else 0
                avg_score = np.mean([m.get('score', 0) for m in frame_metrics]) if frame_metrics else 0
                avg_edge_energy = np.mean([m.get('edge_energy', 0) for m in frame_metrics]) if frame_metrics else 0
                avg_redness = np.mean([m.get('redness', 0) for m in frame_metrics]) if frame_metrics else 0
                avg_dark_ratio = np.mean([m.get('dark_ratio', 0) for m in frame_metrics]) if frame_metrics else 0

                # Get chosen width fraction (from first frame)
                chosen_w_frac = frame_metrics[0].get('width', 0) / 400 if frame_metrics and len(frame_metrics) > 0 else 0  # Assuming 400px width

                quality_record = {
                    'clip_id': output_name,
                    'phrase': phrase,
                    'speaker_id': speaker_id,
                    'cx': avg_cx,
                    'cy': avg_cy,
                    'w_frac': avg_width / 400 if avg_width > 0 else 0,  # Assuming 400px width
                    'chosen_w_frac': chosen_w_frac,
                    'edge_energy': avg_edge_energy,
                    'redness': avg_redness,
                    'dark_ratio': avg_dark_ratio,
                    'score': avg_score,
                    'motion_score': clip_metrics.get('motion_score', 0),
                    'edge_var': clip_metrics.get('edge_var', 0),
                    'valid': clip_metrics.get('valid', False),
                    'success': result.get('success', False),
                    'error': result.get('error', '')
                }
                self.quality_records.append(quality_record)

            elif self.mode == 'yolo_sam_lips':
                # YOLO+SAM quality recording
                clip_metrics = result.get('clip_metrics', {})
                frame_metrics = clip_metrics.get('frame_metrics', [])

                # Calculate average metrics across frames
                avg_face_area = np.mean([m.get('face_area', 0) for m in frame_metrics]) if frame_metrics else 0
                avg_mask_area = np.mean([m.get('mask_area', 0) for m in frame_metrics]) if frame_metrics else 0
                avg_mask_width = np.mean([m.get('mask_width', 0) for m in frame_metrics]) if frame_metrics else 0
                avg_crop_w = np.mean([m.get('crop_w', 0) for m in frame_metrics]) if frame_metrics else 0
                avg_crop_h = np.mean([m.get('crop_h', 0) for m in frame_metrics]) if frame_metrics else 0
                frames_with_face = sum([1 for m in frame_metrics if m.get('has_face', False)]) if frame_metrics else 0
                frames_with_mask = sum([1 for m in frame_metrics if m.get('has_mask', False)]) if frame_metrics else 0
                fallback_frames = sum([1 for m in frame_metrics if m.get('fallback', False)]) if frame_metrics else 0

                quality_record = {
                    'clip_id': output_name,
                    'phrase': phrase,
                    'speaker_id': speaker_id,
                    'frames_with_mask_pct': (frames_with_mask / len(frame_metrics) * 100) if frame_metrics else 0,
                    'motion_score': clip_metrics.get('motion_score', 0),
                    'mask_area_mean': avg_mask_area,
                    'mask_width_ratio_mean': avg_mask_width / 96 if avg_mask_width > 0 else 0,  # Assuming 96px crop
                    'crop_w': avg_crop_w,
                    'crop_h': avg_crop_h,
                    'face_area_mean': avg_face_area,
                    'frames_with_face': frames_with_face,
                    'frames_with_mask': frames_with_mask,
                    'fallback_frames': fallback_frames,
                    'valid': clip_metrics.get('valid', False),
                    'success': result.get('success', False),
                    'error': result.get('error', '')
                }
                self.quality_records.append(quality_record)

            elif 'frames_with_landmarks_pct' in result:
                # Landmark-anchored approach quality recording
                clip_metrics = result.get('clip_metrics', {})
                frame_metrics = clip_metrics.get('frame_metrics', [])

                # Calculate average metrics across frames
                avg_mouth_width = np.mean([m.get('mouth_width_px', 0) for m in frame_metrics if m.get('mouth_width_px', 0) > 0]) if frame_metrics else 0
                avg_crop_w = np.mean([m.get('crop_w_px', 0) for m in frame_metrics if m.get('crop_w_px', 0) > 0]) if frame_metrics else 0
                avg_target_ratio = np.mean([m.get('target_ratio', 0) for m in frame_metrics if m.get('target_ratio', 0) > 0]) if frame_metrics else 0
                avg_coverage_before = np.mean([m.get('coverage_before', 0) for m in frame_metrics]) if frame_metrics else 0
                avg_coverage_after = np.mean([m.get('coverage_after', 0) for m in frame_metrics]) if frame_metrics else 0
                total_adjustments = sum([m.get('adjustments_made', 0) for m in frame_metrics]) if frame_metrics else 0

                quality_record = {
                    'clip_id': output_name,
                    'phrase': phrase,
                    'speaker_id': speaker_id,
                    'valid': result.get('is_high_quality', False),
                    'frames_with_landmarks_pct': result.get('frames_with_landmarks_pct', 0),
                    'mouth_width_px': avg_mouth_width,
                    'crop_w_px': avg_crop_w,
                    'target_ratio': avg_target_ratio,
                    'coverage_before': avg_coverage_before,
                    'coverage_after': avg_coverage_after,
                    'adjustments_made': total_adjustments,
                    'avg_confidence': result.get('avg_confidence', 0),
                    'valid_frames': result.get('valid_frames', 0),
                    'total_frames': result.get('total_frames', 0),
                    'success': result.get('success', False),
                    'error': result.get('error', '')
                }
                self.quality_records.append(quality_record)

            results.append(result)

        # Save processing log
        log_name = os.path.basename(manifest_path).replace('.csv', '_processing_log.csv')
        log_path = os.path.join(self.processed_data_dir, 'quality_logs', log_name)

        results_df = pd.DataFrame(results)
        results_df.to_csv(log_path, index=False)

        # Save quality CSV with auto-scoring results
        quality_csv_path = os.path.join(self.processed_data_dir, 'quality_logs', 'quality_scores.csv')
        self._save_quality_csv(quality_csv_path)

        # Print statistics
        self._print_processing_stats()

        self.logger.info(f"✅ Processing completed. Log saved to: {log_path}")
        self.logger.info(f"📊 Quality scores saved to: {quality_csv_path}")

    def _save_quality_csv(self, output_path="data_processed/quality_logs/quality_scores.csv"):
        """Save quality records to CSV"""
        if not self.quality_records:
            return

        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        import pandas as pd
        df = pd.DataFrame(self.quality_records)
        df.to_csv(output_path, index=False)

        self.logger.info(f"💾 Quality scores saved to: {output_path}")

        # Print summary statistics (mode-specific)
        if self.mode == 'top_middle_supercrop_v2':
            high_quality_count = len(df[df['valid'] == True])
            total_count = len(df)
            avg_score = df['score'].mean() if 'score' in df.columns else 0
            avg_motion = df['motion_score'].mean() if 'motion_score' in df.columns else 0
            avg_edge_var = df['edge_var'].mean() if 'edge_var' in df.columns else 0
        else:
            high_quality_count = len(df[df['valid'] == True]) if 'valid' in df.columns else 0
            total_count = len(df)
            avg_landmark_coverage = df['frames_with_landmarks_pct'].mean() if 'frames_with_landmarks_pct' in df.columns else 0

        print(f"\n📊 Quality Summary:")
        print("=" * 50)
        print(f"Total clips processed: {total_count}")
        print(f"High quality clips:    {high_quality_count} ({high_quality_count/total_count*100:.1f}%)")
        print(f"Low quality clips:     {total_count-high_quality_count} ({(total_count-high_quality_count)/total_count*100:.1f}%)")

        if self.mode == 'top_middle_supercrop_v2':
            print(f"Avg lipness score:     {avg_score:.3f}")
            print(f"Avg motion score:      {avg_motion:.6f}")
            print(f"Avg edge variance:     {avg_edge_var:.6f}")
        else:
            print(f"Avg landmark coverage: {avg_landmark_coverage:.1f}%")

        print("=" * 50)

    def _print_processing_stats(self):
        """Print processing statistics"""
        total = self.quality_stats['total_processed']

        print(f"\n📊 Processing Statistics")
        print("=" * 50)
        print(f"Total processed:     {total:6d}")
        print(f"Successful:          {self.quality_stats['successful']:6d} ({self.quality_stats['successful']/total*100:.1f}%)")
        print(f"Invalid crops:       {self.quality_stats['invalid_crop']:6d} ({self.quality_stats['invalid_crop']/total*100:.1f}%)")
        print(f"No landmarks:        {self.quality_stats['no_landmarks']:6d} ({self.quality_stats['no_landmarks']/total*100:.1f}%)")
        print(f"Low confidence:      {self.quality_stats['low_confidence']:6d} ({self.quality_stats['low_confidence']/total*100:.1f}%)")
        print(f"Processing errors:   {self.quality_stats['processing_error']:6d} ({self.quality_stats['processing_error']/total*100:.1f}%)")
        print("=" * 50)


def main():
    parser = argparse.ArgumentParser(description='Preprocess videos using YOLO→SAM pipeline')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--manifest', required=True, help='Manifest CSV file to process')
    parser.add_argument('--limit_per_class', type=int, help='Limit number of samples per class (for testing)')
    parser.add_argument('--mode', default='landmark_anchored',
                       choices=['landmark_anchored', 'top_middle_supercrop_v2', 'yolo_sam_lips', 'lowerface_roi_retinaface_sam'],
                       help='Preprocessing mode to use')
    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)

    # Set seed for reproducibility
    set_seed(config.get('seed', 42))

    # Process videos
    preprocessor = VideoPreprocessor(config, mode=args.mode)
    preprocessor.process_from_manifest(args.manifest, limit_per_class=args.limit_per_class)


if __name__ == "__main__":
    main()
