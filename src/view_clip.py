#!/usr/bin/env python3
"""
View processed clips to verify lip cropping quality
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import random
import glob
import argparse


def load_clip(clip_path):
    """Load a processed clip"""
    try:
        clip = np.load(clip_path)
        return clip
    except Exception as e:
        print(f"Error loading {clip_path}: {e}")
        return None


def display_clip_frames(clip, clip_name, num_frames=8):
    """Display frames from a clip"""
    if clip is None:
        return

    num_frames = min(num_frames, len(clip))

    # Create subplot grid
    cols = 4
    rows = (num_frames + cols - 1) // cols

    fig, axes = plt.subplots(rows, cols, figsize=(12, 3*rows))
    fig.suptitle(f'Clip: {clip_name}\nShape: {clip.shape}, Range: [{clip.min():.3f}, {clip.max():.3f}]',
                 fontsize=14)

    # Flatten axes for easier indexing
    if rows == 1:
        axes = [axes] if cols == 1 else axes
    else:
        axes = axes.flatten()

    for i in range(num_frames):
        frame = clip[i]

        # Convert to uint8 for display
        if frame.dtype == np.float32:
            display_frame = (frame * 255).astype(np.uint8)
        else:
            display_frame = frame

        axes[i].imshow(display_frame)
        axes[i].set_title(f'Frame {i+1}')
        axes[i].axis('off')

    # Hide unused subplots
    for i in range(num_frames, len(axes)):
        axes[i].axis('off')

    plt.tight_layout()
    plt.show()


def view_random_clips(clips_dir, num_clips=5, num_frames=8):
    """View random clips from the processed directory"""

    # Find all clip files
    clip_files = glob.glob(os.path.join(clips_dir, "*.npy"))

    if not clip_files:
        print(f"No clips found in {clips_dir}")
        return

    print(f"Found {len(clip_files)} clips")

    # Select random clips
    selected_clips = random.sample(clip_files, min(num_clips, len(clip_files)))

    for clip_path in selected_clips:
        clip_name = os.path.basename(clip_path)
        print(f"\nViewing: {clip_name}")

        clip = load_clip(clip_path)
        if clip is not None:
            display_clip_frames(clip, clip_name, num_frames)


def view_specific_clip(clip_path, num_frames=16):
    """View a specific clip"""
    if not os.path.exists(clip_path):
        print(f"Clip not found: {clip_path}")
        return

    clip_name = os.path.basename(clip_path)
    print(f"Viewing: {clip_name}")

    clip = load_clip(clip_path)
    if clip is not None:
        display_clip_frames(clip, clip_name, num_frames)


def analyze_clips_stats(clips_dir):
    """Analyze statistics of all clips"""
    clip_files = glob.glob(os.path.join(clips_dir, "*.npy"))

    if not clip_files:
        print(f"No clips found in {clips_dir}")
        return

    print(f"Analyzing {len(clip_files)} clips...")

    shapes = []
    dtypes = []
    value_ranges = []

    for clip_path in clip_files[:100]:  # Sample first 100 clips
        try:
            clip = np.load(clip_path)
            shapes.append(clip.shape)
            dtypes.append(str(clip.dtype))
            value_ranges.append((clip.min(), clip.max()))
        except Exception as e:
            print(f"Error loading {clip_path}: {e}")

    # Print statistics
    print(f"\n📊 Clip Statistics (sample of {len(shapes)} clips):")
    print("=" * 50)

    # Shapes
    unique_shapes = list(set(shapes))
    print(f"Shapes: {unique_shapes}")

    # Data types
    unique_dtypes = list(set(dtypes))
    print(f"Data types: {unique_dtypes}")

    # Value ranges
    if value_ranges:
        min_vals = [r[0] for r in value_ranges]
        max_vals = [r[1] for r in value_ranges]
        print(f"Value range: [{min(min_vals):.3f}, {max(max_vals):.3f}]")
        print(f"Average range: [{np.mean(min_vals):.3f}, {np.mean(max_vals):.3f}]")

    print("=" * 50)


def main():
    parser = argparse.ArgumentParser(description='View processed lipreading clips')
    parser.add_argument('--clips_dir', default='data_processed/clips',
                       help='Directory containing processed clips')
    parser.add_argument('--clip', type=str, help='Specific clip file to view')
    parser.add_argument('--num_clips', type=int, default=5,
                       help='Number of random clips to view')
    parser.add_argument('--num_frames', type=int, default=8,
                       help='Number of frames to display per clip')
    parser.add_argument('--stats', action='store_true',
                       help='Show statistics about all clips')

    args = parser.parse_args()

    if args.stats:
        analyze_clips_stats(args.clips_dir)
    elif args.clip:
        view_specific_clip(args.clip, args.num_frames)
    else:
        view_random_clips(args.clips_dir, args.num_clips, args.num_frames)


if __name__ == "__main__":
    main()
