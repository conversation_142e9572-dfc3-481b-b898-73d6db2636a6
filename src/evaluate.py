"""
Comprehensive Evaluation for ICU Lipreading Model
Macro-F1, per-class metrics, confusion matrices, and detailed reporting
"""

import os
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, f1_score, precision_score, recall_score,
    classification_report, confusion_matrix, roc_auc_score
)
from sklearn.preprocessing import label_binarize
import argparse
from tqdm import tqdm

from utils import setup_logging, load_config, get_device, create_directories
from dataset import create_data_loaders
from model import create_model


class LipreadingEvaluator:
    """Comprehensive evaluation for lipreading model"""
    
    def __init__(self, config: dict, checkpoint_path: str):
        self.config = config
        self.logger = setup_logging(
            config.get('logging', {}).get('level', 'INFO'),
            config.get('logging', {}).get('log_dir', './logs')
        )
        
        self.target_phrases = config['data']['target_phrases']
        self.num_classes = len(self.target_phrases)
        self.device = get_device()
        
        # Create output directory
        self.results_dir = os.path.join(config['data']['manifests_dir'], 'evaluation_results')
        create_directories([self.results_dir])
        
        # Load model
        self._load_model(checkpoint_path)
        
        # Setup data loaders
        self._setup_data_loaders()
    
    def _load_model(self, checkpoint_path: str):
        """Load trained model from checkpoint"""
        self.logger.info(f"🤖 Loading model from {checkpoint_path}")
        
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
        
        # Create model
        self.model = create_model(self.config)
        
        # Load checkpoint
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.to(self.device)
        self.model.eval()
        
        # Log checkpoint info
        epoch = checkpoint.get('epoch', 'unknown')
        best_score = checkpoint.get('best_val_score', 'unknown')
        self.logger.info(f"✅ Loaded model from epoch {epoch}, best val score: {best_score}")
    
    def _setup_data_loaders(self):
        """Setup data loaders"""
        self.logger.info("📊 Setting up data loaders...")
        
        self.train_loader, self.val_loader, self.test_loader = create_data_loaders(
            self.config, batch_size=self.config['training']['batch_size']
        )
    
    def predict_dataset(self, data_loader, dataset_name: str) -> tuple:
        """Get predictions for entire dataset"""
        self.logger.info(f"🔮 Getting predictions for {dataset_name} set...")
        
        all_predictions = []
        all_labels = []
        all_probabilities = []
        all_metadata = []
        
        with torch.no_grad():
            for clips, labels, metadata in tqdm(data_loader, desc=f"Predicting {dataset_name}"):
                clips = clips.to(self.device)
                
                # Forward pass
                outputs = self.model(clips)
                probabilities = torch.softmax(outputs, dim=1)
                predictions = torch.argmax(outputs, dim=1)
                
                # Collect results
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
                all_metadata.extend(metadata)
        
        return (
            np.array(all_predictions),
            np.array(all_labels),
            np.array(all_probabilities),
            all_metadata
        )
    
    def calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, 
                         y_prob: np.ndarray) -> dict:
        """Calculate comprehensive metrics"""
        
        # Basic metrics
        accuracy = accuracy_score(y_true, y_pred)
        macro_f1 = f1_score(y_true, y_pred, average='macro')
        weighted_f1 = f1_score(y_true, y_pred, average='weighted')
        
        macro_precision = precision_score(y_true, y_pred, average='macro')
        macro_recall = recall_score(y_true, y_pred, average='macro')
        
        # Per-class metrics
        per_class_precision = precision_score(y_true, y_pred, average=None)
        per_class_recall = recall_score(y_true, y_pred, average=None)
        per_class_f1 = f1_score(y_true, y_pred, average=None)
        
        # Classification report
        class_report = classification_report(
            y_true, y_pred,
            target_names=self.target_phrases,
            output_dict=True,
            zero_division=0
        )
        
        # Confusion matrix
        conf_matrix = confusion_matrix(y_true, y_pred)
        
        # ROC AUC (one-vs-rest)
        try:
            y_true_bin = label_binarize(y_true, classes=range(self.num_classes))
            if y_true_bin.shape[1] == 1:  # Binary case
                y_true_bin = np.hstack([1 - y_true_bin, y_true_bin])
                y_prob_roc = np.column_stack([1 - y_prob[:, 1], y_prob[:, 1]])
            else:
                y_prob_roc = y_prob
            
            macro_auc = roc_auc_score(y_true_bin, y_prob_roc, average='macro')
            per_class_auc = roc_auc_score(y_true_bin, y_prob_roc, average=None)
        except Exception as e:
            self.logger.warning(f"Could not calculate AUC: {e}")
            macro_auc = 0.0
            per_class_auc = np.zeros(self.num_classes)
        
        return {
            'accuracy': accuracy,
            'macro_f1': macro_f1,
            'weighted_f1': weighted_f1,
            'macro_precision': macro_precision,
            'macro_recall': macro_recall,
            'macro_auc': macro_auc,
            'per_class_precision': per_class_precision,
            'per_class_recall': per_class_recall,
            'per_class_f1': per_class_f1,
            'per_class_auc': per_class_auc,
            'confusion_matrix': conf_matrix,
            'classification_report': class_report
        }
    
    def plot_confusion_matrix(self, conf_matrix: np.ndarray, dataset_name: str):
        """Plot and save confusion matrix"""
        plt.figure(figsize=(12, 10))
        
        # Normalize confusion matrix
        conf_matrix_norm = conf_matrix.astype('float') / conf_matrix.sum(axis=1)[:, np.newaxis]
        
        # Create heatmap
        sns.heatmap(
            conf_matrix_norm,
            annot=True,
            fmt='.2f',
            cmap='Blues',
            xticklabels=self.target_phrases,
            yticklabels=self.target_phrases,
            cbar_kws={'label': 'Normalized Count'}
        )
        
        plt.title(f'Confusion Matrix - {dataset_name} Set')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()
        
        # Save plot
        plot_path = os.path.join(self.results_dir, f'confusion_matrix_{dataset_name.lower()}.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"📊 Saved confusion matrix to {plot_path}")
    
    def create_detailed_report(self, metrics: dict, dataset_name: str) -> pd.DataFrame:
        """Create detailed per-class report"""
        
        report_data = []
        
        for i, phrase in enumerate(self.target_phrases):
            report_data.append({
                'phrase': phrase,
                'precision': metrics['per_class_precision'][i],
                'recall': metrics['per_class_recall'][i],
                'f1_score': metrics['per_class_f1'][i],
                'auc': metrics['per_class_auc'][i] if len(metrics['per_class_auc']) > i else 0.0,
                'support': metrics['classification_report'][phrase]['support']
            })
        
        # Add overall metrics
        report_data.append({
            'phrase': 'MACRO_AVG',
            'precision': metrics['macro_precision'],
            'recall': metrics['macro_recall'],
            'f1_score': metrics['macro_f1'],
            'auc': metrics['macro_auc'],
            'support': sum(metrics['classification_report'][phrase]['support'] 
                          for phrase in self.target_phrases)
        })
        
        report_df = pd.DataFrame(report_data)
        
        # Save report
        report_path = os.path.join(self.results_dir, f'detailed_report_{dataset_name.lower()}.csv')
        report_df.to_csv(report_path, index=False)
        
        return report_df
    
    def print_summary(self, metrics: dict, dataset_name: str):
        """Print formatted summary"""
        min_recall_threshold = self.config['evaluation']['min_class_recall']
        
        print(f"\n{'='*60}")
        print(f"{dataset_name.upper()} SET EVALUATION RESULTS")
        print(f"{'='*60}")
        
        print(f"Overall Metrics:")
        print(f"  Accuracy:      {metrics['accuracy']:.4f}")
        print(f"  Macro F1:      {metrics['macro_f1']:.4f}")
        print(f"  Weighted F1:   {metrics['weighted_f1']:.4f}")
        print(f"  Macro AUC:     {metrics['macro_auc']:.4f}")
        
        print(f"\nPer-Class Performance:")
        print(f"{'Phrase':<20} {'Precision':<10} {'Recall':<10} {'F1':<10} {'Support':<10}")
        print("-" * 70)
        
        low_recall_classes = []
        
        for i, phrase in enumerate(self.target_phrases):
            precision = metrics['per_class_precision'][i]
            recall = metrics['per_class_recall'][i]
            f1 = metrics['per_class_f1'][i]
            support = metrics['classification_report'][phrase]['support']
            
            print(f"{phrase:<20} {precision:<10.3f} {recall:<10.3f} {f1:<10.3f} {support:<10d}")
            
            if recall < min_recall_threshold:
                low_recall_classes.append((phrase, recall))
        
        # Highlight problematic classes
        if low_recall_classes:
            print(f"\n⚠️  Classes below {min_recall_threshold:.2f} recall threshold:")
            for phrase, recall in low_recall_classes:
                print(f"  {phrase}: {recall:.3f}")
        else:
            print(f"\n✅ All classes above {min_recall_threshold:.2f} recall threshold")
        
        # Success criteria check
        success_criteria = {
            'accuracy_80': metrics['accuracy'] >= 0.80,
            'macro_f1_80': metrics['macro_f1'] >= 0.80,
            'min_recall_70': all(r >= 0.70 for r in metrics['per_class_recall'])
        }
        
        print(f"\n🎯 Success Criteria:")
        print(f"  ≥80% Accuracy:     {'✅' if success_criteria['accuracy_80'] else '❌'} ({metrics['accuracy']:.1%})")
        print(f"  ≥80% Macro F1:     {'✅' if success_criteria['macro_f1_80'] else '❌'} ({metrics['macro_f1']:.1%})")
        print(f"  ≥70% Min Recall:   {'✅' if success_criteria['min_recall_70'] else '❌'} ({min(metrics['per_class_recall']):.1%})")
        
        overall_success = all(success_criteria.values())
        print(f"\n🏆 Overall Success: {'✅ PASSED' if overall_success else '❌ FAILED'}")
    
    def evaluate_all_sets(self):
        """Evaluate on all datasets"""
        self.logger.info("🚀 Starting comprehensive evaluation...")
        
        datasets = [
            (self.val_loader, 'Validation'),
            (self.test_loader, 'Test')
        ]
        
        all_results = {}
        
        for data_loader, dataset_name in datasets:
            self.logger.info(f"📊 Evaluating {dataset_name} set...")
            
            # Get predictions
            y_pred, y_true, y_prob, metadata = self.predict_dataset(data_loader, dataset_name)
            
            # Calculate metrics
            metrics = self.calculate_metrics(y_true, y_pred, y_prob)
            
            # Create visualizations
            self.plot_confusion_matrix(metrics['confusion_matrix'], dataset_name)
            
            # Create detailed report
            detailed_report = self.create_detailed_report(metrics, dataset_name)
            
            # Print summary
            self.print_summary(metrics, dataset_name)
            
            # Store results
            all_results[dataset_name.lower()] = {
                'metrics': metrics,
                'predictions': y_pred,
                'true_labels': y_true,
                'probabilities': y_prob,
                'metadata': metadata,
                'detailed_report': detailed_report
            }
        
        # Save comprehensive results
        self._save_comprehensive_results(all_results)
        
        self.logger.info("🎉 Evaluation completed!")
        
        return all_results
    
    def _save_comprehensive_results(self, results: dict):
        """Save all results to files"""
        
        # Create summary comparison
        summary_data = []
        
        for dataset_name, result in results.items():
            metrics = result['metrics']
            summary_data.append({
                'dataset': dataset_name,
                'accuracy': metrics['accuracy'],
                'macro_f1': metrics['macro_f1'],
                'weighted_f1': metrics['weighted_f1'],
                'macro_precision': metrics['macro_precision'],
                'macro_recall': metrics['macro_recall'],
                'macro_auc': metrics['macro_auc'],
                'min_recall': min(metrics['per_class_recall']),
                'max_recall': max(metrics['per_class_recall'])
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_path = os.path.join(self.results_dir, 'evaluation_summary.csv')
        summary_df.to_csv(summary_path, index=False)
        
        self.logger.info(f"📊 Saved evaluation summary to {summary_path}")


def main():
    parser = argparse.ArgumentParser(description='Evaluate ICU Lipreading Model')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--checkpoint', default='checkpoints/best_model.pth', help='Model checkpoint path')
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Create evaluator and run evaluation
    evaluator = LipreadingEvaluator(config, args.checkpoint)
    results = evaluator.evaluate_all_sets()


if __name__ == "__main__":
    main()
