#!/usr/bin/env python3
"""
Lower-face ROI RetinaFace+SAM Trial Runner
QC MANDATORY - No full run yet

This script runs a trial on 100 clips (stratified: ≥10 per phrase) and produces:
1) qc_lowerface_roi_side_by_side.png — 20 clips with original+ROI+mask vs final crop
2) qc_lowerface_roi_preview.png — 10 clips × 4 frames (final crops)
3) qc_lowerface_roi_failures.png — worst 12 with failure reasons
4) CSV: data_processed_trial_lowerface_roi/quality.csv
"""

import os
import sys
import pandas as pd
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
from collections import defaultdict
import argparse

# Add src to path
sys.path.append('src')
from preprocess import VideoPreprocessor
from utils import load_config, setup_logging


def create_trial_manifest(full_manifest_path: str, output_path: str, target_clips: int = 100):
    """Create stratified trial manifest with ≥10 clips per phrase"""
    print(f"📋 Creating trial manifest from {full_manifest_path}...")
    
    # Load full manifest
    df = pd.read_csv(full_manifest_path)
    
    # Group by phrase
    phrase_groups = df.groupby('phrase')
    
    # Sample clips per phrase (stratified)
    clips_per_phrase = max(10, target_clips // len(phrase_groups))
    trial_clips = []
    
    for phrase, group in phrase_groups:
        # Sample clips for this phrase
        sampled = group.sample(n=min(clips_per_phrase, len(group)), random_state=42)
        trial_clips.append(sampled)
        print(f"  {phrase}: {len(sampled)} clips")
    
    # Combine and save
    trial_df = pd.concat(trial_clips, ignore_index=True)
    
    # If we have too many, randomly sample down to target
    if len(trial_df) > target_clips:
        trial_df = trial_df.sample(n=target_clips, random_state=42)
    
    trial_df.to_csv(output_path, index=False)
    print(f"✅ Trial manifest saved: {output_path} ({len(trial_df)} clips)")
    
    return output_path


def run_preprocessing_trial(config_path: str, trial_manifest: str):
    """Run preprocessing on trial data"""
    print("🚀 Running lower-face ROI RetinaFace+SAM preprocessing trial...")
    
    # Load config
    config = load_config(config_path)
    
    # Create preprocessor with lowerface_roi_retinaface_sam mode
    preprocessor = VideoPreprocessor(config, mode='lowerface_roi_retinaface_sam')
    
    # Process trial manifest
    preprocessor.process_from_manifest(trial_manifest)
    
    print("✅ Preprocessing trial completed!")


def analyze_trial_results(processed_dir: str, output_dir: str):
    """Analyze trial results and create QC visualizations"""
    print("📊 Analyzing trial results...")
    
    # Load quality logs
    quality_log_path = os.path.join(processed_dir, 'quality_logs', 'quality_log.csv')
    if not os.path.exists(quality_log_path):
        print(f"❌ Quality log not found: {quality_log_path}")
        return
    
    df = pd.read_csv(quality_log_path)
    print(f"📈 Loaded {len(df)} processed clips")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # 1) Side-by-side comparison (20 clips)
    create_side_by_side_comparison(df, processed_dir, output_dir)
    
    # 2) Preview grid (10 clips × 4 frames)
    create_preview_grid(df, processed_dir, output_dir)
    
    # 3) Failure analysis (worst 12)
    create_failure_analysis(df, processed_dir, output_dir)
    
    # 4) Save quality CSV
    quality_csv_path = os.path.join(output_dir, 'quality.csv')
    df.to_csv(quality_csv_path, index=False)
    print(f"✅ Quality CSV saved: {quality_csv_path}")


def create_side_by_side_comparison(df: pd.DataFrame, processed_dir: str, output_dir: str):
    """Create side-by-side comparison: original+ROI+mask vs final crop"""
    print("🖼️ Creating side-by-side comparison...")
    
    # Select 20 clips (mix of successful and failed)
    successful = df[df['valid'] == True].sample(n=min(15, len(df[df['valid'] == True])), random_state=42)
    failed = df[df['valid'] == False].sample(n=min(5, len(df[df['valid'] == False])), random_state=42)
    selected = pd.concat([successful, failed])
    
    fig, axes = plt.subplots(len(selected), 2, figsize=(12, 3 * len(selected)))
    if len(selected) == 1:
        axes = axes.reshape(1, -1)
    
    for i, (_, row) in enumerate(selected.iterrows()):
        # Load processed clip
        clip_path = os.path.join(processed_dir, 'clips', f"{row['output_name']}.npy")
        if os.path.exists(clip_path):
            clip = np.load(clip_path)
            
            # Show first frame of processed clip
            if len(clip) > 0:
                axes[i, 1].imshow(cv2.cvtColor(clip[0], cv2.COLOR_BGR2RGB))
                axes[i, 1].set_title(f"Final Crop\n{row['phrase']}")
                axes[i, 1].axis('off')
            else:
                axes[i, 1].text(0.5, 0.5, 'No frames', ha='center', va='center')
                axes[i, 1].set_title(f"Failed\n{row['phrase']}")
        
        # Placeholder for original+ROI+mask (would need original frame access)
        axes[i, 0].text(0.5, 0.5, f"Original + ROI + Mask\n{row['phrase']}\nValid: {row['valid']}", 
                       ha='center', va='center')
        axes[i, 0].set_title("Original + ROI + Mask")
        axes[i, 0].axis('off')
    
    plt.tight_layout()
    output_path = os.path.join(output_dir, 'qc_lowerface_roi_side_by_side.png')
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"✅ Side-by-side comparison saved: {output_path}")


def create_preview_grid(df: pd.DataFrame, processed_dir: str, output_dir: str):
    """Create preview grid: 10 clips × 4 frames"""
    print("🖼️ Creating preview grid...")
    
    # Select 10 successful clips
    successful = df[df['valid'] == True].sample(n=min(10, len(df[df['valid'] == True])), random_state=42)
    
    fig, axes = plt.subplots(len(successful), 4, figsize=(16, 3 * len(successful)))
    if len(successful) == 1:
        axes = axes.reshape(1, -1)
    
    for i, (_, row) in enumerate(successful.iterrows()):
        clip_path = os.path.join(processed_dir, 'clips', f"{row['output_name']}.npy")
        if os.path.exists(clip_path):
            clip = np.load(clip_path)
            
            # Show 4 evenly spaced frames
            frame_indices = np.linspace(0, len(clip) - 1, 4, dtype=int) if len(clip) >= 4 else range(len(clip))
            
            for j, frame_idx in enumerate(frame_indices):
                if j < 4 and frame_idx < len(clip):
                    axes[i, j].imshow(cv2.cvtColor(clip[frame_idx], cv2.COLOR_BGR2RGB))
                    axes[i, j].set_title(f"Frame {frame_idx}")
                    axes[i, j].axis('off')
                elif j < 4:
                    axes[i, j].axis('off')
        
        # Add row label
        if len(successful) > 1:
            axes[i, 0].text(-0.1, 0.5, f"{row['phrase']}", rotation=90, 
                           ha='center', va='center', transform=axes[i, 0].transAxes)
    
    plt.tight_layout()
    output_path = os.path.join(output_dir, 'qc_lowerface_roi_preview.png')
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"✅ Preview grid saved: {output_path}")


def create_failure_analysis(df: pd.DataFrame, processed_dir: str, output_dir: str):
    """Create failure analysis: worst 12 with failure reasons"""
    print("🖼️ Creating failure analysis...")
    
    # Select failed clips
    failed = df[df['valid'] == False]
    if len(failed) == 0:
        print("✅ No failures to analyze!")
        return
    
    # Sort by quality metrics (lowest first)
    failed_sorted = failed.sort_values(['frames_with_mask_pct', 'motion_score'])
    worst_12 = failed_sorted.head(12)
    
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    axes = axes.flatten()
    
    for i, (_, row) in enumerate(worst_12.iterrows()):
        if i >= 12:
            break
        
        clip_path = os.path.join(processed_dir, 'clips', f"{row['output_name']}.npy")
        if os.path.exists(clip_path):
            clip = np.load(clip_path)
            if len(clip) > 0:
                axes[i].imshow(cv2.cvtColor(clip[0], cv2.COLOR_BGR2RGB))
            else:
                axes[i].text(0.5, 0.5, 'No frames', ha='center', va='center')
        else:
            axes[i].text(0.5, 0.5, 'File not found', ha='center', va='center')
        
        # Add failure info
        error_reason = row.get('error_reason', 'unknown')
        mask_pct = row.get('frames_with_mask_pct', 0)
        motion = row.get('motion_score', 0)
        
        axes[i].set_title(f"{row['phrase']}\n{error_reason}\nMask: {mask_pct:.1f}%, Motion: {motion:.3f}", 
                         fontsize=8)
        axes[i].axis('off')
    
    # Hide unused subplots
    for i in range(len(worst_12), 12):
        axes[i].axis('off')
    
    plt.tight_layout()
    output_path = os.path.join(output_dir, 'qc_lowerface_roi_failures.png')
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"✅ Failure analysis saved: {output_path}")


def main():
    parser = argparse.ArgumentParser(description='Run lower-face ROI RetinaFace+SAM trial')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--manifest', default='manifests/train.csv', help='Full manifest to sample from')
    parser.add_argument('--trial-clips', type=int, default=100, help='Number of clips for trial')
    args = parser.parse_args()
    
    # Create trial manifest
    trial_manifest = 'trial_manifest_lowerface_roi.csv'
    create_trial_manifest(args.manifest, trial_manifest, args.trial_clips)
    
    # Run preprocessing trial
    run_preprocessing_trial(args.config, trial_manifest)
    
    # Analyze results
    processed_dir = './data_processed_lowerface_roi'
    output_dir = './data_processed_trial_lowerface_roi'
    analyze_trial_results(processed_dir, output_dir)
    
    print("🎉 Lower-face ROI trial completed!")
    print(f"📁 Results in: {output_dir}")
    print("📋 Review QC images before proceeding to full dataset processing")


if __name__ == "__main__":
    main()
