"""
Manifest Management Module

Maintain comprehensive tracking of videos through all pipeline stages.
"""

import pandas as pd
import json
import os
from pathlib import Path
from typing import Dict, List, Optional
import logging

logger = logging.getLogger(__name__)


class ManifestManager:
    """Manages video manifests across all pipeline stages"""
    
    def __init__(self, manifests_dir: str):
        self.manifests_dir = Path(manifests_dir)
        self.manifests_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize manifest files
        self.stage1_manifest = self.manifests_dir / "stage1_manifest.csv"
        self.stage2_manifest = self.manifests_dir / "stage2_manifest.csv"
        self.stage3_manifest = self.manifests_dir / "stage3_manifest.csv"
        self.stage4_manifest = self.manifests_dir / "stage4_manifest.csv"
        self.final_manifest = self.manifests_dir / "final_manifest.csv"
    
    def create_initial_manifest(self, input_dir: str, target_phrases: List[str]) -> pd.DataFrame:
        """
        Create initial manifest by scanning input directory for target phrases.
        
        Args:
            input_dir: Root directory containing video files
            target_phrases: List of phrases to include
            
        Returns:
            DataFrame with initial manifest
        """
        input_dir = Path(input_dir)
        
        videos = []
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
        
        logger.info(f"Scanning {input_dir} for videos with phrases: {target_phrases}")
        
        for phrase in target_phrases:
            phrase_dir = input_dir / phrase
            if not phrase_dir.exists():
                logger.warning(f"Phrase directory not found: {phrase_dir}")
                continue
                
            # Recursively find video files
            for ext in video_extensions:
                for video_path in phrase_dir.rglob(f"*{ext}"):
                    # Extract speaker_id from path structure
                    # Assuming structure: phrase/speaker/video_file
                    relative_path = video_path.relative_to(phrase_dir)
                    path_parts = relative_path.parts
                    
                    if len(path_parts) >= 2:
                        speaker_id = path_parts[0]
                    else:
                        speaker_id = "unknown"
                    
                    video_id = video_path.stem
                    
                    videos.append({
                        'video_id': video_id,
                        'phrase': phrase,
                        'speaker_id': speaker_id,
                        'input_path': str(video_path),
                        'relative_path': str(relative_path),
                        'file_size_mb': video_path.stat().st_size / (1024 * 1024)
                    })
        
        df = pd.DataFrame(videos)
        
        if len(df) > 0:
            # Add stage-specific columns
            df['path_stage1'] = None
            df['path_stage2_landmarks'] = None
            df['path_stage3_masked'] = None
            df['path_stage4_final'] = None
            df['kept'] = True
            df['filter_reasons'] = None
            df['n_frames'] = None
            df['width'] = None
            df['height'] = None
            df['roi_coords'] = None
            
            logger.info(f"Created initial manifest with {len(df)} videos")
            logger.info(f"Phrase distribution:\n{df['phrase'].value_counts()}")
        else:
            logger.warning("No videos found for target phrases")
        
        return df
    
    def update_manifest(self, stage: int, video_data: Dict) -> None:
        """
        Update manifest with stage-specific paths and metadata.
        
        Args:
            stage: Stage number (1-4)
            video_data: Dictionary with video processing results
        """
        manifest_file = getattr(self, f"stage{stage}_manifest")
        
        # Load existing manifest or create new one
        if manifest_file.exists():
            df = pd.read_csv(manifest_file)
        else:
            # For stage 1, we might need to create from scratch
            if stage == 1:
                logger.warning(f"No existing manifest for stage {stage}, this should be created first")
                return
            else:
                # Load from previous stage
                prev_manifest = getattr(self, f"stage{stage-1}_manifest")
                if prev_manifest.exists():
                    df = pd.read_csv(prev_manifest)
                else:
                    logger.error(f"No previous manifest found for stage {stage}")
                    return
        
        # Update row for this video
        video_id = video_data['video_id']
        mask = df['video_id'] == video_id
        
        if not mask.any():
            logger.warning(f"Video {video_id} not found in manifest")
            return
        
        # Update stage-specific columns
        if stage == 1:
            df.loc[mask, 'path_stage1'] = video_data.get('output_path')
            df.loc[mask, 'n_frames'] = video_data.get('n_frames')
            df.loc[mask, 'width'] = video_data.get('original_size', [None, None])[0]
            df.loc[mask, 'height'] = video_data.get('original_size', [None, None])[1]
            df.loc[mask, 'roi_coords'] = str(video_data.get('roi_coords'))
        elif stage == 2:
            df.loc[mask, 'path_stage2_landmarks'] = video_data.get('landmarks_path')
        elif stage == 3:
            df.loc[mask, 'path_stage3_masked'] = video_data.get('masks_path')
        elif stage == 4:
            df.loc[mask, 'path_stage4_final'] = video_data.get('final_path')
            df.loc[mask, 'kept'] = video_data.get('kept', False)
            df.loc[mask, 'filter_reasons'] = str(video_data.get('reasons', []))
        
        # Save updated manifest
        df.to_csv(manifest_file, index=False)
        logger.debug(f"Updated manifest for stage {stage}, video {video_id}")
    
    def get_stage_summary(self, stage: int) -> Dict:
        """Get summary statistics for a stage"""
        manifest_file = getattr(self, f"stage{stage}_manifest")
        
        if not manifest_file.exists():
            return {'error': f'No manifest found for stage {stage}'}
        
        df = pd.read_csv(manifest_file)
        
        summary = {
            'total_videos': len(df),
            'phrase_counts': df['phrase'].value_counts().to_dict(),
            'speaker_counts': df['speaker_id'].nunique(),
        }
        
        if stage >= 4:
            summary['kept_videos'] = df['kept'].sum()
            summary['filtered_videos'] = (~df['kept']).sum()
            summary['filter_rate'] = summary['filtered_videos'] / len(df) if len(df) > 0 else 0
        
        return summary
    
    def save_manifest(self, df: pd.DataFrame, stage: int) -> None:
        """Save manifest for a specific stage"""
        manifest_file = getattr(self, f"stage{stage}_manifest")
        df.to_csv(manifest_file, index=False)
        logger.info(f"Saved manifest for stage {stage}: {len(df)} videos")


def load_config(config_path: str) -> Dict:
    """Load TOML configuration file"""
    import toml
    with open(config_path, 'r') as f:
        return toml.load(f)


def setup_logging(config: Dict) -> None:
    """Setup logging configuration"""
    logs_dir = Path(config['paths']['logs_dir'])
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    logging.basicConfig(
        level=getattr(logging, config.get('logging', {}).get('level', 'INFO')),
        format=config.get('logging', {}).get('format', '%(asctime)s - %(levelname)s - %(message)s'),
        handlers=[
            logging.FileHandler(logs_dir / 'pipeline.log'),
            logging.StreamHandler()
        ]
    )


def find_videos_by_phrase(input_dir: str, target_phrases: List[str], 
                         extensions: List[str] = None) -> Dict[str, List[str]]:
    """
    Find all videos for target phrases in input directory.
    
    Args:
        input_dir: Root directory to search
        target_phrases: List of phrases to find
        extensions: Video file extensions to look for
        
    Returns:
        Dictionary mapping phrase to list of video paths
    """
    if extensions is None:
        extensions = ['.mp4', '.avi', '.mov', '.mkv']
    
    input_dir = Path(input_dir)
    phrase_videos = {}
    
    for phrase in target_phrases:
        phrase_dir = input_dir / phrase
        videos = []
        
        if phrase_dir.exists():
            for ext in extensions:
                videos.extend(list(phrase_dir.rglob(f"*{ext}")))
        
        phrase_videos[phrase] = [str(p) for p in videos]
    
    return phrase_videos
