"""
ICU Lipreading 4-Stage Gated Preprocessing Pipeline

A comprehensive preprocessing pipeline for ICU lipreading data with visual QC gates.
Each stage must complete with manual review before proceeding to the next.

Stages:
1. ROI Cropping + Visual QC
2. MediaPipe Facial Landmarks  
3. YOLO + SAM Segmentation
4. Quality Filtering

Author: ICU Lipreading Team
Version: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "ICU Lipreading Team"

# Import main modules
from . import crop_roi
from . import manifest
from . import qc_gallery

__all__ = [
    "crop_roi",
    "manifest", 
    "qc_gallery",
]
