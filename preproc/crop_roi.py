"""
Stage 1: ROI Cropping Module

Extract top-middle third of each video frame with optional downward shift
for lip positioning, resize to 96x96 with aspect preservation.
"""

import cv2
import numpy as np
import json
import os
from pathlib import Path
from typing import Tuple, Dict, Optional
import logging

logger = logging.getLogger(__name__)


def compute_roi(width: int, height: int, config: dict) -> Tuple[int, int, int, int]:
    """
    Compute ROI coordinates for top-middle third of 3x3 grid.
    
    Args:
        width: Frame width in pixels
        height: Frame height in pixels  
        config: Crop configuration from TOML
        
    Returns:
        Tuple of (x0, y0, x1, y1) pixel coordinates for ROI
    """
    crop_config = config['crop']
    
    # Base ROI coordinates (top-middle third)
    x0 = int(width * crop_config['x0_frac'])
    x1 = int(width * crop_config['x1_frac'])
    y0 = int(height * crop_config['y0_frac'])
    y1 = int(height * crop_config['y1_frac'])
    
    # Optional downward shift for better lip capture
    if 'shift_y_frac' in crop_config:
        shift_y = int(height * crop_config['shift_y_frac'])
        y0 += shift_y
        y1 += shift_y
        
        # Ensure we don't go beyond frame boundaries
        if y1 > height:
            overflow = y1 - height
            y0 -= overflow
            y1 -= overflow
            y0 = max(0, y0)
    
    # Ensure valid coordinates
    x0 = max(0, min(x0, width - 1))
    x1 = max(x0 + 1, min(x1, width))
    y0 = max(0, min(y0, height - 1))
    y1 = max(y0 + 1, min(y1, height))
    
    return (x0, y0, x1, y1)


def crop_and_resize(frame: np.ndarray, roi: Tuple[int, int, int, int], 
                   target_size: int, keep_aspect: bool, pad_mode: str) -> np.ndarray:
    """
    Crop frame to ROI and resize to target_size x target_size.
    
    Args:
        frame: Input frame as numpy array
        roi: ROI coordinates (x0, y0, x1, y1)
        target_size: Target size for output (square)
        keep_aspect: Whether to preserve aspect ratio
        pad_mode: Padding mode for cv2.copyMakeBorder
        
    Returns:
        Processed frame as numpy array
    """
    x0, y0, x1, y1 = roi
    
    # Extract ROI
    cropped = frame[y0:y1, x0:x1]
    
    if cropped.size == 0:
        logger.warning(f"Empty crop with ROI {roi}, returning black frame")
        return np.zeros((target_size, target_size, 3), dtype=np.uint8)
    
    if not keep_aspect:
        # Simple resize without aspect preservation
        return cv2.resize(cropped, (target_size, target_size))
    
    # Resize with aspect preservation
    h, w = cropped.shape[:2]
    
    # Calculate scaling factor
    scale = target_size / max(h, w)
    new_w = int(w * scale)
    new_h = int(h * scale)
    
    # Resize
    resized = cv2.resize(cropped, (new_w, new_h))
    
    # Calculate padding
    pad_w = target_size - new_w
    pad_h = target_size - new_h
    
    # Pad to square
    pad_mode_cv2 = getattr(cv2, f'BORDER_{pad_mode.upper()}', cv2.BORDER_CONSTANT)
    
    padded = cv2.copyMakeBorder(
        resized,
        pad_h // 2, pad_h - pad_h // 2,  # top, bottom
        pad_w // 2, pad_w - pad_w // 2,  # left, right
        pad_mode_cv2,
        value=[0, 0, 0] if pad_mode == 'constant' else None
    )
    
    return padded


def process_video(input_path: str, output_dir: str, config: dict) -> Dict:
    """
    Process single video through Stage 1 ROI cropping.
    
    Args:
        input_path: Path to input video file
        output_dir: Directory to save processed video
        config: Full configuration dictionary
        
    Returns:
        Metadata dictionary with processing results
    """
    input_path = Path(input_path)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate output paths
    video_id = input_path.stem
    output_video_path = output_dir / f"{video_id}.mp4"
    output_meta_path = output_dir / f"{video_id}_meta.json"
    
    metadata = {
        'video_id': video_id,
        'input_path': str(input_path),
        'output_path': str(output_video_path),
        'status': 'processing',
        'error': None,
        'n_frames': 0,
        'original_size': None,
        'roi_coords': None,
        'target_size': config['crop']['resize_to']
    }
    
    try:
        # Open input video
        cap = cv2.VideoCapture(str(input_path))
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {input_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        metadata['original_size'] = (width, height)
        metadata['fps'] = fps
        
        # Compute ROI
        roi = compute_roi(width, height, config)
        metadata['roi_coords'] = roi
        
        # Setup output video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        target_size = config['crop']['resize_to']
        out = cv2.VideoWriter(
            str(output_video_path), 
            fourcc, 
            fps, 
            (target_size, target_size)
        )
        
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            # Process frame
            processed_frame = crop_and_resize(
                frame, roi, target_size,
                config['crop']['keep_aspect'],
                config['crop']['pad_mode']
            )
            
            # Write frame
            out.write(processed_frame)
            frame_count += 1
        
        # Cleanup
        cap.release()
        out.release()
        
        metadata['n_frames'] = frame_count
        metadata['status'] = 'success'
        
        logger.info(f"Processed {video_id}: {frame_count} frames, ROI {roi}")
        
    except Exception as e:
        metadata['status'] = 'failed'
        metadata['error'] = str(e)
        logger.error(f"Failed to process {video_id}: {e}")
        
        # Cleanup on failure
        if output_video_path.exists():
            output_video_path.unlink()
    
    # Save metadata
    with open(output_meta_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    return metadata
