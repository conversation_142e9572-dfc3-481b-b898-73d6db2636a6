"""
Quality Control Gallery Module

Generate HTML galleries for visual inspection of each pipeline stage.
"""

import cv2
import numpy as np
import json
import base64
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import random
import logging
from jinja2 import Template

logger = logging.getLogger(__name__)


def extract_sample_frames(video_path: str, n_frames: int = 5) -> List[np.ndarray]:
    """
    Extract evenly spaced sample frames from video.
    
    Args:
        video_path: Path to video file
        n_frames: Number of frames to extract
        
    Returns:
        List of frame arrays
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        logger.warning(f"Could not open video: {video_path}")
        return []
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total_frames <= n_frames:
        # Extract all frames
        frame_indices = list(range(total_frames))
    else:
        # Extract evenly spaced frames
        frame_indices = np.linspace(0, total_frames - 1, n_frames, dtype=int)
    
    frames = []
    for frame_idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if ret:
            frames.append(frame)
    
    cap.release()
    return frames


def frame_to_base64(frame: np.ndarray, max_size: int = 200) -> str:
    """
    Convert frame to base64 encoded image for HTML embedding.
    
    Args:
        frame: Frame as numpy array
        max_size: Maximum dimension for thumbnail
        
    Returns:
        Base64 encoded image string
    """
    # Resize for thumbnail
    h, w = frame.shape[:2]
    scale = max_size / max(h, w)
    if scale < 1:
        new_w = int(w * scale)
        new_h = int(h * scale)
        frame = cv2.resize(frame, (new_w, new_h))
    
    # Encode as JPEG
    _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
    img_base64 = base64.b64encode(buffer).decode('utf-8')
    
    return f"data:image/jpeg;base64,{img_base64}"


def draw_roi_overlay(frame: np.ndarray, roi: Tuple[int, int, int, int], 
                    color: Tuple[int, int, int] = (0, 255, 0)) -> np.ndarray:
    """
    Draw ROI rectangle overlay on frame.
    
    Args:
        frame: Input frame
        roi: ROI coordinates (x0, y0, x1, y1)
        color: Rectangle color in BGR
        
    Returns:
        Frame with ROI overlay
    """
    frame_copy = frame.copy()
    x0, y0, x1, y1 = roi
    cv2.rectangle(frame_copy, (x0, y0), (x1, y1), color, 2)
    
    # Add ROI label
    cv2.putText(frame_copy, 'ROI', (x0, y0 - 10), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
    
    return frame_copy


def check_lip_center_heuristic(roi: Tuple[int, int, int, int], 
                              tolerance_px: int = 10) -> Dict:
    """
    Heuristic check for lip center positioning within ROI.
    
    Args:
        roi: ROI coordinates (x0, y0, x1, y1)
        tolerance_px: Tolerance in pixels for center check
        
    Returns:
        Dictionary with center check results
    """
    x0, y0, x1, y1 = roi
    roi_w = x1 - x0
    roi_h = y1 - y0
    
    # Expected lip center (lower third of ROI, mid-width)
    expected_center_x = x0 + roi_w // 2
    expected_center_y = y0 + int(roi_h * 0.67)  # Lower third
    
    # For now, assume lips are centered (would need actual detection for real check)
    # This is a placeholder for the heuristic
    actual_center_x = expected_center_x  # Placeholder
    actual_center_y = expected_center_y  # Placeholder
    
    distance = np.sqrt((actual_center_x - expected_center_x)**2 + 
                      (actual_center_y - expected_center_y)**2)
    
    is_centered = distance <= tolerance_px
    
    return {
        'is_centered': is_centered,
        'distance': distance,
        'expected_center': (expected_center_x, expected_center_y),
        'actual_center': (actual_center_x, actual_center_y)
    }


def generate_stage1_gallery(config: Dict, sample_size: int = 200) -> Dict:
    """
    Generate Stage 1 QC gallery showing ROI cropping results.
    
    Args:
        config: Full configuration dictionary
        sample_size: Number of videos to sample for gallery
        
    Returns:
        Dictionary with gallery data and statistics
    """
    stage1_dir = Path(config['paths']['stage1_dir'])
    reports_dir = Path(config['paths']['reports_dir'])
    reports_dir.mkdir(parents=True, exist_ok=True)
    
    # Find all processed videos
    video_files = list(stage1_dir.glob("*.mp4"))
    meta_files = list(stage1_dir.glob("*_meta.json"))
    
    logger.info(f"Found {len(video_files)} processed videos in {stage1_dir}")
    
    if len(video_files) == 0:
        logger.warning("No processed videos found for Stage 1 gallery")
        return {'error': 'No processed videos found'}
    
    # Sample videos for gallery
    sample_videos = random.sample(video_files, min(sample_size, len(video_files)))
    
    gallery_data = []
    center_checks = []
    failures = []
    
    for video_path in sample_videos:
        video_id = video_path.stem
        meta_path = stage1_dir / f"{video_id}_meta.json"
        
        try:
            # Load metadata
            if meta_path.exists():
                with open(meta_path, 'r') as f:
                    metadata = json.load(f)
            else:
                logger.warning(f"No metadata found for {video_id}")
                continue
            
            # Skip failed videos
            if metadata.get('status') != 'success':
                failures.append({
                    'video_id': video_id,
                    'error': metadata.get('error', 'Unknown error')
                })
                continue
            
            # Extract sample frames from processed video
            processed_frames = extract_sample_frames(str(video_path), 
                                                   config['qc']['frames_per_video'])
            
            if not processed_frames:
                continue
            
            # Load original video for ROI overlay (if available)
            original_path = metadata.get('input_path')
            roi_coords = metadata.get('roi_coords')
            
            original_frames = []
            if original_path and Path(original_path).exists() and roi_coords:
                original_frames = extract_sample_frames(original_path, 
                                                      config['qc']['frames_per_video'])
                # Add ROI overlays
                original_frames = [draw_roi_overlay(frame, roi_coords) 
                                 for frame in original_frames]
            
            # Convert frames to base64 for HTML
            processed_thumbs = [frame_to_base64(frame) for frame in processed_frames]
            original_thumbs = [frame_to_base64(frame) for frame in original_frames]
            
            # Check lip centering heuristic
            if roi_coords:
                center_check = check_lip_center_heuristic(
                    roi_coords, config['qc']['mouth_center_tolerance_px']
                )
                center_checks.append(center_check['is_centered'])
            else:
                center_check = {'is_centered': False, 'distance': float('inf')}
                center_checks.append(False)
            
            # Extract phrase from path or metadata
            phrase = "unknown"
            if original_path:
                path_parts = Path(original_path).parts
                for part in path_parts:
                    if part in config['target_phrases']['phrases']:
                        phrase = part
                        break
            
            gallery_data.append({
                'video_id': video_id,
                'phrase': phrase,
                'n_frames': metadata.get('n_frames', 0),
                'original_size': metadata.get('original_size'),
                'roi_coords': roi_coords,
                'processed_thumbs': processed_thumbs,
                'original_thumbs': original_thumbs,
                'center_check': center_check,
                'status': metadata.get('status', 'unknown')
            })
            
        except Exception as e:
            logger.error(f"Error processing {video_id} for gallery: {e}")
            failures.append({
                'video_id': video_id,
                'error': str(e)
            })
    
    # Calculate statistics
    centerfrac = np.mean(center_checks) if center_checks else 0.0
    failure_rate = len(failures) / len(sample_videos) if sample_videos else 0.0
    
    stats = {
        'total_sampled': len(sample_videos),
        'successful': len(gallery_data),
        'failed': len(failures),
        'failure_rate': failure_rate,
        'centerfrac': centerfrac,
        'acceptance_threshold': config['qc']['acceptance_min_centerfrac']
    }
    
    # Generate HTML gallery
    html_content = generate_stage1_html(gallery_data, stats, failures)
    
    # Save gallery
    gallery_path = reports_dir / "stage1_gallery.html"
    with open(gallery_path, 'w') as f:
        f.write(html_content)
    
    # Save summary
    summary = {
        'stats': stats,
        'gallery_path': str(gallery_path),
        'sample_size': len(gallery_data)
    }
    
    summary_path = reports_dir / "stage1_summary.json"
    with open(summary_path, 'w') as f:
        json.dump(summary, f, indent=2)
    
    logger.info(f"Generated Stage 1 gallery: {gallery_path}")
    logger.info(f"Center fraction: {centerfrac:.3f} (threshold: {stats['acceptance_threshold']})")
    logger.info(f"Failure rate: {failure_rate:.3f}")
    
    return summary


def generate_stage1_html(gallery_data: List[Dict], stats: Dict, failures: List[Dict]) -> str:
    """Generate HTML content for Stage 1 gallery"""
    
    html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>Stage 1 QC Gallery - ROI Cropping</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .stats { display: flex; gap: 20px; margin-bottom: 20px; }
        .stat-box { background: #e8f4f8; padding: 15px; border-radius: 5px; text-align: center; }
        .gallery { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .video-item { border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        .video-header { font-weight: bold; margin-bottom: 10px; }
        .frames { display: flex; gap: 10px; flex-wrap: wrap; }
        .frame-section { flex: 1; min-width: 180px; }
        .frame-section h4 { margin: 5px 0; font-size: 12px; }
        .frame-thumbs { display: flex; gap: 5px; flex-wrap: wrap; }
        .frame-thumbs img { width: 80px; height: 80px; object-fit: cover; border: 1px solid #ccc; }
        .center-check { margin-top: 10px; padding: 5px; border-radius: 3px; }
        .center-good { background: #d4edda; color: #155724; }
        .center-bad { background: #f8d7da; color: #721c24; }
        .failures { margin-top: 30px; background: #f8f9fa; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Stage 1 QC Gallery - ROI Cropping</h1>
        <p>Visual quality control for ROI extraction and cropping. Review lip positioning and ROI accuracy.</p>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <h3>{{ stats.successful }}</h3>
            <p>Successful</p>
        </div>
        <div class="stat-box">
            <h3>{{ "%.1f"|format(stats.centerfrac * 100) }}%</h3>
            <p>Center Fraction</p>
        </div>
        <div class="stat-box">
            <h3>{{ "%.1f"|format(stats.failure_rate * 100) }}%</h3>
            <p>Failure Rate</p>
        </div>
        <div class="stat-box">
            <h3>{{ stats.total_sampled }}</h3>
            <p>Total Sampled</p>
        </div>
    </div>
    
    <div class="gallery">
        {% for item in gallery_data %}
        <div class="video-item">
            <div class="video-header">
                {{ item.video_id }} - {{ item.phrase }}
                <br><small>{{ item.n_frames }} frames, {{ item.original_size }}</small>
            </div>
            
            <div class="frames">
                {% if item.original_thumbs %}
                <div class="frame-section">
                    <h4>Original + ROI</h4>
                    <div class="frame-thumbs">
                        {% for thumb in item.original_thumbs %}
                        <img src="{{ thumb }}" alt="Original frame">
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <div class="frame-section">
                    <h4>Processed (96x96)</h4>
                    <div class="frame-thumbs">
                        {% for thumb in item.processed_thumbs %}
                        <img src="{{ thumb }}" alt="Processed frame">
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="center-check {{ 'center-good' if item.center_check.is_centered else 'center-bad' }}">
                Center Check: {{ "✓ PASS" if item.center_check.is_centered else "✗ FAIL" }}
                (distance: {{ "%.1f"|format(item.center_check.distance) }}px)
            </div>
        </div>
        {% endfor %}
    </div>
    
    {% if failures %}
    <div class="failures">
        <h3>Processing Failures ({{ failures|length }})</h3>
        <ul>
            {% for failure in failures %}
            <li><strong>{{ failure.video_id }}</strong>: {{ failure.error }}</li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
    
    <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border-radius: 5px;">
        <h3>Acceptance Criteria</h3>
        <ul>
            <li>Center fraction ≥ {{ "%.1f"|format(stats.acceptance_threshold * 100) }}% (Current: {{ "%.1f"|format(stats.centerfrac * 100) }}%)</li>
            <li>Failure rate < 5% (Current: {{ "%.1f"|format(stats.failure_rate * 100) }}%)</li>
            <li>Visual gallery shows consistent ROI positioning across phrases</li>
        </ul>
        <p><strong>Status: {{ "✓ PASS" if stats.centerfrac >= stats.acceptance_threshold and stats.failure_rate < 0.05 else "✗ FAIL" }}</strong></p>
    </div>
</body>
</html>
    """
    
    template = Template(html_template)
    return template.render(
        gallery_data=gallery_data,
        stats=stats,
        failures=failures
    )


def main():
    """CLI entry point for QC gallery generation"""
    import argparse
    import sys
    from pathlib import Path

    # Add parent directory to path for imports
    sys.path.append(str(Path(__file__).parent.parent))
    from preproc.manifest import load_config, setup_logging

    parser = argparse.ArgumentParser(description='Generate QC Gallery')
    parser.add_argument('--config', required=True, help='Path to TOML configuration file')
    parser.add_argument('--stage', type=int, required=True, choices=[1, 2, 3, 4],
                       help='Pipeline stage to generate gallery for')
    parser.add_argument('--sample-size', type=int, help='Number of videos to sample')
    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)
    setup_logging(config)
    logger = logging.getLogger(__name__)

    sample_size = args.sample_size or config['qc']['sample_videos']

    logger.info(f"Generating Stage {args.stage} QC gallery (sample size: {sample_size})")

    if args.stage == 1:
        result = generate_stage1_gallery(config, sample_size)
        if 'error' in result:
            logger.error(f"Gallery generation failed: {result['error']}")
            return 1
        else:
            logger.info(f"Gallery generated successfully: {result['gallery_path']}")
            return 0
    else:
        logger.error(f"Stage {args.stage} gallery generation not implemented yet")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
