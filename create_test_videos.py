#!/usr/bin/env python3
"""
Create test videos for pipeline testing
"""

import cv2
import numpy as np
from pathlib import Path

def create_test_video(output_path: str, width: int = 640, height: int = 480, 
                     fps: int = 25, duration_sec: int = 2):
    """Create a simple test video with moving shapes"""
    
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
    
    total_frames = fps * duration_sec
    
    for frame_idx in range(total_frames):
        # Create a frame with gradient background
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add gradient background
        for y in range(height):
            intensity = int(255 * y / height)
            frame[y, :] = [intensity // 3, intensity // 2, intensity]
        
        # Add moving circle (simulating mouth/face region)
        center_x = width // 2
        center_y = height // 3  # Upper third where ROI will be
        
        # Make circle move slightly
        offset_x = int(10 * np.sin(frame_idx * 0.2))
        offset_y = int(5 * np.cos(frame_idx * 0.3))
        
        cv2.circle(frame, 
                  (center_x + offset_x, center_y + offset_y), 
                  30, (255, 200, 150), -1)
        
        # Add smaller circle for "mouth"
        cv2.circle(frame, 
                  (center_x + offset_x, center_y + offset_y + 15), 
                  8, (100, 50, 50), -1)
        
        # Add some text
        cv2.putText(frame, f'Frame {frame_idx}', (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame)
    
    out.release()
    print(f"Created test video: {output_path}")

def main():
    # Create test videos for different phrases
    phrases = ["pillow", "phone", "doctor", "glasses"]

    for phrase in phrases:
        for speaker_id in ["speaker1", "speaker2"]:
            for video_id in range(3):  # 3 videos per speaker per phrase
                output_path = f"data/RAW_VIDEOS/{phrase}/{speaker_id}/test_video_{video_id:03d}.mp4"
                create_test_video(output_path)

    print("Test videos created successfully!")

if __name__ == "__main__":
    main()
