#!/usr/bin/env python3
"""
Check video file integrity and extract sample frames.
"""

import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
import glob
import pandas as pd


def check_single_video(video_path, save_frames=True):
    """Check if we can read frames from a video"""
    print(f"\n🔍 Checking: {os.path.basename(video_path)}")
    
    # Check if file exists and get size
    if not os.path.exists(video_path):
        print("❌ File does not exist")
        return False
    
    file_size = os.path.getsize(video_path)
    print(f"📁 File size: {file_size:,} bytes ({file_size/1024/1024:.1f} MB)")
    
    # Try to open with OpenCV
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print("❌ OpenCV cannot open video")
        return False
    
    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    print(f"📊 Properties: {width}x{height}, {fps:.1f}fps")
    print(f"📊 Reported frame count: {frame_count}")
    
    # Try to read frames manually
    frames_read = 0
    frames_with_data = 0
    sample_frames = []
    
    for i in range(20):  # Try to read first 20 frames
        ret, frame = cap.read()
        if not ret:
            break
        
        frames_read += 1
        
        if frame is not None and frame.size > 0:
            frames_with_data += 1
            
            # Check if frame has actual content (not all black)
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            brightness = np.mean(gray)
            
            print(f"  Frame {i}: {frame.shape}, brightness={brightness:.1f}")
            
            # Save first few frames for inspection
            if save_frames and len(sample_frames) < 3:
                sample_frames.append((i, frame, brightness))
    
    cap.release()
    
    print(f"📊 Successfully read {frames_read} frames, {frames_with_data} with data")
    
    # Save sample frames
    if save_frames and sample_frames:
        fig, axes = plt.subplots(1, len(sample_frames), figsize=(15, 5))
        if len(sample_frames) == 1:
            axes = [axes]
        
        for idx, (frame_num, frame, brightness) in enumerate(sample_frames):
            ax = axes[idx]
            ax.imshow(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            ax.set_title(f'Frame {frame_num}\nBrightness: {brightness:.1f}')
            ax.axis('off')
        
        filename = f"sample_frames_{os.path.basename(video_path).replace('.webm', '')}.png"
        plt.tight_layout()
        plt.savefig(filename, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"💾 Sample frames saved: {filename}")
    
    return frames_with_data > 0


def check_video_dataset():
    """Check multiple videos from the dataset"""
    print("🎯 Checking Video Dataset Integrity")
    print("=" * 50)
    
    # Find videos
    video_pattern = "../top 10 dataset 6.9.25/**/*.webm"
    all_videos = glob.glob(video_pattern, recursive=True)
    
    if not all_videos:
        print("❌ No video files found!")
        return
    
    print(f"📊 Found {len(all_videos)} video files")
    
    # Check first 10 videos
    working_videos = []
    broken_videos = []
    
    for video_path in all_videos[:10]:
        try:
            if check_single_video(video_path, save_frames=True):
                working_videos.append(video_path)
            else:
                broken_videos.append(video_path)
        except Exception as e:
            print(f"❌ Error checking {video_path}: {e}")
            broken_videos.append(video_path)
    
    print(f"\n📊 Summary:")
    print(f"✅ Working videos: {len(working_videos)}")
    print(f"❌ Broken videos: {len(broken_videos)}")
    
    if working_videos:
        print(f"\n✅ Working videos:")
        for video in working_videos:
            print(f"  - {os.path.basename(video)}")
    
    if broken_videos:
        print(f"\n❌ Broken videos:")
        for video in broken_videos:
            print(f"  - {os.path.basename(video)}")
    
    return working_videos


def test_alternative_video_readers():
    """Test alternative ways to read videos"""
    print("\n🧪 Testing alternative video readers...")
    
    # Find a sample video
    video_pattern = "../top 10 dataset 6.9.25/**/*.webm"
    all_videos = glob.glob(video_pattern, recursive=True)
    
    if not all_videos:
        print("❌ No videos found")
        return
    
    sample_video = all_videos[0]
    print(f"📹 Testing with: {os.path.basename(sample_video)}")
    
    # Test different backends
    backends = [
        (cv2.CAP_FFMPEG, "FFmpeg"),
        (cv2.CAP_GSTREAMER, "GStreamer"),
        (cv2.CAP_AVFOUNDATION, "AVFoundation"),
    ]
    
    for backend_id, backend_name in backends:
        try:
            print(f"\n🧪 Testing {backend_name} backend...")
            cap = cv2.VideoCapture(sample_video, backend_id)
            
            if cap.isOpened():
                ret, frame = cap.read()
                if ret and frame is not None:
                    print(f"  ✅ {backend_name}: Successfully read frame {frame.shape}")
                else:
                    print(f"  ❌ {backend_name}: Could not read frame")
                cap.release()
            else:
                print(f"  ❌ {backend_name}: Could not open video")
        
        except Exception as e:
            print(f"  ❌ {backend_name}: Error - {e}")


def main():
    """Main function"""
    print("🔍 Video Integrity Check")
    print("=" * 30)
    
    # Check video dataset
    working_videos = check_video_dataset()
    
    # Test alternative readers
    test_alternative_video_readers()
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if not working_videos:
        print("  - All videos appear to be corrupted or in unsupported format")
        print("  - Try converting videos to MP4 format")
        print("  - Check if videos were properly downloaded/transferred")
        print("  - Consider using different video codec/container")
    else:
        print(f"  - {len(working_videos)} videos are readable")
        print("  - Proceed with MediaPipe testing on working videos")
        print("  - Consider filtering out corrupted videos")
    
    print("\n✅ Video integrity check completed!")


if __name__ == "__main__":
    main()
