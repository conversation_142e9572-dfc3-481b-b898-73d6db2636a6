#!/usr/bin/env python3
"""
Simple QC visualization for strict lip cropping.
Works without MediaPipe - shows grid-based cropping results.
"""

import os
import sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
import glob
import random
from pathlib import Path


def extract_grid_crop(frame, img_size=96):
    """
    Extract crop using 3x2 grid heuristic (top-middle cell).
    This mimics the spatial prior from the preprocessing pipeline.
    """
    h, w = frame.shape[:2]
    
    # Divide frame into 3 columns × 2 rows grid
    col_width = w // 3
    row_height = h // 2
    
    # Top-middle cell (row 0, column 1) - where lips should be
    x1 = col_width
    x2 = 2 * col_width
    y1 = 0
    y2 = row_height
    
    # Extract the crop
    lip_crop = frame[y1:y2, x1:x2]
    
    # Resize to target size
    if lip_crop.size > 0:
        resized_crop = cv2.resize(lip_crop, (img_size, img_size))
        return resized_crop, (x1, y1, x2, y2)
    else:
        # Fallback: center crop
        crop_size = min(h, w)
        start_h = (h - crop_size) // 2
        start_w = (w - crop_size) // 2
        center_crop = frame[start_h:start_h+crop_size, start_w:start_w+crop_size]
        resized_crop = cv2.resize(center_crop, (img_size, img_size))
        return resized_crop, (start_w, start_h, start_w+crop_size, start_h+crop_size)


def load_video_frames(video_path, max_frames=16):
    """Load frames from video file"""
    try:
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        while len(frames) < max_frames:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        
        cap.release()
        return frames
    except Exception as e:
        print(f"Error loading {video_path}: {e}")
        return None


def create_qc_visualization(num_clips=10, frames_per_clip=4, output_file="qc_strict_preview.png"):
    """
    Create QC visualization showing grid-based cropping results.
    """
    
    print("🔍 Starting QC visualization for strict lip cropping...")
    
    # Find all video files in the local dataset
    video_pattern = "../top 10 dataset 6.9.25/**/*.webm"
    all_videos = glob.glob(video_pattern, recursive=True)
    
    if not all_videos:
        print("❌ No video files found!")
        return False
    
    print(f"📊 Found {len(all_videos)} video files")
    
    # Sample random videos
    sample_videos = random.sample(all_videos, min(50, len(all_videos)))
    
    processed_results = []
    
    for video_path in sample_videos:
        try:
            # Extract label from path
            path_parts = video_path.split('/')
            label = path_parts[-6] if len(path_parts) >= 6 else "unknown"
            
            # Load video frames
            frames = load_video_frames(video_path, max_frames=frames_per_clip)
            if not frames:
                continue
            
            print(f"✅ Processing {os.path.basename(video_path)} ({len(frames)} frames)")
            
            # Process frames with grid cropping
            frame_results = []
            for frame in frames:
                crop, bbox = extract_grid_crop(frame)
                
                frame_results.append({
                    'crop': crop,
                    'bbox': bbox,
                    'original_frame': frame
                })
            
            if frame_results:
                processed_results.append({
                    'video_path': video_path,
                    'label': label,
                    'frames': frame_results
                })
        
        except Exception as e:
            print(f"⚠️ Error processing {video_path}: {e}")
            continue
    
    if not processed_results:
        print("❌ No clips could be processed!")
        return False
    
    # Select best examples (first N successful ones)
    best_clips = processed_results[:num_clips]
    
    print(f"✅ Processed {len(processed_results)} clips, showing {len(best_clips)}")
    
    # Create visualization
    create_crop_grid(best_clips, frames_per_clip, output_file)
    
    # Print statistics
    print_qc_statistics(processed_results)
    
    return True


def create_crop_grid(best_clips, frames_per_clip, output_file):
    """Create grid visualization showing original frames with crop boxes and resulting crops"""
    
    num_clips = len(best_clips)
    
    # Create figure with 2 rows per clip (original + crop)
    fig, axes = plt.subplots(num_clips * 2, frames_per_clip, figsize=(16, 6*num_clips))
    fig.suptitle('QC: Strict Lip Cropping - Grid-Based Heuristic\n'
                 'Top row: Original frames with crop boxes (red)\n'
                 'Bottom row: Resulting 96x96 crops', 
                 fontsize=16, fontweight='bold')
    
    # Handle single clip case
    if num_clips == 1:
        axes = axes.reshape(2, frames_per_clip)
    
    for clip_idx, clip_data in enumerate(best_clips):
        clip_name = os.path.basename(clip_data['video_path']).replace('.webm', '')
        
        for frame_idx in range(frames_per_clip):
            if frame_idx < len(clip_data['frames']):
                frame_data = clip_data['frames'][frame_idx]
                original_frame = frame_data['original_frame']
                crop = frame_data['crop']
                bbox = frame_data['bbox']
                
                # Top row: Original frame with crop box
                orig_display = original_frame.copy()
                x1, y1, x2, y2 = bbox
                cv2.rectangle(orig_display, (x1, y1), (x2, y2), (0, 0, 255), 3)  # Red box
                
                ax_orig = axes[clip_idx * 2, frame_idx]
                ax_orig.imshow(cv2.cvtColor(orig_display, cv2.COLOR_BGR2RGB))
                ax_orig.set_title(f'{clip_name}\nFrame {frame_idx+1} (Original)', fontsize=8)
                ax_orig.axis('off')
                
                # Bottom row: Resulting crop
                ax_crop = axes[clip_idx * 2 + 1, frame_idx]
                ax_crop.imshow(cv2.cvtColor(crop, cv2.COLOR_BGR2RGB))
                ax_crop.set_title(f'96x96 Crop\nLabel: {clip_data["label"]}', fontsize=8)
                ax_crop.axis('off')
            else:
                # Empty subplots
                axes[clip_idx * 2, frame_idx].axis('off')
                axes[clip_idx * 2 + 1, frame_idx].axis('off')
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"💾 QC visualization saved to: {output_file}")


def print_qc_statistics(results):
    """Print QC statistics"""
    total_clips = len(results)
    
    labels = {}
    for result in results:
        label = result['label']
        labels[label] = labels.get(label, 0) + 1
    
    print(f"\n📊 QC Statistics:")
    print("=" * 50)
    print(f"Total clips processed: {total_clips}")
    print(f"Labels distribution:")
    for label, count in sorted(labels.items()):
        print(f"  {label}: {count}")
    print("=" * 50)


if __name__ == "__main__":
    success = create_qc_visualization()
    
    if success:
        print("\n🎯 QC visualization complete!")
        print("📋 NEXT STEPS:")
        print("1. Open 'qc_strict_preview.png' and visually inspect the crops")
        print("2. Check that red crop boxes are positioned over the mouth region")
        print("3. Verify that resulting 96x96 crops show lips clearly")
        print("4. If satisfied, proceed with full preprocessing")
        print("5. If not satisfied, adjust cropping parameters and re-run QC")
    else:
        print("❌ QC visualization failed!")
        sys.exit(1)
