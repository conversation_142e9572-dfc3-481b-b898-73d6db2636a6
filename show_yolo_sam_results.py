#!/usr/bin/env python3
"""
Show YOLO+SAM processed frames
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
import os
import glob

def main():
    # Find all processed clips
    clip_files = glob.glob('data_processed_yolo_sam/clips/*.npy')
    print(f'Found {len(clip_files)} processed clips')
    
    if len(clip_files) == 0:
        print("No processed clips found!")
        return
    
    # Create visualization
    fig, axes = plt.subplots(len(clip_files), 5, figsize=(20, 4*len(clip_files)))
    fig.suptitle('YOLO+SAM Processed Frames - 5 Frames from Each Video', fontsize=16, fontweight='bold')
    
    if len(clip_files) == 1:
        axes = axes.reshape(1, -1)
    
    for clip_idx, clip_file in enumerate(clip_files):
        # Extract clip name
        clip_name = os.path.basename(clip_file).replace('.npy', '')
        phrase = clip_name.split('_')[0]
        if len(clip_name.split('_')) > 2:
            phrase = clip_name.split('_')[0] + '_' + clip_name.split('_')[1]
        
        # Load clip
        clip = np.load(clip_file)
        print(f'Clip {clip_idx}: {clip_name} - Shape: {clip.shape}')
        
        # Select 5 frames evenly spaced
        frame_indices = [0, len(clip)//4, len(clip)//2, 3*len(clip)//4, len(clip)-1]
        
        for frame_idx, frame_pos in enumerate(frame_indices):
            if frame_pos < len(clip):
                frame = clip[frame_pos]
                # Convert BGR to RGB for display
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                if len(clip_files) > 1:
                    ax = axes[clip_idx, frame_idx]
                else:
                    ax = axes[frame_idx]
                    
                ax.imshow(frame_rgb)
                ax.set_title(f'{phrase}\nFrame {frame_pos}', fontsize=10)
                ax.axis('off')
            else:
                if len(clip_files) > 1:
                    ax = axes[clip_idx, frame_idx]
                else:
                    ax = axes[frame_idx]
                ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('yolo_sam_processed_frames.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print('✅ Saved visualization to yolo_sam_processed_frames.png')

if __name__ == "__main__":
    main()
