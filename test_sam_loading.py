#!/usr/bin/env python3
"""
Test SAM model loading with PyTorch 2.6 compatibility
"""

import torch
import os
from typing import Optional

def load_sam_model_pytorch26_compatible(model_type: str, checkpoint_path: Optional[str] = None):
    """Load SAM model with PyTorch 2.6 compatibility fix."""
    import torch
    from segment_anything import sam_model_registry
    
    # Monkey patch torch.load to use weights_only=False for SAM loading
    original_torch_load = torch.load
    
    def patched_torch_load(*args, **kwargs):
        # Force weights_only=False for SAM checkpoint loading
        kwargs['weights_only'] = False
        return original_torch_load(*args, **kwargs)
    
    try:
        # Temporarily replace torch.load
        torch.load = patched_torch_load
        
        # Load SAM model
        sam_model = sam_model_registry[model_type](checkpoint=checkpoint_path)
        
        return sam_model
        
    finally:
        # Restore original torch.load
        torch.load = original_torch_load

def main():
    print("🔍 Testing SAM model loading...")
    
    # Check available checkpoints
    checkpoint_paths = [
        "checkpoints/sam_vit_b_01ec64.pth",
        "checkpoints/sam_vit_b.pth",
        None  # Auto-download
    ]
    
    for checkpoint_path in checkpoint_paths:
        print(f"\n📁 Testing checkpoint: {checkpoint_path}")
        
        if checkpoint_path and not os.path.exists(checkpoint_path):
            print(f"❌ File not found: {checkpoint_path}")
            continue
            
        if checkpoint_path and os.path.getsize(checkpoint_path) < 1000:
            print(f"❌ File too small (corrupted): {checkpoint_path} ({os.path.getsize(checkpoint_path)} bytes)")
            continue
        
        try:
            print("🔄 Loading SAM model...")
            sam_model = load_sam_model_pytorch26_compatible("vit_b", checkpoint_path)
            print("✅ SAM model loaded successfully!")
            
            # Test predictor creation
            from segment_anything import SamPredictor
            predictor = SamPredictor(sam_model)
            print("✅ SAM predictor created successfully!")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to load SAM model: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n❌ All SAM loading attempts failed!")
    return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
