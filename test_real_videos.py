#!/usr/bin/env python3
"""
Test Quick Crop 50 with Real ICU Videos

This script helps you test the quick_crop50.py script with your actual ICU video dataset.
It will sample a few real videos and show you the cropped lip regions for visual inspection.
"""

import argparse
import os
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description='Test Quick Crop 50 with real ICU videos')
    parser.add_argument('--real_videos_dir', required=True, 
                       help='Path to your real ICU videos directory')
    parser.add_argument('--sample', type=int, default=10, 
                       help='Number of videos to sample for testing')
    args = parser.parse_args()
    
    real_videos_dir = Path(args.real_videos_dir)
    
    if not real_videos_dir.exists():
        print(f"❌ Directory not found: {real_videos_dir}")
        print("Please provide the correct path to your ICU videos.")
        return 1
    
    print(f"🎬 Testing Quick Crop 50 with real videos from: {real_videos_dir}")
    print(f"📊 Sampling {args.sample} videos for visual inspection")
    print()
    
    # Run quick_crop50.py with real videos
    cmd = f"""python scripts/quick_crop50.py \\
  --input_dir "{real_videos_dir}" \\
  --output_dir "data/real_video_crops" \\
  --sample {args.sample} \\
  --size 96 \\
  --shift_y_frac 0.08 \\
  --frames_per_video 3"""
    
    print("🚀 Running command:")
    print(cmd)
    print()
    
    # Execute the command
    exit_code = os.system(cmd)
    
    if exit_code == 0:
        print()
        print("✅ Processing complete!")
        print("📁 Cropped videos saved to: data/real_video_crops/")
        print("🎨 Visual gallery: reports/quick_crop_gallery.html")
        print()
        print("🔍 VISUAL INSPECTION:")
        print("1. Open reports/quick_crop_gallery.html in your browser")
        print("2. Check the side-by-side comparison:")
        print("   - Left: Original frames with green ROI box")
        print("   - Right: Final 96×96 cropped lip regions")
        print("3. Verify that the cropped regions show clear lip/mouth areas")
        print("4. Look for proper centering and good lip visibility")
        print()
        print("💡 If crops look good, you can proceed with the full pipeline!")
        print("💡 If adjustments needed, modify --shift_y_frac parameter")
        
        # Try to open gallery automatically (macOS)
        try:
            os.system("open reports/quick_crop_gallery.html")
        except:
            pass
            
    else:
        print("❌ Processing failed. Check the error messages above.")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())


# USAGE EXAMPLES:
#
# Test with your real ICU videos:
# python test_real_videos.py --real_videos_dir "/path/to/your/icu/videos" --sample 10
#
# The script will:
# 1. Run quick_crop50.py on your real videos
# 2. Generate visual gallery showing original vs cropped frames
# 3. Open the gallery in your browser for inspection
# 4. Show you exactly where the lip regions are being cropped
