#!/usr/bin/env python3
"""
QC visualization for landmark-driven lip cropping with MediaPipe landmark overlays.
MANDATORY: Must be run and visually inspected before full preprocessing.
"""

import os
import sys
import numpy as np
import cv2
import matplotlib.pyplot as plt
import glob
import random
from pathlib import Path

# Add src to path
sys.path.append('src')

# Try to import MediaPipe
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
    mp_face_mesh = mp.solutions.face_mesh
    mp_drawing = mp.solutions.drawing_utils
    mp_drawing_styles = mp.solutions.drawing_styles
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    print("⚠️ MediaPipe not available - using fallback approach")


def extract_landmark_driven_crop_simple(frame, img_size=96):
    """
    Simplified version of geometric landmark-driven cropping for QC (Python 3.13 compatible).
    1. Use basic face detection (Haar cascades) to find face
    2. Apply geometric heuristics to locate lip region
    3. Build tight bounding box + 20% padding
    """
    h, w = frame.shape[:2]

    # Try to use OpenCV's Haar cascade for face detection
    try:
        # Load Haar cascade for face detection
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

        # Convert to grayscale for face detection
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Detect faces
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)

        if len(faces) > 0:
            # Use the largest face
            areas = [w * h for (x, y, w, h) in faces]
            largest_idx = np.argmax(areas)
            fx, fy, fw, fh = faces[largest_idx]

            # Apply geometric heuristics to locate lip region within face
            # Lips are typically located in the lower third of the face
            # Horizontally centered, occupying about 40% of face width

            # Vertical positioning: lips are roughly 70-85% down from top of face
            lip_center_y = fy + int(fh * 0.77)  # 77% down from face top
            lip_height = max(int(fh * 0.15), 24)  # 15% of face height, min 24px

            # Horizontal positioning: centered, 40% of face width
            lip_center_x = fx + fw // 2
            lip_width = max(int(fw * 0.4), 32)  # 40% of face width, min 32px

            # Create tight bounding box around estimated lip region
            lip_x1 = lip_center_x - lip_width // 2
            lip_x2 = lip_center_x + lip_width // 2
            lip_y1 = lip_center_y - lip_height // 2
            lip_y2 = lip_center_y + lip_height // 2

            # Ensure lip region is within face bounds
            lip_x1 = max(fx, lip_x1)
            lip_x2 = min(fx + fw, lip_x2)
            lip_y1 = max(fy, lip_y1)
            lip_y2 = min(fy + fh, lip_y2)

            # Add 20% padding around the estimated lip region
            lip_w = lip_x2 - lip_x1
            lip_h = lip_y2 - lip_y1
            pad_x = int(lip_w * 0.2)
            pad_y = int(lip_h * 0.2)

            # Apply padding with frame bounds checking
            x1_pad = max(0, lip_x1 - pad_x)
            x2_pad = min(w, lip_x2 + pad_x)
            y1_pad = max(0, lip_y1 - pad_y)
            y2_pad = min(h, lip_y2 + pad_y)

            # Extract crop
            lip_crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]

            if lip_crop.size > 0:
                resized_crop = cv2.resize(lip_crop, (img_size, img_size))

                # Create face info for visualization
                face_info = {
                    'face_bbox': (fx, fy, fx + fw, fy + fh),
                    'lip_bbox': (lip_x1, lip_y1, lip_x2, lip_y2),
                    'padded_bbox': (x1_pad, y1_pad, x2_pad, y2_pad)
                }

                return resized_crop, face_info, "geometric_landmark_driven"

    except Exception as e:
        print(f"Face detection failed: {e}")

    # Fallback: center crop
    crop_size = min(h, w)
    start_h = (h - crop_size) // 2
    start_w = (w - crop_size) // 2
    center_crop = frame[start_h:start_h+crop_size, start_w:start_w+crop_size]
    resized_crop = cv2.resize(center_crop, (img_size, img_size))

    face_info = {
        'face_bbox': None,
        'lip_bbox': None,
        'padded_bbox': (start_w, start_h, start_w+crop_size, start_h+crop_size)
    }

    return resized_crop, face_info, "center_crop_fallback"


def draw_geometric_indicators_on_crop(crop, face_info, original_frame):
    """
    Draw geometric indicators on the cropped frame to show the lip region estimation.
    """
    if face_info is None:
        return crop

    crop_with_indicators = crop.copy()
    h_crop, w_crop = crop.shape[:2]
    h_orig, w_orig = original_frame.shape[:2]

    # Get bounding boxes
    face_bbox = face_info.get('face_bbox')
    lip_bbox = face_info.get('lip_bbox')
    padded_bbox = face_info.get('padded_bbox')

    if lip_bbox and padded_bbox:
        lip_x1, lip_y1, lip_x2, lip_y2 = lip_bbox
        pad_x1, pad_y1, pad_x2, pad_y2 = padded_bbox

        # Calculate the lip region within the crop
        # The crop corresponds to the padded bbox
        if pad_x2 > pad_x1 and pad_y2 > pad_y1:
            # Map lip bbox to crop coordinates
            lip_crop_x1 = int((lip_x1 - pad_x1) / (pad_x2 - pad_x1) * w_crop)
            lip_crop_x2 = int((lip_x2 - pad_x1) / (pad_x2 - pad_x1) * w_crop)
            lip_crop_y1 = int((lip_y1 - pad_y1) / (pad_y2 - pad_y1) * h_crop)
            lip_crop_y2 = int((lip_y2 - pad_y1) / (pad_y2 - pad_y1) * h_crop)

            # Ensure coordinates are within crop bounds
            lip_crop_x1 = max(0, min(lip_crop_x1, w_crop-1))
            lip_crop_x2 = max(lip_crop_x1+1, min(lip_crop_x2, w_crop))
            lip_crop_y1 = max(0, min(lip_crop_y1, h_crop-1))
            lip_crop_y2 = max(lip_crop_y1+1, min(lip_crop_y2, h_crop))

            # Draw green rectangle around estimated lip region
            cv2.rectangle(crop_with_indicators,
                         (lip_crop_x1, lip_crop_y1),
                         (lip_crop_x2, lip_crop_y2),
                         (0, 255, 0), 2)

            # Draw center point
            center_x = (lip_crop_x1 + lip_crop_x2) // 2
            center_y = (lip_crop_y1 + lip_crop_y2) // 2
            cv2.circle(crop_with_indicators, (center_x, center_y), 3, (0, 255, 0), -1)

    return crop_with_indicators


def load_video_frames(video_path, max_frames=4):
    """Load frames from video file"""
    try:
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        while len(frames) < max_frames:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        
        cap.release()
        return frames
    except Exception as e:
        print(f"Error loading {video_path}: {e}")
        return None


def create_qc_visualization(num_clips=10, frames_per_clip=4, output_file="qc_landmark_preview.png"):
    """
    Create QC visualization for landmark-driven cropping approach.
    Shows 96x96 crops with green lip landmark overlays.
    """
    
    print("🔍 Starting QC visualization for landmark-driven lip cropping...")
    
    # Find all video files in the local dataset
    video_pattern = "../top 10 dataset 6.9.25/**/*.webm"
    all_videos = glob.glob(video_pattern, recursive=True)
    
    if not all_videos:
        print("❌ No video files found!")
        return False
    
    print(f"📊 Found {len(all_videos)} video files")
    
    # Sample random videos
    sample_videos = random.sample(all_videos, min(100, len(all_videos)))
    
    processed_results = []
    
    for video_path in sample_videos:
        try:
            # Extract label from path
            path_parts = video_path.split('/')
            label = path_parts[-6] if len(path_parts) >= 6 else "unknown"
            
            # Load video frames
            frames = load_video_frames(video_path, max_frames=frames_per_clip)
            if not frames:
                continue
            
            print(f"✅ Processing {os.path.basename(video_path)} ({len(frames)} frames)")
            
            # Process frames with geometric landmark-driven cropping
            frame_results = []
            for frame in frames:
                crop, face_info, method = extract_landmark_driven_crop_simple(frame)

                # Draw geometric indicators on crop if available
                if face_info is not None:
                    crop_with_indicators = draw_geometric_indicators_on_crop(crop, face_info, frame)
                else:
                    crop_with_indicators = crop

                frame_results.append({
                    'crop': crop_with_indicators,
                    'face_info': face_info,
                    'method': method,
                    'original_frame': frame
                })
            
            if frame_results:
                processed_results.append({
                    'video_path': video_path,
                    'label': label,
                    'frames': frame_results
                })
        
        except Exception as e:
            print(f"⚠️ Error processing {video_path}: {e}")
            continue
    
    if not processed_results:
        print("❌ No clips could be processed!")
        return False
    
    # Select best examples (prioritize geometric landmark-driven results)
    geometric_clips = [clip for clip in processed_results
                      if any(f['method'] == 'geometric_landmark_driven' for f in clip['frames'])]

    if len(geometric_clips) >= num_clips:
        best_clips = geometric_clips[:num_clips]
    else:
        best_clips = processed_results[:num_clips]

    print(f"✅ Processed {len(processed_results)} clips, showing {len(best_clips)}")
    print(f"📊 Geometric landmark-driven clips: {len(geometric_clips)}")

    # Create visualization
    create_landmark_grid(best_clips, frames_per_clip, output_file)

    # Print statistics
    print_qc_statistics(processed_results)

    return True


def create_landmark_grid(best_clips, frames_per_clip, output_file):
    """Create grid visualization showing geometric landmark-driven cropping with green indicators"""

    num_clips = len(best_clips)

    # Create figure with single row per clip (just the crops with geometric indicators)
    fig, axes = plt.subplots(num_clips, frames_per_clip, figsize=(16, 4*num_clips))
    fig.suptitle('QC: Geometric Landmark-Driven Lip Cropping\n'
                 'Green rectangles = Estimated lip regions with geometric heuristics',
                 fontsize=16, fontweight='bold')
    
    # Handle single clip case
    if num_clips == 1:
        axes = axes.reshape(1, frames_per_clip)
    elif frames_per_clip == 1:
        axes = axes.reshape(num_clips, 1)
    
    for clip_idx, clip_data in enumerate(best_clips):
        clip_name = os.path.basename(clip_data['video_path']).replace('.webm', '')

        for frame_idx in range(frames_per_clip):
            if frame_idx < len(clip_data['frames']):
                frame_data = clip_data['frames'][frame_idx]
                crop_with_indicators = frame_data['crop']
                method = frame_data['method']

                ax = axes[clip_idx, frame_idx] if num_clips > 1 else axes[frame_idx]
                ax.imshow(cv2.cvtColor(crop_with_indicators, cv2.COLOR_BGR2RGB))
                ax.set_title(f'{clip_name}\nFrame {frame_idx+1} ({method})\nLabel: {clip_data["label"]}', fontsize=8)
                ax.axis('off')
            else:
                # Empty subplot
                ax = axes[clip_idx, frame_idx] if num_clips > 1 else axes[frame_idx]
                ax.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"💾 QC visualization saved to: {output_file}")


def print_qc_statistics(results):
    """Print QC statistics"""
    total_clips = len(results)
    
    labels = {}
    methods = {}
    
    for result in results:
        label = result['label']
        labels[label] = labels.get(label, 0) + 1
        
        for frame in result['frames']:
            method = frame['method']
            methods[method] = methods.get(method, 0) + 1
    
    print(f"\n📊 QC Statistics:")
    print("=" * 50)
    print(f"Total clips processed: {total_clips}")
    print(f"Labels distribution:")
    for label, count in sorted(labels.items()):
        print(f"  {label}: {count}")
    print(f"Methods used:")
    for method, count in sorted(methods.items()):
        print(f"  {method}: {count}")
    print("=" * 50)


if __name__ == "__main__":
    success = create_qc_visualization()
    
    if success:
        print("\n🎯 QC visualization complete!")
        print("📋 NEXT STEPS:")
        print("1. Open 'qc_landmark_preview.png' and visually inspect the crops")
        print("2. Check that green dots are positioned on lip landmarks")
        print("3. Verify that crops are tightly focused on the lip region")
        print("4. Confirm that landmark detection is working across different speakers")
        print("5. If satisfied, proceed with 100-sample trial run")
        print("6. If not satisfied, adjust landmark parameters and re-run QC")
    else:
        print("❌ QC visualization failed!")
        sys.exit(1)
