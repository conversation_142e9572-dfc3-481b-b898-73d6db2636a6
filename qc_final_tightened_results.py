#!/usr/bin/env python3
"""
Generate final QC visualizations for tightened MediaPipe landmark-anchored preprocessing.
Must be run in Python 3.11 environment.
"""

import os
import sys
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import glob
import random
from pathlib import Path


def create_final_analysis():
    """Create comprehensive final analysis"""
    
    # Load tightened results
    csv_path = 'data_processed_tightened/quality_logs/quality_scores.csv'
    if not os.path.exists(csv_path):
        print("❌ Tightened quality CSV not found!")
        return
    
    df = pd.read_csv(csv_path)
    
    # Create comprehensive analysis
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Final Tightened MediaPipe Landmark-Anchored Preprocessing Results', fontsize=16, fontweight='bold')
    
    # Coverage histogram
    ax1 = axes[0, 0]
    coverage_values = df['coverage_after'].values
    coverage_values = coverage_values[~np.isnan(coverage_values)]  # Remove NaN values
    
    if len(coverage_values) > 0:
        ax1.hist(coverage_values, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(0.35, color='red', linestyle='--', label='Min Accept (35%)')
        ax1.axvline(0.70, color='red', linestyle='--', label='Max Accept (70%)')
        ax1.axvline(0.45, color='green', linestyle='-', label='Target (45%)')
        ax1.set_xlabel('Lip Coverage After Adjustment')
        ax1.set_ylabel('Number of Clips')
        ax1.set_title('Distribution of Lip Coverage Values\n(Tightened: 90% mouth width, 75% aspect ratio)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim(0, 0.8)
    
    # Landmark detection rate by phrase
    ax2 = axes[0, 1]
    phrase_landmarks = df.groupby('phrase')['frames_with_landmarks_pct'].mean().sort_values()
    colors = ['red' if x == 0 else 'orange' if x < 50 else 'green' for x in phrase_landmarks.values]
    phrase_landmarks.plot(kind='bar', ax=ax2, color=colors)
    ax2.set_xlabel('Phrase')
    ax2.set_ylabel('Avg Landmark Detection %')
    ax2.set_title('Landmark Detection Rate by Phrase\n(Red=0%, Orange<50%, Green≥50%)')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 100)
    
    # Mouth width distribution (for clips with landmarks)
    ax3 = axes[1, 0]
    mouth_widths = df['mouth_width_px'].values
    mouth_widths = mouth_widths[~np.isnan(mouth_widths) & (mouth_widths > 0)]
    
    if len(mouth_widths) > 0:
        ax3.hist(mouth_widths, bins=10, alpha=0.7, color='lightgreen', edgecolor='black')
        ax3.set_xlabel('Mouth Width (pixels)')
        ax3.set_ylabel('Number of Clips')
        ax3.set_title(f'Distribution of Detected Mouth Widths\n({len(mouth_widths)} clips with landmarks)')
        ax3.grid(True, alpha=0.3)
    else:
        ax3.text(0.5, 0.5, 'No mouth widths detected', ha='center', va='center', transform=ax3.transAxes, fontsize=14)
        ax3.set_title('Distribution of Detected Mouth Widths\n(No landmarks detected)')
    
    # Summary statistics
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    total_clips = len(df)
    valid_clips = df['valid'].sum()
    clips_with_landmarks = (df['frames_with_landmarks_pct'] > 0).sum()
    clips_with_good_landmarks = (df['frames_with_landmarks_pct'] >= 50).sum()
    avg_landmarks_pct = df['frames_with_landmarks_pct'].mean()
    avg_coverage = df['coverage_after'].mean() if not df['coverage_after'].isna().all() else 0
    
    # Calculate success metrics
    success_rate = (clips_with_landmarks / total_clips * 100) if total_clips > 0 else 0
    good_detection_rate = (clips_with_good_landmarks / total_clips * 100) if total_clips > 0 else 0
    
    stats_text = f"""
    FINAL TIGHTENED PREPROCESSING RESULTS
    
    📊 Processing Statistics:
    Total Clips Processed: {total_clips}
    Clips with ANY landmarks: {clips_with_landmarks} ({success_rate:.1f}%)
    Clips with ≥50% landmarks: {clips_with_good_landmarks} ({good_detection_rate:.1f}%)
    Valid clips (≥90% landmarks): {valid_clips} ({valid_clips/total_clips*100:.1f}%)
    
    🎯 Landmark Detection:
    Average Detection Rate: {avg_landmarks_pct:.1f}%
    Best Detection Rate: {df['frames_with_landmarks_pct'].max():.1f}%
    Phrases with 0% detection: {(phrase_landmarks == 0).sum()}/10
    
    📏 Coverage Analysis:
    Average Coverage: {avg_coverage:.4f}
    Target Range [0.35-0.70]: {((coverage_values >= 0.35) & (coverage_values <= 0.70)).sum() if len(coverage_values) > 0 else 0} clips
    Below 0.35: {(coverage_values < 0.35).sum() if len(coverage_values) > 0 else 0} clips
    
    ⚠️  Key Issues Identified:
    • Very low landmark detection rates
    • Most clips have 0% landmark detection
    • Coverage values extremely low (<0.01)
    • Video quality/resolution challenges
    • Need alternative preprocessing approach
    
    💡 Recommendations:
    • Consider YOLO face detection + grid approach
    • Implement video quality filtering
    • Try different preprocessing techniques
    • Consider manual annotation for training data
    """
    
    ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, 
             fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig('qc_final_tightened_analysis.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 Final tightened analysis saved to: qc_final_tightened_analysis.png")


def create_sample_crops_preview():
    """Create preview of the few successful crops"""
    
    # Load some processed clips
    clips_dir = 'data_processed_tightened/clips'
    if not os.path.exists(clips_dir):
        print("❌ No processed clips found!")
        return
    
    clip_files = glob.glob(os.path.join(clips_dir, '*.npy'))
    
    if not clip_files:
        print("❌ No clip files found!")
        return
    
    # Load CSV to find clips with landmarks
    csv_path = 'data_processed_tightened/quality_logs/quality_scores.csv'
    df = pd.read_csv(csv_path)
    clips_with_landmarks = df[df['frames_with_landmarks_pct'] > 0]['clip_id'].tolist()
    
    print(f"📊 Found {len(clips_with_landmarks)} clips with landmarks: {clips_with_landmarks}")
    
    # Find corresponding clip files
    successful_clips = []
    for clip_id in clips_with_landmarks:
        clip_file = os.path.join(clips_dir, f'{clip_id}.npy')
        if os.path.exists(clip_file):
            successful_clips.append((clip_id, clip_file))
    
    if not successful_clips:
        print("❌ No successful clip files found!")
        return
    
    # Create preview grid
    num_clips = min(len(successful_clips), 4)  # Show up to 4 clips
    fig, axes = plt.subplots(num_clips, 4, figsize=(16, 4*num_clips))
    fig.suptitle('Tightened MediaPipe Lip Crops - Successful Cases Only', 
                 fontsize=16, fontweight='bold')
    
    if num_clips == 1:
        axes = axes.reshape(1, 4)
    
    for clip_idx, (clip_id, clip_file) in enumerate(successful_clips[:num_clips]):
        try:
            clip_array = np.load(clip_file)
            
            # Get clip stats
            clip_stats = df[df['clip_id'] == clip_id].iloc[0]
            landmarks_pct = clip_stats['frames_with_landmarks_pct']
            coverage = clip_stats['coverage_after']
            mouth_width = clip_stats['mouth_width_px']
            
            # Show 4 frames from the clip
            frame_indices = [0, len(clip_array)//3, 2*len(clip_array)//3, len(clip_array)-1]
            
            for frame_idx, frame_pos in enumerate(frame_indices):
                if frame_pos < len(clip_array):
                    frame = clip_array[frame_pos]
                    
                    ax = axes[clip_idx, frame_idx]
                    ax.imshow(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                    ax.set_title(f'{clip_id}\nFrame {frame_pos}\nLandmarks: {landmarks_pct:.1f}%\nCoverage: {coverage:.4f}', fontsize=8)
                    ax.axis('off')
                    
                    # Draw estimated lip region overlay
                    h, w = frame.shape[:2]
                    center_x, center_y = w // 2, int(h * 0.6)
                    
                    # Use actual mouth width if available
                    if not np.isnan(mouth_width):
                        # Scale mouth width to crop coordinates
                        lip_width = int(w * 0.9)  # 90% width for tightened
                    else:
                        lip_width = int(w * 0.5)  # Fallback
                    
                    lip_height = int(lip_width * 0.3)
                    
                    x1 = center_x - lip_width // 2
                    x2 = center_x + lip_width // 2
                    y1 = center_y - lip_height // 2
                    y2 = center_y + lip_height // 2
                    
                    # Draw rectangle overlay
                    rect = plt.Rectangle((x1, y1), lip_width, lip_height, 
                                       linewidth=2, edgecolor='lime', facecolor='none', alpha=0.7)
                    ax.add_patch(rect)
                    
                    # Draw center point
                    ax.plot(center_x, center_y, 'ro', markersize=4)
        
        except Exception as e:
            print(f"Error processing {clip_file}: {e}")
    
    plt.tight_layout()
    plt.savefig('qc_tightened_successful_crops.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 Successful crops preview saved to: qc_tightened_successful_crops.png")


def main():
    """Main QC function"""
    print("🎯 Final QC Analysis for Tightened MediaPipe Preprocessing")
    print("=" * 60)
    
    # Check MediaPipe availability
    try:
        import mediapipe as mp
        print("✅ MediaPipe available")
    except ImportError:
        print("❌ MediaPipe not available! Please run in Python 3.11 environment")
        return False
    
    # Create final analysis
    create_final_analysis()
    
    # Create preview of successful crops
    create_sample_crops_preview()
    
    print("\n✅ Final QC analysis completed!")
    print("📁 Generated files:")
    print("  - qc_final_tightened_analysis.png")
    print("  - qc_tightened_successful_crops.png")
    
    print("\n📋 Summary:")
    print("  - MediaPipe landmark detection working but with low success rate")
    print("  - Histogram equalization enables some landmark detection")
    print("  - Video quality/resolution appears to be limiting factor")
    print("  - Coverage values very low due to small detected mouth regions")
    print("  - Alternative approaches may be needed for robust preprocessing")
    
    return True


if __name__ == "__main__":
    main()
