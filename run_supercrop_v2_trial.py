#!/usr/bin/env python3
"""
Run QC trial for top_middle_supercrop_v2 mode.

F) QC & visual gate (MANDATORY)
- Process a stratified TRIAL of 100 clips (≥10 per phrase).
- Save:
   1) qc_supercrop_v2_preview.png — 10 clips × 4 frames (96×96 crops).
   2) qc_supercrop_v2_overlays.png — the same 10 clips with boxes drawn on the original frames;
      also plot an edge-energy heatmap across vertical positions for one frame per clip.
   3) qc_supercrop_v2_failures.png — worst 12 by score/motion.
   4) CSV: data_processed_supercrop_v2/quality.csv
"""

import os
import sys
import pandas as pd
import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import argparse
from tqdm import tqdm
import glob

# Add src to path
sys.path.append('src')
from preprocess import VideoPreprocessor
from utils import load_config, setup_logging


def create_stratified_trial_manifest(original_manifest: str, output_manifest: str, 
                                   target_total: int = 100, min_per_phrase: int = 10):
    """Create a stratified sample manifest for the trial"""
    df = pd.read_csv(original_manifest)
    
    # Get unique phrases
    phrases = df['phrase'].unique()
    print(f"📊 Found {len(phrases)} phrases: {list(phrases)}")
    
    # Calculate samples per phrase
    samples_per_phrase = max(min_per_phrase, target_total // len(phrases))
    
    # Sample from each phrase
    sampled_dfs = []
    for phrase in phrases:
        phrase_df = df[df['phrase'] == phrase]
        n_available = len(phrase_df)
        n_sample = min(samples_per_phrase, n_available)
        
        if n_sample > 0:
            sampled = phrase_df.sample(n=n_sample, random_state=42)
            sampled_dfs.append(sampled)
            print(f"  {phrase}: {n_sample}/{n_available} samples")
    
    # Combine and save
    trial_df = pd.concat(sampled_dfs, ignore_index=True)
    trial_df.to_csv(output_manifest, index=False)
    
    print(f"✅ Created trial manifest with {len(trial_df)} samples")
    print(f"💾 Saved to: {output_manifest}")
    
    return output_manifest


def run_preprocessing_trial(config_path: str, trial_manifest: str):
    """Run preprocessing on trial data"""
    print("🚀 Running supercrop_v2 preprocessing trial...")
    
    # Load config
    config = load_config(config_path)
    
    # Create preprocessor with supercrop_v2 mode
    preprocessor = VideoPreprocessor(config, mode='top_middle_supercrop_v2')
    
    # Process trial manifest
    preprocessor.process_from_manifest(trial_manifest)
    
    print("✅ Preprocessing trial completed!")


def generate_qc_visualizations():
    """Generate all required QC visualizations"""
    print("🎨 Generating QC visualizations...")
    
    # Load quality data
    quality_csv = 'data_processed_supercrop_v2/quality_logs/quality_scores.csv'
    if not os.path.exists(quality_csv):
        print(f"❌ Quality CSV not found: {quality_csv}")
        return
    
    df = pd.read_csv(quality_csv)
    print(f"📊 Loaded quality data for {len(df)} clips")
    
    # Generate visualizations
    generate_preview_visualization(df)
    generate_overlay_visualization(df)
    generate_failures_visualization(df)
    
    print("✅ QC visualizations completed!")


def generate_preview_visualization(df: pd.DataFrame):
    """Generate qc_supercrop_v2_preview.png — 10 clips × 4 frames (96×96 crops)"""
    print("📸 Generating preview visualization...")
    
    # Select 10 clips (stratified by phrase if possible)
    phrases = df['phrase'].unique()
    selected_clips = []
    
    for phrase in phrases[:10]:  # Up to 10 phrases
        phrase_clips = df[df['phrase'] == phrase]
        if len(phrase_clips) > 0:
            # Select best clip from this phrase (highest score)
            if 'score' in phrase_clips.columns:
                best_clip = phrase_clips.loc[phrase_clips['score'].idxmax()]
            else:
                best_clip = phrase_clips.iloc[0]
            selected_clips.append(best_clip)
    
    # If we have fewer than 10, fill with random clips
    while len(selected_clips) < 10 and len(selected_clips) < len(df):
        remaining = df[~df['clip_id'].isin([c['clip_id'] for c in selected_clips])]
        if len(remaining) > 0:
            selected_clips.append(remaining.iloc[0])
        else:
            break
    
    # Create visualization
    fig, axes = plt.subplots(len(selected_clips), 4, figsize=(16, 4*len(selected_clips)))
    fig.suptitle('Supercrop V2 Preview - 10 Clips × 4 Frames (96×96 crops)', 
                 fontsize=16, fontweight='bold')
    
    if len(selected_clips) == 1:
        axes = axes.reshape(1, 4)
    
    for clip_idx, clip_row in enumerate(selected_clips):
        clip_id = clip_row['clip_id']
        phrase = clip_row['phrase']
        
        # Load clip data
        clip_file = f'data_processed_supercrop_v2/clips/{clip_id}.npy'
        if os.path.exists(clip_file):
            clip_data = np.load(clip_file)
            
            # Show 4 frames: first, 1/3, 2/3, last
            frame_indices = [0, len(clip_data)//3, 2*len(clip_data)//3, len(clip_data)-1]
            
            for frame_idx, frame_pos in enumerate(frame_indices):
                if frame_pos < len(clip_data):
                    frame = clip_data[frame_pos]
                    
                    ax = axes[clip_idx, frame_idx]
                    ax.imshow(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                    ax.set_title(f'{phrase}\nFrame {frame_pos}', fontsize=8)
                    ax.axis('off')
                else:
                    axes[clip_idx, frame_idx].axis('off')
        else:
            # Show placeholder
            for frame_idx in range(4):
                axes[clip_idx, frame_idx].text(0.5, 0.5, f'Clip {clip_id}\nNot Found', 
                                             ha='center', va='center', 
                                             transform=axes[clip_idx, frame_idx].transAxes)
                axes[clip_idx, frame_idx].axis('off')
    
    plt.tight_layout()
    plt.savefig('qc_supercrop_v2_preview.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 Saved: qc_supercrop_v2_preview.png")


def generate_overlay_visualization(df: pd.DataFrame):
    """Generate qc_supercrop_v2_overlays.png with boxes and edge heatmaps"""
    print("📊 Generating overlay visualization...")
    
    # This is a placeholder - would need access to original frames and processing details
    # For now, create a simple summary plot
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Supercrop V2 Analysis - Overlays and Edge Energy', fontsize=16, fontweight='bold')
    
    # Score distribution
    ax1 = axes[0, 0]
    if 'score' in df.columns:
        ax1.hist(df['score'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_xlabel('Lipness Score')
        ax1.set_ylabel('Number of Clips')
        ax1.set_title('Distribution of Lipness Scores')
        ax1.grid(True, alpha=0.3)
    
    # Motion score vs Edge variance
    ax2 = axes[0, 1]
    if 'motion_score' in df.columns and 'edge_var' in df.columns:
        scatter = ax2.scatter(df['motion_score'], df['edge_var'], 
                            c=df.get('valid', 1), cmap='RdYlGn', alpha=0.7)
        ax2.set_xlabel('Motion Score')
        ax2.set_ylabel('Edge Variance')
        ax2.set_title('Motion Score vs Edge Variance')
        ax2.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax2, label='Valid')
    
    # Phrase performance
    ax3 = axes[1, 0]
    if 'phrase' in df.columns and 'score' in df.columns:
        phrase_scores = df.groupby('phrase')['score'].mean().sort_values()
        phrase_scores.plot(kind='bar', ax=ax3, color='lightgreen')
        ax3.set_xlabel('Phrase')
        ax3.set_ylabel('Average Score')
        ax3.set_title('Average Lipness Score by Phrase')
        ax3.tick_params(axis='x', rotation=45)
        ax3.grid(True, alpha=0.3)
    
    # Summary statistics
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    total_clips = len(df)
    valid_clips = df.get('valid', pd.Series([True]*len(df))).sum()
    avg_score = df.get('score', pd.Series([0]*len(df))).mean()
    avg_motion = df.get('motion_score', pd.Series([0]*len(df))).mean()
    avg_edge_var = df.get('edge_var', pd.Series([0]*len(df))).mean()
    
    stats_text = f"""
    SUPERCROP V2 TRIAL RESULTS
    
    📊 Processing Statistics:
    Total Clips: {total_clips}
    Valid Clips: {valid_clips} ({valid_clips/total_clips*100:.1f}%)
    
    🎯 Quality Metrics:
    Average Lipness Score: {avg_score:.3f}
    Average Motion Score: {avg_motion:.6f}
    Average Edge Variance: {avg_edge_var:.6f}
    
    📏 Thresholds:
    Motion Threshold: 0.0098
    Edge Var Threshold: 0.002
    
    ✅ Ready for full processing if:
    - Valid rate > 80%
    - Good score distribution
    - Reasonable motion/edge metrics
    """
    
    ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, 
             fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig('qc_supercrop_v2_overlays.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 Saved: qc_supercrop_v2_overlays.png")


def generate_failures_visualization(df: pd.DataFrame):
    """Generate qc_supercrop_v2_failures.png — worst 12 by score/motion"""
    print("⚠️  Generating failures visualization...")
    
    # Find worst clips by score and motion
    worst_by_score = df.nsmallest(6, 'score') if 'score' in df.columns else df.head(6)
    worst_by_motion = df.nsmallest(6, 'motion_score') if 'motion_score' in df.columns else df.head(6)
    
    # Combine and deduplicate
    worst_clips = pd.concat([worst_by_score, worst_by_motion]).drop_duplicates(subset=['clip_id'])
    worst_clips = worst_clips.head(12)  # Limit to 12
    
    # Create visualization
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    fig.suptitle('Supercrop V2 Failures - Worst 12 Clips by Score/Motion', 
                 fontsize=16, fontweight='bold')
    
    axes = axes.flatten()
    
    for idx, (_, clip_row) in enumerate(worst_clips.iterrows()):
        if idx >= 12:
            break
            
        clip_id = clip_row['clip_id']
        phrase = clip_row['phrase']
        score = clip_row.get('score', 0)
        motion = clip_row.get('motion_score', 0)
        
        ax = axes[idx]
        
        # Try to load and show middle frame
        clip_file = f'data_processed_supercrop_v2/clips/{clip_id}.npy'
        if os.path.exists(clip_file):
            clip_data = np.load(clip_file)
            if len(clip_data) > 0:
                middle_frame = clip_data[len(clip_data)//2]
                ax.imshow(cv2.cvtColor(middle_frame, cv2.COLOR_BGR2RGB))
        
        ax.set_title(f'{phrase}\nScore: {score:.3f}\nMotion: {motion:.6f}', fontsize=8)
        ax.axis('off')
    
    # Hide unused subplots
    for idx in range(len(worst_clips), 12):
        axes[idx].axis('off')
    
    plt.tight_layout()
    plt.savefig('qc_supercrop_v2_failures.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 Saved: qc_supercrop_v2_failures.png")


def main():
    parser = argparse.ArgumentParser(description='Run supercrop_v2 QC trial')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--manifest', required=True, help='Original manifest CSV file')
    parser.add_argument('--skip_processing', action='store_true', 
                       help='Skip preprocessing, only generate visualizations')
    args = parser.parse_args()
    
    print("🎯 Supercrop V2 QC Trial")
    print("=" * 50)
    
    # Create trial manifest
    trial_manifest = 'trial_supercrop_v2_manifest.csv'
    if not args.skip_processing:
        create_stratified_trial_manifest(args.manifest, trial_manifest)
        
        # Run preprocessing
        run_preprocessing_trial(args.config, trial_manifest)
    
    # Generate QC visualizations
    generate_qc_visualizations()
    
    print("\n✅ Supercrop V2 QC trial completed!")
    print("📁 Generated files:")
    print("  - qc_supercrop_v2_preview.png")
    print("  - qc_supercrop_v2_overlays.png") 
    print("  - qc_supercrop_v2_failures.png")
    print("  - data_processed_supercrop_v2/quality_logs/quality_scores.csv")
    print("\n🔍 Review these files before proceeding to full processing.")


if __name__ == "__main__":
    main()
