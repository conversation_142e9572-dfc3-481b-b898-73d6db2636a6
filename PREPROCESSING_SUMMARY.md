# Video Preprocessing for Classifier Training - Summary

## 🎯 **Objective**
Convert high-quality motion-detected cropped videos from label audit results into standardized numpy arrays for CNN classifier training.

## 📊 **Processing Results**

### **Input Selection**
- **Source**: `data/label_screened_v1/keep/` (1,868 high-quality videos from label audit)
- **Selection Method**: Lowest suspicion scores per phrase (highest confidence)
- **Selected Videos**: 7 representative videos (one per phrase)

### **Selected Videos with Quality Scores**
| Phrase | Video File | Suspicion Score |
|--------|------------|-----------------|
| doctor | call_the_doctor__useruser01__40to64__female__caucasian__20250723T052421.mp4 | 0.167 |
| glasses | glasses__useruser01__40to64__female__caucasian__20250723T042749.mp4 | 0.163 |
| help | help__useruser01__40to64__female__caucasian__20250830T112438.mp4 | 0.183 |
| i_need_to_move | i_need_to_move__useruser01__65plus__male__asian__20250716T061849.mp4 | 0.165 |
| my_mouth_is_dry | my_mouth_is_dry__useruser01__65plus__female__caucasian__20250730T062553.mp4 | 0.175 |
| phone | phone__useruser01__40to64__female__caucasian__20250830T112027.mp4 | 0.157 |
| pillow | pillow__useruser01__65plus__female__caucasian__20250730T033810.mp4 | 0.177 |

## 🔧 **Preprocessing Pipeline**

### **1. Video Loading & Resizing**
- **Target Resolution**: 96×96 pixels (matches segmentation output)
- **Color Format**: RGB (3 channels)
- **Frame Extraction**: All frames from each video

### **2. Frame Count Standardization**
- **Method**: Standardized to median frame count (39 frames)
- **Padding**: Repeat last frame for shorter videos
- **Truncation**: Evenly spaced frame selection for longer videos

| Phrase | Original Frames | Final Frames | Action |
|--------|----------------|--------------|---------|
| doctor | 39 | 39 | No change |
| glasses | 30 | 39 | Padded (+9) |
| help | 55 | 39 | Truncated (-16) |
| i_need_to_move | 87 | 39 | Truncated (-48) |
| my_mouth_is_dry | 23 | 39 | Padded (+16) |
| phone | 46 | 39 | Truncated (-7) |
| pillow | 34 | 39 | Padded (+5) |

### **3. Pixel Value Normalization**
- **Range**: [0, 1] (divided by 255)
- **Data Type**: float32
- **Memory Efficient**: Optimized for CNN training

### **4. Contrast Enhancement**
- **Method**: CLAHE (Contrast Limited Adaptive Histogram Equalization)
- **Applied Per**: Frame and RGB channel
- **Purpose**: Improve feature visibility for training

## 📁 **Output Structure**

```
data/classifier_ready/
├── preprocessed_videos/
│   ├── doctor_preprocessed.npy          # (39, 96, 96, 3)
│   ├── glasses_preprocessed.npy         # (39, 96, 96, 3)
│   ├── help_preprocessed.npy            # (39, 96, 96, 3)
│   ├── i_need_to_move_preprocessed.npy  # (39, 96, 96, 3)
│   ├── my_mouth_is_dry_preprocessed.npy # (39, 96, 96, 3)
│   ├── phone_preprocessed.npy           # (39, 96, 96, 3)
│   └── pillow_preprocessed.npy          # (39, 96, 96, 3)
├── labels.json                          # Phrase to class_id mapping
├── training_manifest.csv               # Metadata and file paths
└── visualizations/                     # Sample frame images
    ├── sample_frames.png               # Grid of all phrases
    ├── doctor_frames.png               # Individual phrase sequences
    └── ...
```

## 📊 **Data Statistics**

### **Overall Statistics**
- **Total Videos**: 7
- **Total Frames**: 273 (39 × 7)
- **Total Size**: 28.8 MB
- **Shape Consistency**: ✅ All (39, 96, 96, 3)
- **Data Type**: ✅ All float32
- **Value Range**: [0.008, 1.000] (properly normalized)

### **Per-Phrase Statistics**
| Phrase | Mean | Std | Min | Max | Size (MB) |
|--------|------|-----|-----|-----|-----------|
| doctor | 0.292 | 0.153 | 0.016 | 0.714 | 4.11 |
| glasses | 0.393 | 0.205 | 0.016 | 0.855 | 4.11 |
| help | 0.335 | 0.280 | 0.016 | 0.851 | 4.11 |
| i_need_to_move | 0.291 | 0.249 | 0.008 | 1.000 | 4.11 |
| my_mouth_is_dry | 0.107 | 0.158 | 0.012 | 0.714 | 4.11 |
| phone | 0.186 | 0.250 | 0.012 | 0.969 | 4.11 |
| pillow | 0.181 | 0.217 | 0.016 | 0.792 | 4.11 |

## 🏷️ **Label Mapping**

```json
{
  "doctor": 0,
  "glasses": 1,
  "help": 2,
  "i_need_to_move": 3,
  "my_mouth_is_dry": 4,
  "phone": 5,
  "pillow": 6
}
```

## 🚀 **Usage for Classifier Training**

### **Loading Data**
```python
import numpy as np
import pandas as pd
import json

# Load labels mapping
with open('data/classifier_ready/labels.json', 'r') as f:
    labels_mapping = json.load(f)

# Load manifest
manifest = pd.read_csv('data/classifier_ready/training_manifest.csv')

# Load training data
X_data = []
y_data = []

for _, row in manifest.iterrows():
    array = np.load(row['file_path'])
    X_data.append(array)
    y_data.append(row['class_id'])

X = np.array(X_data)  # Shape: (7, 39, 96, 96, 3)
y = np.array(y_data)  # Shape: (7,)
```

### **Model Compatibility**
- **3D CNNs**: Direct input (frames, height, width, channels)
- **2D CNNs**: Process frames individually with TimeDistributed
- **RNNs**: Extract features per frame, then temporal modeling
- **MobileNet/EfficientNet**: Compatible with 96×96 input size

## ✅ **Quality Validation**

### **Validation Results**
- ✅ **Shape Consistency**: All arrays (39, 96, 96, 3)
- ✅ **Data Type**: All float32
- ✅ **Value Range**: Properly normalized [0, 1]
- ✅ **No Missing Data**: All 7 phrases successfully processed
- ✅ **Visualizations**: Generated for quality inspection

### **Generated Visualizations**
- **Sample Frames Grid**: Overview of all phrases
- **Individual Sequences**: Frame-by-frame progression per phrase
- **Quality Check**: Visual confirmation of preprocessing success

## 🎯 **Next Steps**

1. **Scale Up Data Collection**: Need more samples per phrase for robust training
2. **Data Augmentation**: Implement rotation, brightness, contrast variations
3. **Model Architecture**: Design CNN optimized for lip-reading
4. **Training Pipeline**: Implement proper train/validation/test splits
5. **Performance Metrics**: Accuracy, precision, recall per phrase

## 📝 **Scripts Created**

1. **`scripts/preprocess_for_classifier.py`** - Main preprocessing script
2. **`scripts/validate_preprocessed_data.py`** - Data validation and visualization
3. **`scripts/train_simple_classifier.py`** - Proof-of-concept CNN training
4. **`run_preprocessing.py`** - Simple runner script

## 🎉 **Success Metrics**

- ✅ **High-Quality Input**: Selected lowest suspicion score videos
- ✅ **Standardized Format**: Consistent 96×96×3 RGB format
- ✅ **Proper Normalization**: [0,1] range for CNN training
- ✅ **Enhanced Contrast**: CLAHE preprocessing for better features
- ✅ **Complete Pipeline**: From raw videos to training-ready arrays
- ✅ **Validation Confirmed**: All quality checks passed

The preprocessing pipeline successfully converts high-quality motion-detected videos into standardized numpy arrays ready for CNN classifier training, with comprehensive validation and visualization capabilities.
