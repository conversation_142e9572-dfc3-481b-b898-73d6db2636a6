# AI Lip-Reading Dataset Processing Pipeline

A comprehensive pipeline for processing video datasets for lip-reading AI model training, featuring motion-guided cropping and quality screening.

## Project Overview

This project implements a sophisticated video processing pipeline designed for ICU lip-reading applications. The system processes raw video datasets through multiple stages to produce high-quality, motion-guided cropped videos focused on lip regions.

## Key Features

- **Motion-Guided Cropping**: Advanced optical flow analysis to detect lip movement hotspots
- **Quality Screening**: Automated filtering using MediaPipe face detection and motion analysis
- **Batch Processing**: Multiprocessing support for efficient large-scale video processing
- **Visual Galleries**: HTML galleries for manual quality inspection
- **Comprehensive Logging**: Detailed processing logs and quality metrics

## Pipeline Stages

### Stage 1: Initial Cropping
- Geometric cropping to top-middle third with downward shift
- Letterboxing to square format (96×96 pixels)
- Basic quality filtering

### Stage 1M: Motion-Guided Refinement
- Optical flow analysis to detect lip movement patterns
- Motion hotspot detection and ROI refinement
- MediaPipe landmark confirmation
- Fallback to geometric crop when motion is insufficient

### Quality Screening
- Motion share analysis (minimum 12% of total motion in mouth region)
- Heatmap intensity thresholds (minimum 100 intensity units)
- Face detection rate validation
- Automated keep/review/remove recommendations

## Scripts Overview

### Core Processing Scripts
- `scripts/quick_crop.py` - Fast sanity check cropping (20 video sample)
- `scripts/batch_crop_7phrases.py` - Batch crop top 7 phrases
- `scripts/motion_guided_crop.py` - Motion-guided cropping refinement
- `scripts/screen_crops.py` - Quality screening and filtering

### Analysis Scripts
- `scripts/analyze_motion_quality.py` - Comprehensive quality analysis
- `scripts/create_suspect_gallery.py` - Visual gallery for low-quality videos
- `scripts/clean_dataset.py` - Dataset cleaning and final validation

### Utility Scripts
- `scripts/backup_to_github.py` - Project backup and version control

## Dataset Statistics

### Final Cleaned Dataset
- **Total Videos**: 2,141 high-quality motion-guided crops
- **Quality Rate**: 100% motion-guided (no fallback crops)
- **Coverage**: 7 key phrases for ICU communication
- **Format**: 96×96 pixel MP4 videos
- **Removal Rate**: 5.9% (135 low-quality videos removed)

### Phrase Distribution
- pillow: ~384 videos
- phone: ~400 videos  
- i_need_to_move: ~249 videos
- doctor: ~337 videos
- glasses: ~328 videos
- my_mouth_is_dry: ~235 videos
- help: ~343 videos

## Quality Metrics

### Motion Analysis Results
- **Average Motion Share**: 0.642 (target: >0.12)
- **Average Heatmap Intensity**: 388.6 (target: >100)
- **Motion-Guided Success Rate**: 97.4%
- **Fallback Rate**: 2.6% (all removed in final dataset)

### Screening Results
- **Clean Videos**: 94.1% (kept)
- **Review Candidates**: 1.9% (removed)
- **Removal Candidates**: 1.3% (removed)
- **False Positive Rate**: <1%

## Usage Examples

### Quick Sanity Check (20 videos)
```bash
python scripts/quick_crop.py \
  --input_dir "~/Desktop/top10dataset6.9.25" \
  --output_dir "data/quick_stage1_cropped" \
  --sample 20 --size 96 --seed 42
```

### Full Motion-Guided Processing
```bash
# 1. Batch crop all videos for top 7 phrases
python scripts/batch_crop_7phrases.py \
  --input_dir "~/Desktop/top10dataset6.9.25" \
  --phrases "pillow,phone,i_need_to_move,doctor,glasses,my_mouth_is_dry,help" \
  --output_dir "data/stage1_cropped_top7" \
  --workers 4

# 2. Apply motion-guided refinement
python scripts/motion_guided_crop.py \
  --input_dir "data/stage1_cropped_top7" \
  --output_dir "data/stage1M_motion_refined_full" \
  --gallery "reports/stage1M_full_gallery.html" \
  --summary "reports/stage1M_full_summary.csv" \
  --workers 4 --sample 0

# 3. Quality analysis and screening
python scripts/analyze_motion_quality.py \
  --summary "reports/stage1M_full_summary.csv" \
  --output_candidates "reports/low_motion_candidates.csv" \
  --output_summary "reports/motion_quality_summary.txt"

# 4. Clean dataset
python scripts/clean_dataset.py \
  --candidates "reports/low_motion_candidates.csv" \
  --dataset_dir "data/stage1M_motion_refined_full" \
  --gallery "reports/cleaned_dataset_inspection.html"
```

## Requirements

```bash
pip install -r requirements.txt
```

Key dependencies:
- opencv-python
- numpy
- mediapipe
- tqdm
- jinja2
- pandas

## Output Structure

```
data/
├── stage1M_motion_refined_full/    # Final cleaned dataset (2,141 videos)
└── quick_stage1_cropped/           # Quick test outputs

reports/
├── stage1M_full_summary.csv        # Complete processing metrics
├── low_motion_candidates.csv       # Quality screening results
├── motion_quality_summary.txt      # Executive summary
├── cleaned_dataset_inspection.html # Final quality gallery
├── suspect_gallery.html            # Low-quality video gallery
└── dataset_cleaning_log.txt        # Cleaning operation log

scripts/
├── motion_guided_crop.py           # Core motion analysis
├── batch_crop_7phrases.py          # Batch processing
├── analyze_motion_quality.py       # Quality analysis
├── clean_dataset.py               # Dataset cleaning
└── [other utility scripts]
```

## Technical Details

### Motion-Guided Cropping Algorithm
1. **Optical Flow Analysis**: Dense optical flow using Farneback method
2. **Global Motion Removal**: Subtract median flow to isolate local motion
3. **Motion Hotspot Detection**: Threshold at 85th percentile, morphological operations
4. **Temporal Smoothing**: EMA smoothing with coefficient 0.2
5. **MediaPipe Confirmation**: Face mesh validation with IoU analysis
6. **Quality Scoring**: Composite score based on motion share, heatmap intensity, detection rate

### Quality Screening Criteria
- **Motion Share**: ≥12% of total motion in mouth region
- **Heatmap Intensity**: ≥100 intensity units
- **Detection Rate**: ≥60% face detection (when available)
- **Fallback Penalty**: Automatic flagging of geometric fallback crops

## Results and Validation

The pipeline successfully processed 2,276 videos and produced a cleaned dataset of 2,141 high-quality motion-guided crops with:
- 100% motion-guided cropping (no fallback crops)
- Verified lip region targeting through visual inspection
- Comprehensive quality metrics and traceability
- Ready for lip-reading model training

## Author

Created for ICU lip-reading AI application development.
Processing pipeline optimized for medical communication phrases.

## License

[Specify license here]
