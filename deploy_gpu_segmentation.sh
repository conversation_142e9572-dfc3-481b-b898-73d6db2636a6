#!/bin/bash
# GPU Deployment Script for Stage-3 Segmentation
# Run this script on the A40 GPU server

set -e  # Exit on any error

echo "🚀 Setting up GPU environment for Stage-3 Segmentation..."

# Check if running in tmux
if [ -z "$TMUX" ]; then
    echo "⚠️  Not running in tmux. Starting tmux session..."
    tmux new-session -d -s stage3_segmentation
    tmux send-keys -t stage3_segmentation "cd $(pwd) && bash $0" Enter
    echo "✅ Started tmux session 'stage3_segmentation'"
    echo "📋 To attach: tmux attach -t stage3_segmentation"
    exit 0
fi

echo "✅ Running in tmux session: $TMUX"

# Update system and install dependencies
echo "📦 Installing system dependencies..."
apt-get update -qq
apt-get install -y python3-pip git wget unzip htop nvtop

# Upgrade pip
echo "🔧 Upgrading pip..."
python3 -m pip install --upgrade pip

# Install PyTorch with CUDA support
echo "🔥 Installing PyTorch with CUDA..."
python3 -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Install other ML dependencies
echo "📚 Installing ML dependencies..."
python3 -m pip install ultralytics==8.2.*
python3 -m pip install opencv-python-headless==4.9.*
python3 -m pip install numpy scipy scikit-learn jinja2 tqdm

# Install Segment Anything
echo "🎯 Installing Segment Anything..."
python3 -m pip install git+https://github.com/facebookresearch/segment-anything.git

# Verify GPU setup
echo "🎮 Verifying GPU setup..."
python3 -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA version: {torch.version.cuda}')
    print(f'GPU count: {torch.cuda.device_count()}')
    for i in range(torch.cuda.device_count()):
        print(f'GPU {i}: {torch.cuda.get_device_name(i)}')
        props = torch.cuda.get_device_properties(i)
        print(f'  Memory: {props.total_memory / 1024**3:.1f}GB')
else:
    print('❌ CUDA not available!')
    exit(1)
"

# Check if required files exist
echo "📁 Checking required files..."
required_files=(
    "scripts/segment_yolo_sam.py"
    "data/stage1M_motion_refined_full"
    "yolov8n.pt"
    "checkpoints/sam_vit_b_01ec64.pth"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ ! -e "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -ne 0 ]; then
    echo "❌ Missing required files:"
    printf '  %s\n' "${missing_files[@]}"
    echo ""
    echo "📋 Please ensure the following are available:"
    echo "  1. scripts/segment_yolo_sam.py (segmentation script)"
    echo "  2. data/stage1M_motion_refined_full/ (input videos)"
    echo "  3. yolov8n.pt (YOLO model)"
    echo "  4. checkpoints/sam_vit_b_01ec64.pth (SAM checkpoint)"
    exit 1
fi

echo "✅ All required files found"

# Count input videos
video_count=$(find data/stage1M_motion_refined_full -name "*.mp4" -o -name "*.mov" -o -name "*.mkv" -o -name "*.avi" -o -name "*.webm" | wc -l)
echo "📹 Found $video_count input videos"

# Create output directories
echo "📁 Creating output directories..."
mkdir -p data/stage3_segmented
mkdir -p data/manifests
mkdir -p reports

# Determine optimal worker count based on GPU memory
gpu_memory_gb=$(python3 -c "import torch; print(int(torch.cuda.get_device_properties(0).total_memory / 1024**3))" 2>/dev/null || echo "0")
if [ "$gpu_memory_gb" -ge 40 ]; then
    workers=8
    echo "🚀 Using 8 workers (GPU memory: ${gpu_memory_gb}GB)"
elif [ "$gpu_memory_gb" -ge 24 ]; then
    workers=6
    echo "🚀 Using 6 workers (GPU memory: ${gpu_memory_gb}GB)"
elif [ "$gpu_memory_gb" -ge 16 ]; then
    workers=4
    echo "🚀 Using 4 workers (GPU memory: ${gpu_memory_gb}GB)"
else
    workers=2
    echo "🚀 Using 2 workers (GPU memory: ${gpu_memory_gb}GB)"
fi

# Start GPU monitoring in background
echo "📊 Starting GPU monitoring..."
tmux new-window -t stage3_segmentation -n "gpu_monitor"
tmux send-keys -t stage3_segmentation:gpu_monitor "watch -n 2 nvidia-smi" Enter

# Switch back to main window
tmux select-window -t stage3_segmentation:0

echo ""
echo "🎯 Starting Stage-3 Segmentation with GPU acceleration..."
echo "📊 Monitor GPU usage in the 'gpu_monitor' window (Ctrl+B, then 1)"
echo ""

# Run the segmentation
python3 scripts/segment_yolo_sam.py \
  --input_dir data/stage1M_motion_refined_full \
  --output_dir data/stage3_segmented \
  --gallery reports/stage3_gallery.html \
  --summary reports/stage3_summary.csv \
  --manifest data/manifests/stage3_manifest.csv \
  --yolo_model yolov8n.pt \
  --sam_model vit_b \
  --size 96 --upsample 2.0 --motion_percentile 95 \
  --min_mask_coverage 0.015 --max_mask_coverage 0.25 \
  --min_frames 12 --stride 2 \
  --workers $workers

echo ""
echo "🎉 Stage-3 Segmentation completed!"
echo "📊 Check results in:"
echo "  - Segmented videos: data/stage3_segmented/"
echo "  - Gallery: reports/stage3_gallery.html"
echo "  - Summary: reports/stage3_summary.csv"
echo "  - Manifest: data/manifests/stage3_manifest.csv"
