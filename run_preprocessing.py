#!/usr/bin/env python3
"""
Runner script for video preprocessing
"""

import subprocess
import sys
import os

def main():
    print("🎬 Starting Video Preprocessing for Classifier Training...")
    
    # Check if required directories exist
    input_dir = "data/label_screened_v1/keep"
    audit_scores = "reports/label_audit_v1/label_audit_scores.csv"
    
    if not os.path.exists(input_dir):
        print(f"❌ Input directory not found: {input_dir}")
        print("💡 Please run the label audit system first to generate screened videos")
        return 1
    
    if not os.path.exists(audit_scores):
        print(f"⚠️  Audit scores not found: {audit_scores}")
        print("💡 Will proceed without audit scores (using first available video per phrase)")
    
    # Run preprocessing
    cmd = [
        sys.executable, "scripts/preprocess_for_classifier.py",
        "--input_dir", input_dir,
        "--audit_scores", audit_scores,
        "--output_dir", "data/classifier_ready",
        "--target_size", "96",
        "--normalize",
        "--enhance_contrast"
    ]
    
    print(f"🚀 Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ Preprocessing completed successfully!")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"❌ Preprocessing failed with return code {e.returncode}")
        return e.returncode
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
