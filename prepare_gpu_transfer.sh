#!/bin/bash
# Prepare files for GPU server transfer
# Run this script locally before transferring to GPU server

echo "📦 Preparing files for GPU server transfer..."

# Create transfer directory
mkdir -p gpu_transfer
cd gpu_transfer

echo "📁 Creating directory structure..."
mkdir -p scripts
mkdir -p checkpoints
mkdir -p data

# Copy essential scripts
echo "📄 Copying scripts..."
cp ../scripts/segment_yolo_sam.py scripts/
cp ../deploy_gpu_segmentation.sh .
cp ../requirements_gpu.txt .

# Copy model files
echo "🤖 Copying model files..."
if [ -f "../yolov8n.pt" ]; then
    cp ../yolov8n.pt .
    echo "✅ YOLO model copied"
else
    echo "⚠️  yolov8n.pt not found - will download on GPU server"
fi

if [ -f "../checkpoints/sam_vit_b_01ec64.pth" ]; then
    cp ../checkpoints/sam_vit_b_01ec64.pth checkpoints/
    echo "✅ SAM checkpoint copied"
else
    echo "⚠️  SAM checkpoint not found - will download on GPU server"
fi

# Create data archive (this will be large!)
echo "📹 Creating data archive..."
if [ -d "../data/stage1M_motion_refined_full" ]; then
    echo "🗜️  Compressing video data (this may take a while)..."
    tar -czf data_stage1M_motion_refined_full.tar.gz -C ../data stage1M_motion_refined_full
    echo "✅ Data archive created: data_stage1M_motion_refined_full.tar.gz"
    
    # Get archive size
    archive_size=$(du -h data_stage1M_motion_refined_full.tar.gz | cut -f1)
    echo "📊 Archive size: $archive_size"
else
    echo "❌ Input data directory not found: ../data/stage1M_motion_refined_full"
    exit 1
fi

# Create transfer instructions
cat > TRANSFER_INSTRUCTIONS.md << 'EOF'
# GPU Server Transfer Instructions

## Files to transfer:
1. `deploy_gpu_segmentation.sh` - Main deployment script
2. `requirements_gpu.txt` - Python dependencies
3. `scripts/segment_yolo_sam.py` - Segmentation script
4. `yolov8n.pt` - YOLO model (if available)
5. `checkpoints/sam_vit_b_01ec64.pth` - SAM checkpoint (if available)
6. `data_stage1M_motion_refined_full.tar.gz` - Input video data

## Transfer methods:

### Option 1: SCP (if you have SSH access)
```bash
scp -r gpu_transfer/* user@gpu-server:/path/to/project/
```

### Option 2: Cloud storage (Google Drive, AWS S3, etc.)
1. Upload the gpu_transfer folder to cloud storage
2. Download on GPU server
3. Extract data archive: `tar -xzf data_stage1M_motion_refined_full.tar.gz`

### Option 3: Direct upload to GPU pod/container
Follow your GPU provider's file upload instructions.

## Setup on GPU server:
1. Extract data archive: `tar -xzf data_stage1M_motion_refined_full.tar.gz`
2. Run deployment script: `bash deploy_gpu_segmentation.sh`
3. Monitor progress: `tmux attach -t stage3_segmentation`

## Expected performance:
- A40 GPU: ~4-8 hours for 2,141 videos
- Processing speed: ~5-10 videos/minute (vs 0.5-1 video/minute on CPU)
- Success rate: Target ≥95% with proper GPU acceleration
EOF

echo ""
echo "✅ GPU transfer package prepared in 'gpu_transfer/' directory"
echo ""
echo "📋 Next steps:"
echo "1. Transfer the 'gpu_transfer/' directory to your GPU server"
echo "2. Extract the data archive: tar -xzf data_stage1M_motion_refined_full.tar.gz"
echo "3. Run: bash deploy_gpu_segmentation.sh"
echo "4. Monitor: tmux attach -t stage3_segmentation"
echo ""
echo "📊 Files prepared:"
ls -lah
echo ""
echo "📖 See TRANSFER_INSTRUCTIONS.md for detailed transfer options"
