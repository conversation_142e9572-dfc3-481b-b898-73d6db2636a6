#!/usr/bin/env python3
"""
Monitor preprocessing progress
"""

import os
import time
import glob
from datetime import datetime, timedelta

def count_processed_clips():
    """Count how many clips have been processed"""
    clips_dir = "data_processed/clips"
    if not os.path.exists(clips_dir):
        return 0
    
    clip_files = glob.glob(os.path.join(clips_dir, "*.npy"))
    return len(clip_files)

def get_target_counts():
    """Get target counts from manifests"""
    targets = {}
    
    for split in ['train', 'val', 'test']:
        manifest_path = f"manifests/{split}.csv"
        if os.path.exists(manifest_path):
            with open(manifest_path, 'r') as f:
                # Count lines minus header
                targets[split] = len(f.readlines()) - 1
        else:
            targets[split] = 0
    
    return targets

def estimate_completion(current, total, elapsed_time):
    """Estimate completion time"""
    if current == 0:
        return "Unknown"
    
    rate = current / elapsed_time  # clips per second
    remaining = total - current
    remaining_time = remaining / rate
    
    completion_time = datetime.now() + timedelta(seconds=remaining_time)
    
    return f"{remaining_time/60:.1f} min (ETA: {completion_time.strftime('%H:%M:%S')})"

def main():
    print("🔍 Preprocessing Progress Monitor")
    print("=" * 50)
    
    targets = get_target_counts()
    total_target = sum(targets.values())
    
    print(f"Target clips to process:")
    for split, count in targets.items():
        print(f"  {split:5}: {count:4d}")
    print(f"  Total: {total_target:4d}")
    print()
    
    start_time = time.time()
    last_count = 0
    
    try:
        while True:
            current_count = count_processed_clips()
            elapsed = time.time() - start_time
            
            # Calculate rate
            if elapsed > 0:
                overall_rate = current_count / elapsed
                recent_rate = (current_count - last_count) / 10 if elapsed > 10 else 0
            else:
                overall_rate = recent_rate = 0
            
            # Progress percentage
            progress = (current_count / total_target * 100) if total_target > 0 else 0
            
            # Estimate completion
            eta = estimate_completion(current_count, total_target, elapsed)
            
            # Display
            print(f"\r📊 Progress: {current_count:4d}/{total_target} ({progress:5.1f}%) | "
                  f"Rate: {overall_rate:.2f}/s | ETA: {eta}", end="", flush=True)
            
            if current_count >= total_target:
                print("\n🎉 All clips processed!")
                break
            
            last_count = current_count
            time.sleep(10)  # Update every 10 seconds
            
    except KeyboardInterrupt:
        print(f"\n\n📊 Final Status:")
        print(f"Processed: {current_count}/{total_target} clips")
        print(f"Elapsed time: {elapsed/60:.1f} minutes")

if __name__ == "__main__":
    main()
