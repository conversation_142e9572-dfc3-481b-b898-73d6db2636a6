#!/usr/bin/env python3
"""
Simplified landmark-driven preprocessor for trial run.
Works with local files and implements geometric landmark-driven approach.
"""

import os
import sys
import cv2
import numpy as np
import pandas as pd
from pathlib import Path
from tqdm import tqdm
import logging

# Add src to path
sys.path.append('src')

# Try to import YOLO
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("⚠️ YOLO not available - using fallback approach")


class SimpleLandmarkPreprocessor:
    """Simplified preprocessor for landmark-driven lip cropping trial"""
    
    def __init__(self, processed_data_dir='data_processed_trial', img_size=96, yolo_conf=0.5):
        self.processed_data_dir = processed_data_dir
        self.img_size = img_size
        self.yolo_conf = yolo_conf
        
        # Create output directories
        os.makedirs(processed_data_dir, exist_ok=True)
        os.makedirs(os.path.join(processed_data_dir, 'clips'), exist_ok=True)
        os.makedirs(os.path.join(processed_data_dir, 'quality_logs'), exist_ok=True)
        
        # Initialize face detection (try YOLO first, then OpenCV Haar cascades)
        self.yolo = None
        self.face_cascade = None

        if YOLO_AVAILABLE:
            try:
                yolo_path = 'models/yolov8n.pt'
                if os.path.exists(yolo_path):
                    self.yolo = YOLO(yolo_path)
                    print("✅ YOLO model loaded successfully")
                else:
                    print("⚠️ YOLO model not found, trying OpenCV face detection")
            except Exception as e:
                print(f"⚠️ YOLO loading failed: {e}")

        # Fallback to OpenCV Haar cascades
        if self.yolo is None:
            try:
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
                if not self.face_cascade.empty():
                    print("✅ OpenCV Haar cascade face detection loaded")
                else:
                    print("⚠️ OpenCV face detection failed to load")
                    self.face_cascade = None
            except Exception as e:
                print(f"⚠️ OpenCV face detection loading failed: {e}")
                self.face_cascade = None

        # Quality tracking
        self.quality_records = []
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'geometric_landmark_driven': 0,
            'fallback_approaches': 0
        }
    
    def extract_landmark_driven_crop(self, frame):
        """Extract lip crop using geometric landmark-driven approach"""
        h, w = frame.shape[:2]
        
        # Try YOLO face detection first
        face_bbox = None
        face_confidence = 0.0

        if self.yolo is not None:
            try:
                results = self.yolo(frame, conf=self.yolo_conf, classes=[0], verbose=False)
                if results and results[0].boxes:
                    boxes = results[0].boxes.xyxy.cpu().numpy()
                    confidences = results[0].boxes.conf.cpu().numpy()

                    if len(boxes) > 0:
                        # Get the most confident face detection
                        best_idx = np.argmax(confidences)
                        box = boxes[best_idx]
                        face_confidence = confidences[best_idx]
                        x1, y1, x2, y2 = map(int, box)
                        face_bbox = (x1, y1, x2, y2)
            except Exception as e:
                pass

        # Fallback to OpenCV Haar cascade if YOLO failed
        if face_bbox is None and self.face_cascade is not None:
            try:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)

                if len(faces) > 0:
                    # Use the largest face
                    areas = [w * h for (x, y, w, h) in faces]
                    largest_idx = np.argmax(areas)
                    fx, fy, fw, fh = faces[largest_idx]
                    face_bbox = (fx, fy, fx + fw, fy + fh)
                    face_confidence = 0.8  # Assume good confidence for OpenCV detection
            except Exception as e:
                pass
        
        if face_bbox is not None and face_confidence > self.yolo_conf:
            # Geometric lip region estimation within detected face
            fx1, fy1, fx2, fy2 = face_bbox
            face_w = fx2 - fx1
            face_h = fy2 - fy1
            
            # Lips are typically 70-85% down from top of face, centered horizontally
            lip_center_y = fy1 + int(face_h * 0.77)
            lip_height = max(int(face_h * 0.15), 24)
            lip_center_x = fx1 + face_w // 2
            lip_width = max(int(face_w * 0.4), 32)
            
            # Create tight bounding box around estimated lip region
            lip_x1 = max(fx1, lip_center_x - lip_width // 2)
            lip_x2 = min(fx2, lip_center_x + lip_width // 2)
            lip_y1 = max(fy1, lip_center_y - lip_height // 2)
            lip_y2 = min(fy2, lip_center_y + lip_height // 2)
            
            # Add 20% padding
            lip_w = lip_x2 - lip_x1
            lip_h = lip_y2 - lip_y1
            pad_x = int(lip_w * 0.2)
            pad_y = int(lip_h * 0.2)
            
            x1_pad = max(0, lip_x1 - pad_x)
            x2_pad = min(w, lip_x2 + pad_x)
            y1_pad = max(0, lip_y1 - pad_y)
            y2_pad = min(h, lip_y2 + pad_y)
            
            # Extract and resize crop
            lip_crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]
            if lip_crop.size > 0:
                resized_crop = cv2.resize(lip_crop, (self.img_size, self.img_size))
                return resized_crop, True, face_confidence, "geometric_landmark_driven"
        
        # Fallback: center crop
        crop_size = min(h, w)
        start_h = (h - crop_size) // 2
        start_w = (w - crop_size) // 2
        center_crop = frame[start_h:start_h+crop_size, start_w:start_w+crop_size]
        resized_crop = cv2.resize(center_crop, (self.img_size, self.img_size))
        
        return resized_crop, False, 0.0, "center_crop_fallback"
    
    def process_single_video(self, video_path, output_name):
        """Process a single video file"""
        try:
            # Load video
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return {'success': False, 'error': 'video_load_failed'}
            
            # Extract frames
            frames = []
            frame_count = 0
            target_frames = 16
            
            while len(frames) < target_frames:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
                frame_count += 1
            
            cap.release()
            
            if len(frames) == 0:
                return {'success': False, 'error': 'no_frames_extracted'}
            
            # Process each frame with landmark-driven cropping
            processed_frames = []
            valid_frames = 0
            total_confidence = 0.0
            methods_used = []
            
            for frame in frames:
                crop, is_valid, confidence, method = self.extract_landmark_driven_crop(frame)
                processed_frames.append(crop)
                methods_used.append(method)
                
                if is_valid:
                    valid_frames += 1
                    total_confidence += confidence
            
            # Calculate quality metrics
            total_frames = len(processed_frames)
            landmark_coverage_pct = (valid_frames / total_frames * 100) if total_frames > 0 else 0
            avg_confidence = (total_confidence / valid_frames) if valid_frames > 0 else 0
            
            # Apply 90% threshold for high quality
            is_high_quality = landmark_coverage_pct >= 90.0
            
            # Save processed clip
            output_path = os.path.join(self.processed_data_dir, 'clips', f'{output_name}.npy')
            clip_array = np.array(processed_frames)
            np.save(output_path, clip_array)
            
            # Update stats
            if 'geometric_landmark_driven' in methods_used:
                self.stats['geometric_landmark_driven'] += 1
            else:
                self.stats['fallback_approaches'] += 1
            
            return {
                'success': True,
                'output_path': output_path,
                'landmark_coverage_pct': landmark_coverage_pct,
                'avg_confidence': avg_confidence,
                'valid_frames': valid_frames,
                'total_frames': total_frames,
                'is_high_quality': is_high_quality,
                'methods_used': list(set(methods_used))
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def process_from_manifest(self, manifest_path):
        """Process videos from manifest file"""
        print(f"📋 Processing videos from manifest: {manifest_path}")
        
        # Load manifest
        df = pd.read_csv(manifest_path)
        print(f"📊 Found {len(df)} videos to process")
        
        # Process each video
        results = []
        
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing videos"):
            s3_key = row['s3_key']
            phrase = row['phrase']
            speaker_id = row['speaker_id']
            
            # Generate output name
            output_name = f"{phrase}_{speaker_id}_{idx:06d}"
            
            # Process video (s3_key is actually local path)
            result = self.process_single_video(s3_key, output_name)
            
            # Add metadata
            result.update({
                'manifest_idx': idx,
                's3_key': s3_key,
                'phrase': phrase,
                'speaker_id': speaker_id,
                'output_name': output_name
            })
            
            # Record quality data
            if result['success']:
                quality_record = {
                    'clip_id': output_name,
                    'phrase': phrase,
                    'speaker_id': speaker_id,
                    'valid_crop': result.get('is_high_quality', False),
                    'landmark_coverage_pct': result.get('landmark_coverage_pct', 0),
                    'avg_confidence': result.get('avg_confidence', 0),
                    'valid_frames': result.get('valid_frames', 0),
                    'total_frames': result.get('total_frames', 0),
                    'success': True,
                    'error': '',
                    'methods_used': ','.join(result.get('methods_used', []))
                }
                self.stats['successful'] += 1
            else:
                quality_record = {
                    'clip_id': output_name,
                    'phrase': phrase,
                    'speaker_id': speaker_id,
                    'valid_crop': False,
                    'landmark_coverage_pct': 0,
                    'avg_confidence': 0,
                    'valid_frames': 0,
                    'total_frames': 0,
                    'success': False,
                    'error': result.get('error', 'unknown'),
                    'methods_used': ''
                }
                self.stats['failed'] += 1
            
            self.quality_records.append(quality_record)
            results.append(result)
            self.stats['total_processed'] += 1
        
        # Save quality CSV
        self.save_quality_csv()
        
        # Print statistics
        self.print_statistics()
        
        return results
    
    def save_quality_csv(self):
        """Save quality records to CSV"""
        if not self.quality_records:
            return
        
        output_path = os.path.join(self.processed_data_dir, 'quality_logs', 'quality_scores.csv')
        df = pd.DataFrame(self.quality_records)
        df.to_csv(output_path, index=False)
        
        print(f"💾 Quality scores saved to: {output_path}")
    
    def print_statistics(self):
        """Print processing statistics"""
        total = self.stats['total_processed']
        successful = self.stats['successful']
        failed = self.stats['failed']
        geometric = self.stats['geometric_landmark_driven']
        fallback = self.stats['fallback_approaches']
        
        print(f"\n📊 Processing Statistics:")
        print("=" * 50)
        print(f"Total processed:           {total}")
        print(f"Successful:                {successful} ({successful/total*100:.1f}%)")
        print(f"Failed:                    {failed} ({failed/total*100:.1f}%)")
        print(f"Geometric landmark-driven: {geometric} ({geometric/total*100:.1f}%)")
        print(f"Fallback approaches:       {fallback} ({fallback/total*100:.1f}%)")
        print("=" * 50)
        
        # Quality summary
        if self.quality_records:
            df = pd.DataFrame(self.quality_records)
            high_quality = len(df[df['valid_crop'] == True])
            avg_coverage = df['landmark_coverage_pct'].mean()
            
            print(f"\n📊 Quality Summary:")
            print("=" * 50)
            print(f"High quality clips (≥90%): {high_quality} ({high_quality/len(df)*100:.1f}%)")
            print(f"Average landmark coverage:  {avg_coverage:.1f}%")
            print("=" * 50)
