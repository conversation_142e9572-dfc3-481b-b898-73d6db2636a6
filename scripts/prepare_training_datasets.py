#!/usr/bin/env python3
"""
Comprehensive Dataset Preparation Script

Processes video validation results to build two separate datasets with demographic-based 
splits and training manifests, then applies numpy conversion pipeline for classifier training.

Usage:
    python scripts/prepare_training_datasets.py \
      --validation_csv "reports/validation_flagged_videos.csv" \
      --clean_video_dir "data/stage1M_motion_refined_full" \
      --flagged_video_dir "data/validation_flagged/stage1M_motion_refined_full" \
      --output_base "datasets" \
      --target_size 96 \
      --target_frames 80 \
      --workers 4
"""

import argparse
import pandas as pd
import numpy as np
import shutil
import json
import hashlib
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm

# Import existing preprocessing functions
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import cv2

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Phrase to class mapping
PHRASE_LABELS = {
    'pillow': 0,
    'phone': 1, 
    'doctor': 2,
    'glasses': 3,
    'help': 4,
    'i_need_to_move': 5,
    'my_mouth_is_dry': 6
}

def parse_video_path(video_path: str) -> Dict[str, str]:
    """
    Parse video path to extract demographics.
    Expected format: phrase/age_group/gender/ethnicity/phrase/filename.mp4
    """
    try:
        path_parts = Path(video_path).parts
        
        # Find the phrase directory (should appear twice)
        phrase = None
        for part in path_parts:
            if part in PHRASE_LABELS:
                phrase = part
                break
        
        if not phrase:
            raise ValueError(f"No valid phrase found in path: {video_path}")
        
        # Extract demographics from path structure
        phrase_idx = None
        for i, part in enumerate(path_parts):
            if part == phrase:
                phrase_idx = i
                break
        
        if phrase_idx is None or phrase_idx + 3 >= len(path_parts):
            raise ValueError(f"Invalid path structure: {video_path}")
        
        age_group = path_parts[phrase_idx + 1]
        gender = path_parts[phrase_idx + 2] 
        ethnicity = path_parts[phrase_idx + 3]
        
        return {
            'phrase': phrase,
            'age_group': age_group,
            'gender': gender,
            'ethnicity': ethnicity,
            'filename': Path(video_path).name
        }
    except Exception as e:
        logger.error(f"Error parsing video path {video_path}: {e}")
        return {
            'phrase': 'unknown',
            'age_group': 'unknown',
            'gender': 'unknown', 
            'ethnicity': 'unknown',
            'filename': Path(video_path).name
        }

def create_stratified_splits(df: pd.DataFrame, test_size: float = 0.15, val_size: float = 0.15,
                           random_state: int = 42) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """
    Create stratified train/val/test splits ensuring demographic balance.
    Uses phrase-only stratification if full demographic stratification fails.
    """
    df = df.copy()

    # Try full demographic stratification first
    df['strat_key'] = (df['phrase'] + '_' + df['age_group'] + '_' +
                      df['gender'] + '_' + df['ethnicity'])

    # Check if stratification is possible
    strat_counts = df['strat_key'].value_counts()
    min_samples = strat_counts.min()

    if min_samples < 2:
        logger.warning(f"⚠️  Full demographic stratification not possible (min samples: {min_samples})")
        logger.warning("🔄 Falling back to phrase-only stratification")
        df['strat_key'] = df['phrase']
        strat_counts = df['strat_key'].value_counts()
        min_samples = strat_counts.min()

        if min_samples < 2:
            logger.warning("⚠️  Even phrase-only stratification not possible, using random split")
            # Random split without stratification
            train_val_df, test_df = train_test_split(
                df, test_size=test_size, random_state=random_state
            )
            val_size_adjusted = val_size / (1 - test_size)
            train_df, val_df = train_test_split(
                train_val_df, test_size=val_size_adjusted, random_state=random_state
            )
        else:
            # Phrase-only stratification
            train_val_df, test_df = train_test_split(
                df, test_size=test_size, stratify=df['strat_key'],
                random_state=random_state
            )
            val_size_adjusted = val_size / (1 - test_size)
            train_df, val_df = train_test_split(
                train_val_df, test_size=val_size_adjusted, stratify=train_val_df['strat_key'],
                random_state=random_state
            )
    else:
        # Full demographic stratification
        logger.info(f"✅ Using full demographic stratification (min samples: {min_samples})")
        train_val_df, test_df = train_test_split(
            df, test_size=test_size, stratify=df['strat_key'],
            random_state=random_state
        )
        val_size_adjusted = val_size / (1 - test_size)
        train_df, val_df = train_test_split(
            train_val_df, test_size=val_size_adjusted, stratify=train_val_df['strat_key'],
            random_state=random_state
        )

    # Add split labels
    train_df = train_df.copy()
    val_df = val_df.copy()
    test_df = test_df.copy()

    train_df['split'] = 'train'
    val_df['split'] = 'val'
    test_df['split'] = 'test'

    # Drop temporary stratification key
    for split_df in [train_df, val_df, test_df]:
        if 'strat_key' in split_df.columns:
            split_df.drop('strat_key', axis=1, inplace=True)

    return train_df, val_df, test_df

def verify_male_18to39_presence(train_df: pd.DataFrame, dataset_name: str) -> bool:
    """Verify that male 18-39 demographic is present in training set."""
    male_18to39 = train_df[(train_df['gender'] == 'male') & (train_df['age_group'] == '18to39')]
    
    if len(male_18to39) == 0:
        logger.error(f"❌ CRITICAL: No male 18-39 samples in {dataset_name} training set!")
        return False
    
    logger.info(f"✅ {dataset_name} training set contains {len(male_18to39)} male 18-39 samples")
    return True

def copy_videos_to_dataset(df: pd.DataFrame, source_base: str, dest_base: str,
                          dataset_type: str) -> Dict[str, int]:
    """Copy videos to organized dataset structure."""
    dest_path = Path(dest_base)
    stats = {'copied': 0, 'failed': 0, 'skipped': 0}

    # Filter out videos that don't exist
    existing_videos = []
    for _, row in df.iterrows():
        source_path = Path(row['video_path'])
        if source_path.exists():
            existing_videos.append(row)
        else:
            stats['skipped'] += 1

    logger.info(f"Found {len(existing_videos)} existing videos out of {len(df)} total for {dataset_type} dataset")

    for row in tqdm(existing_videos, desc=f"Copying {dataset_type} videos"):
        source_path = Path(row['video_path'])

        # Create relative path for destination
        rel_path = source_path.relative_to(source_path.parts[0])  # Remove 'data' prefix
        dest_video_path = dest_path / row['split'] / rel_path

        # Create destination directory
        dest_video_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            shutil.copy2(source_path, dest_video_path)
            stats['copied'] += 1
        except Exception as e:
            logger.error(f"Failed to copy {source_path}: {e}")
            stats['failed'] += 1

    return stats

def read_video_frames(video_path: Path, target_size: int) -> Optional[np.ndarray]:
    """Read video frames and resize to target size."""
    try:
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            logger.error(f"Cannot open video: {video_path}")
            return None
        
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Resize to target size
            frame_resized = cv2.resize(frame_rgb, (target_size, target_size), 
                                     interpolation=cv2.INTER_AREA)
            
            frames.append(frame_resized)
        
        cap.release()
        
        if not frames:
            logger.error(f"No frames found in video: {video_path}")
            return None
        
        return np.array(frames, dtype=np.uint8)
        
    except Exception as e:
        logger.error(f"Error reading video {video_path}: {e}")
        return None

def standardize_frame_count(video_data: np.ndarray, target_frames: int) -> np.ndarray:
    """Standardize video to target frame count."""
    current_frames = video_data.shape[0]
    
    if current_frames == target_frames:
        return video_data
    elif current_frames > target_frames:
        # Truncate: take evenly spaced frames
        indices = np.linspace(0, current_frames - 1, target_frames, dtype=int)
        return video_data[indices]
    else:
        # Pad: repeat last frame
        padding_needed = target_frames - current_frames
        last_frame = video_data[-1:].repeat(padding_needed, axis=0)
        return np.concatenate([video_data, last_frame], axis=0)

def normalize_video(video_data: np.ndarray) -> np.ndarray:
    """Normalize pixel values to [0, 1] range."""
    return video_data.astype(np.float32) / 255.0

def process_single_video(args_tuple) -> Dict:
    """Process a single video through the numpy conversion pipeline."""
    video_path, output_path, target_size, target_frames = args_tuple
    
    try:
        # Read video frames
        frames = read_video_frames(Path(video_path), target_size)
        if frames is None:
            return {'status': 'failed', 'error': 'Failed to read frames'}
        
        # Standardize frame count
        frames = standardize_frame_count(frames, target_frames)
        
        # Normalize pixel values
        frames = normalize_video(frames)
        
        # Create output directory
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Save as numpy array
        np.save(output_path, frames)
        
        return {
            'status': 'success',
            'shape': frames.shape,
            'dtype': str(frames.dtype),
            'min_val': float(frames.min()),
            'max_val': float(frames.max()),
            'mean_val': float(frames.mean()),
            'std_val': float(frames.std())
        }
        
    except Exception as e:
        return {'status': 'failed', 'error': str(e)}

def process_videos_to_numpy(df: pd.DataFrame, dataset_base: str, target_size: int, 
                           target_frames: int, workers: int = 4) -> pd.DataFrame:
    """Process all videos in dataset through numpy conversion pipeline."""
    
    # Prepare processing tasks
    tasks = []
    processed_df = df.copy()
    processed_df['numpy_path'] = ''
    processed_df['processing_status'] = ''
    
    for idx, row in df.iterrows():
        # Construct paths
        video_path = Path(dataset_base) / row['split'] / Path(row['video_path']).relative_to(Path(row['video_path']).parts[0])
        numpy_filename = Path(row['video_path']).stem + '.npy'
        numpy_path = Path(dataset_base) / row['split'] / Path(row['video_path']).relative_to(Path(row['video_path']).parts[0]).parent / numpy_filename
        
        tasks.append((str(video_path), str(numpy_path), target_size, target_frames))
        processed_df.at[idx, 'numpy_path'] = str(numpy_path.relative_to(dataset_base))
    
    # Process videos in parallel
    results = []
    with ProcessPoolExecutor(max_workers=workers) as executor:
        future_to_idx = {executor.submit(process_single_video, task): idx 
                        for idx, task in enumerate(tasks)}
        
        for future in tqdm(as_completed(future_to_idx), total=len(tasks), 
                          desc="Converting videos to numpy"):
            idx = future_to_idx[future]
            try:
                result = future.result()
                results.append((idx, result))
            except Exception as e:
                results.append((idx, {'status': 'failed', 'error': str(e)}))
    
    # Add new columns to dataframe
    for col in ['shape', 'dtype', 'min_val', 'max_val', 'mean_val', 'std_val', 'error']:
        if col not in processed_df.columns:
            processed_df[col] = None

    # Update dataframe with results
    for idx, result in results:
        df_idx = df.index[idx]
        processed_df.at[df_idx, 'processing_status'] = result['status']
        if result['status'] == 'success':
            for key in ['shape', 'dtype', 'min_val', 'max_val', 'mean_val', 'std_val']:
                if key in result:
                    processed_df.at[df_idx, key] = str(result[key])
        else:
            processed_df.at[df_idx, 'error'] = result.get('error', 'Unknown error')
    
    return processed_df

def generate_split_statistics(train_df: pd.DataFrame, val_df: pd.DataFrame, test_df: pd.DataFrame,
                             dataset_type: str) -> str:
    """Generate detailed statistics for dataset splits."""
    stats = []
    stats.append(f"\n{dataset_type.upper()} DATASET STATISTICS")
    stats.append("=" * 50)

    # Overall counts
    total = len(train_df) + len(val_df) + len(test_df)
    stats.append(f"Total videos: {total:,}")
    stats.append(f"Train: {len(train_df):,} ({len(train_df)/total*100:.1f}%)")
    stats.append(f"Val: {len(val_df):,} ({len(val_df)/total*100:.1f}%)")
    stats.append(f"Test: {len(test_df):,} ({len(test_df)/total*100:.1f}%)")

    # Phrase distribution
    stats.append(f"\nPHRASE DISTRIBUTION:")
    stats.append("-" * 20)
    for split_name, split_df in [("Train", train_df), ("Val", val_df), ("Test", test_df)]:
        phrase_counts = split_df['phrase'].value_counts().sort_index()
        stats.append(f"{split_name}:")
        for phrase, count in phrase_counts.items():
            stats.append(f"  {phrase}: {count}")

    # Demographic distribution
    stats.append(f"\nDEMOGRAPHIC DISTRIBUTION:")
    stats.append("-" * 25)

    # Age groups
    stats.append("Age Groups:")
    for split_name, split_df in [("Train", train_df), ("Val", val_df), ("Test", test_df)]:
        age_counts = split_df['age_group'].value_counts().sort_index()
        stats.append(f"  {split_name}: {dict(age_counts)}")

    # Gender
    stats.append("Gender:")
    for split_name, split_df in [("Train", train_df), ("Val", val_df), ("Test", test_df)]:
        gender_counts = split_df['gender'].value_counts().sort_index()
        stats.append(f"  {split_name}: {dict(gender_counts)}")

    # Ethnicity
    stats.append("Ethnicity:")
    for split_name, split_df in [("Train", train_df), ("Val", val_df), ("Test", test_df)]:
        ethnicity_counts = split_df['ethnicity'].value_counts().sort_index()
        stats.append(f"  {split_name}: {dict(ethnicity_counts)}")

    # Critical demographic check
    stats.append(f"\nCRITICAL DEMOGRAPHIC VERIFICATION:")
    stats.append("-" * 35)
    male_18to39_train = train_df[(train_df['gender'] == 'male') & (train_df['age_group'] == '18to39')]
    stats.append(f"Male 18-39 in training set: {len(male_18to39_train)} samples")
    if len(male_18to39_train) > 0:
        stats.append("✅ REQUIREMENT MET: Male 18-39 demographic present in training")
    else:
        stats.append("❌ REQUIREMENT FAILED: Male 18-39 demographic missing from training")

    return "\n".join(stats)

def save_manifests(train_df: pd.DataFrame, val_df: pd.DataFrame, test_df: pd.DataFrame,
                  dataset_type: str, manifests_dir: Path) -> None:
    """Save individual and combined manifests."""

    # Add dataset type column
    for df in [train_df, val_df, test_df]:
        df['dataset_type'] = dataset_type

    # Save individual split manifests
    train_df.to_csv(manifests_dir / f"manifest_{dataset_type}_train.csv", index=False)
    val_df.to_csv(manifests_dir / f"manifest_{dataset_type}_val.csv", index=False)
    test_df.to_csv(manifests_dir / f"manifest_{dataset_type}_test.csv", index=False)

    logger.info(f"✅ Saved {dataset_type} manifests:")
    logger.info(f"  - Train: {len(train_df):,} samples")
    logger.info(f"  - Val: {len(val_df):,} samples")
    logger.info(f"  - Test: {len(test_df):,} samples")

def create_dataset_metadata(dataset_path: Path, dataset_type: str, stats: Dict) -> None:
    """Create dataset metadata file with version and creation info."""
    metadata = {
        'dataset_name': f"dataset_{dataset_type}_{datetime.now().strftime('%Y.%m.%d')}",
        'dataset_type': dataset_type,
        'creation_date': datetime.now().isoformat(),
        'version': '1.0',
        'description': f"ICU Lipreading {dataset_type} dataset with demographic-based splits",
        'total_videos': stats['copied'],
        'failed_copies': stats['failed'],
        'skipped_videos': stats['skipped'],
        'phrases': list(PHRASE_LABELS.keys()),
        'num_classes': len(PHRASE_LABELS),
        'target_resolution': '96x96',
        'splits': ['train', 'val', 'test'],
        'split_ratios': {'train': 0.7, 'val': 0.15, 'test': 0.15}
    }

    with open(dataset_path / 'dataset_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)

def calculate_checksums(dataset_path: Path) -> Dict[str, str]:
    """Calculate checksums for data integrity verification."""
    checksums = {}

    for split in ['train', 'val', 'test']:
        split_path = dataset_path / split
        if split_path.exists():
            # Count files
            video_files = list(split_path.rglob('*.mp4'))
            numpy_files = list(split_path.rglob('*.npy'))

            checksums[f"{split}_videos"] = len(video_files)
            checksums[f"{split}_numpy"] = len(numpy_files)

    return checksums

def main():
    parser = argparse.ArgumentParser(description='Prepare training datasets from validation results')
    parser.add_argument('--validation_csv', required=True,
                       help='Path to validation results CSV')
    parser.add_argument('--clean_video_dir', default='data/stage1M_motion_refined_full',
                       help='Directory containing clean videos')
    parser.add_argument('--flagged_video_dir', default='data/validation_flagged/stage1M_motion_refined_full',
                       help='Directory containing flagged videos')
    parser.add_argument('--output_base', default='datasets',
                       help='Base output directory for datasets')
    parser.add_argument('--target_size', type=int, default=96,
                       help='Target video resolution (square)')
    parser.add_argument('--target_frames', type=int, default=80,
                       help='Target frame count for videos')
    parser.add_argument('--workers', type=int, default=4,
                       help='Number of worker processes for video conversion')
    parser.add_argument('--test_size', type=float, default=0.15,
                       help='Test set proportion')
    parser.add_argument('--val_size', type=float, default=0.15,
                       help='Validation set proportion')
    parser.add_argument('--random_state', type=int, default=42,
                       help='Random state for reproducible splits')

    args = parser.parse_args()

    logger.info("🚀 Starting comprehensive dataset preparation...")

    # Create output directories
    date_stamp = datetime.now().strftime('%Y.%m.%d')
    output_base = Path(args.output_base)
    clean_dataset_dir = output_base / f"dataset_clean_{date_stamp}"
    flagged_dataset_dir = output_base / f"dataset_flagged_{date_stamp}"
    manifests_dir = output_base / "manifests"

    for dir_path in [clean_dataset_dir, flagged_dataset_dir, manifests_dir]:
        dir_path.mkdir(parents=True, exist_ok=True)

    # Load validation results
    logger.info(f"📊 Loading validation results from {args.validation_csv}")
    validation_df = pd.read_csv(args.validation_csv)

    # Parse demographics from video paths
    logger.info("🔍 Parsing demographics from video paths...")
    demographics = []
    for _, row in tqdm(validation_df.iterrows(), total=len(validation_df), desc="Parsing paths"):
        demo = parse_video_path(row['video_path'])
        demo.update({
            'video_path': row['video_path'],
            'frames': row['frames'],
            'duration_ratio': row['duration_ratio'],
            'motion_share': row['motion_share'],
            'heatmap_max': row['heatmap_max'],
            'issues': row['issues'],
            'recommendation': row['recommendation']
        })
        demographics.append(demo)

    demo_df = pd.DataFrame(demographics)

    # Split into clean and flagged datasets
    clean_df = demo_df[demo_df['recommendation'] == 'keep'].copy()
    flagged_df = demo_df[demo_df['recommendation'].isin(['review', 'remove'])].copy()

    logger.info(f"📈 Dataset sizes:")
    logger.info(f"  Clean dataset: {len(clean_df):,} videos")
    logger.info(f"  Flagged dataset: {len(flagged_df):,} videos")

    # Create stratified splits for both datasets
    logger.info("🎯 Creating stratified demographic splits...")

    # Clean dataset splits
    clean_train, clean_val, clean_test = create_stratified_splits(
        clean_df, args.test_size, args.val_size, args.random_state
    )

    # Flagged dataset splits
    flagged_train, flagged_val, flagged_test = create_stratified_splits(
        flagged_df, args.test_size, args.val_size, args.random_state
    )

    # Verify critical demographic requirements
    logger.info("✅ Verifying demographic requirements...")
    clean_male_ok = verify_male_18to39_presence(clean_train, "clean")
    flagged_male_ok = verify_male_18to39_presence(flagged_train, "flagged")

    if not (clean_male_ok and flagged_male_ok):
        logger.error("❌ Critical demographic requirements not met!")
        return 1

    # Phase 1: Copy videos to organized dataset structure
    logger.info("📁 Phase 1: Organizing videos into dataset structure...")

    # Combine splits for copying
    clean_all = pd.concat([clean_train, clean_val, clean_test], ignore_index=True)
    flagged_all = pd.concat([flagged_train, flagged_val, flagged_test], ignore_index=True)

    # Copy clean videos
    logger.info("📋 Copying clean dataset videos...")
    clean_stats = copy_videos_to_dataset(clean_all, args.clean_video_dir,
                                        clean_dataset_dir, "clean")

    # Copy flagged videos
    logger.info("📋 Copying flagged dataset videos...")
    flagged_stats = copy_videos_to_dataset(flagged_all, args.flagged_video_dir,
                                          flagged_dataset_dir, "flagged")

    # Phase 2: Generate manifests
    logger.info("📝 Phase 2: Generating training manifests...")

    # Save individual manifests
    save_manifests(clean_train, clean_val, clean_test, "clean", manifests_dir)
    save_manifests(flagged_train, flagged_val, flagged_test, "flagged", manifests_dir)

    # Create combined master manifest
    all_splits = pd.concat([
        clean_train, clean_val, clean_test,
        flagged_train, flagged_val, flagged_test
    ], ignore_index=True)
    all_splits.to_csv(manifests_dir / "manifest_combined_all.csv", index=False)

    # Generate statistics
    logger.info("📊 Generating split statistics...")
    clean_stats_text = generate_split_statistics(clean_train, clean_val, clean_test, "clean")
    flagged_stats_text = generate_split_statistics(flagged_train, flagged_val, flagged_test, "flagged")

    with open(manifests_dir / "split_statistics_summary.txt", 'w') as f:
        f.write(clean_stats_text)
        f.write("\n\n")
        f.write(flagged_stats_text)

    # Phase 3: Apply numpy conversion pipeline
    logger.info("🔄 Phase 3: Converting videos to numpy format...")

    # Process clean dataset
    logger.info("Processing clean dataset through numpy pipeline...")
    clean_processed = process_videos_to_numpy(
        clean_all, clean_dataset_dir, args.target_size, args.target_frames, args.workers
    )

    # Process flagged dataset
    logger.info("Processing flagged dataset through numpy pipeline...")
    flagged_processed = process_videos_to_numpy(
        flagged_all, flagged_dataset_dir, args.target_size, args.target_frames, args.workers
    )

    # Update manifests with processing results
    logger.info("📝 Updating manifests with processing results...")

    # Update and save processed manifests
    for split_name, split_df in [("train", clean_train), ("val", clean_val), ("test", clean_test)]:
        # Merge processing results
        split_processed = clean_processed[clean_processed['split'] == split_name]
        updated_manifest = split_df.merge(split_processed[['video_path', 'numpy_path', 'processing_status']],
                                        on='video_path', how='left')
        updated_manifest.to_csv(manifests_dir / f"manifest_clean_{split_name}.csv", index=False)

    for split_name, split_df in [("train", flagged_train), ("val", flagged_val), ("test", flagged_test)]:
        # Merge processing results
        split_processed = flagged_processed[flagged_processed['split'] == split_name]
        updated_manifest = split_df.merge(split_processed[['video_path', 'numpy_path', 'processing_status']],
                                        on='video_path', how='left')
        updated_manifest.to_csv(manifests_dir / f"manifest_flagged_{split_name}.csv", index=False)

    # Create dataset metadata
    logger.info("📋 Creating dataset metadata...")
    create_dataset_metadata(clean_dataset_dir, "clean", clean_stats)
    create_dataset_metadata(flagged_dataset_dir, "flagged", flagged_stats)

    # Calculate checksums for integrity verification
    logger.info("🔐 Calculating checksums for data integrity...")
    clean_checksums = calculate_checksums(clean_dataset_dir)
    flagged_checksums = calculate_checksums(flagged_dataset_dir)

    # Save integrity report
    integrity_report = {
        'creation_date': datetime.now().isoformat(),
        'clean_dataset': {
            'path': str(clean_dataset_dir),
            'checksums': clean_checksums,
            'copy_stats': clean_stats
        },
        'flagged_dataset': {
            'path': str(flagged_dataset_dir),
            'checksums': flagged_checksums,
            'copy_stats': flagged_stats
        },
        'processing_summary': {
            'target_size': args.target_size,
            'target_frames': args.target_frames,
            'clean_success_rate': len(clean_processed[clean_processed['processing_status'] == 'success']) / len(clean_processed),
            'flagged_success_rate': len(flagged_processed[flagged_processed['processing_status'] == 'success']) / len(flagged_processed)
        }
    }

    with open(manifests_dir / "data_integrity_report.json", 'w') as f:
        json.dump(integrity_report, f, indent=2)

    # Final summary
    logger.info("=" * 60)
    logger.info("✅ DATASET PREPARATION COMPLETE")
    logger.info("=" * 60)
    logger.info(f"📁 Clean dataset: {clean_dataset_dir}")
    logger.info(f"   - Videos copied: {clean_stats['copied']:,}")
    logger.info(f"   - Processing success: {len(clean_processed[clean_processed['processing_status'] == 'success']):,}")
    logger.info(f"📁 Flagged dataset: {flagged_dataset_dir}")
    logger.info(f"   - Videos copied: {flagged_stats['copied']:,}")
    logger.info(f"   - Processing success: {len(flagged_processed[flagged_processed['processing_status'] == 'success']):,}")
    logger.info(f"📝 Manifests directory: {manifests_dir}")
    logger.info(f"🎯 Target resolution: {args.target_size}x{args.target_size}")
    logger.info(f"🎬 Target frames: {args.target_frames}")
    logger.info("🚀 Datasets ready for SageMaker migration!")

    return 0

if __name__ == "__main__":
    exit(main())
