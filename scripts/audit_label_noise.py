#!/usr/bin/env python3
"""
Label Quality Audit System

Detects and quarantines likely mislabeled videos using lip-motion dynamics 
and MediaPipe landmarks without requiring speaker IDs.

Usage:
    python -u scripts/audit_label_noise.py \
      --input_dir "data/stage1M_motion_refined_full" \
      --manifest_in "data/manifests/stage3_manifest.csv" \
      --audit_dir "reports/label_audit_v1" \
      --output_dir "data/label_screened_v1" \
      --frames_for_gallery 3 \
      --upsample 2.0 \
      --min_frames 12 \
      --review_top_frac 0.10 \
      --reject_top_frac 0.03 \
      --seed 42

Dependencies:
    opencv-python, numpy, pandas, mediapipe, tqdm, scipy, scikit-learn, jinja2
"""

import argparse
import cv2
import numpy as np
import pandas as pd
import os
import json
import shutil
from pathlib import Path
from tqdm import tqdm
from typing import List, Tuple, Optional, Dict, Any
import warnings
warnings.filterwarnings('ignore')

# Import dependencies
try:
    from scipy.spatial.distance import euclidean
    from scipy.stats import zscore
    from sklearn.linear_model import LogisticRegression
    from sklearn.model_selection import cross_val_predict, StratifiedKFold
    from sklearn.preprocessing import StandardScaler
    from jinja2 import Template
    import base64
    print("✅ Core dependencies imported")
except ImportError as e:
    print(f"❌ Missing core dependencies: {e}")
    print("Install with: pip install scipy scikit-learn jinja2")
    exit(1)

# Try to import MediaPipe (optional for landmark-based features)
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
    print("✅ MediaPipe available - will use landmark-based features")
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    print("⚠️  MediaPipe not available - will use motion-only features")


def find_videos(input_dir: str, extensions: List[str]) -> List[Path]:
    """Find all video files recursively."""
    input_path = Path(input_dir)
    videos = []
    for ext in extensions:
        videos.extend(input_path.rglob(f'*{ext}'))
        videos.extend(input_path.rglob(f'*{ext.upper()}'))
    return sorted(videos)


def extract_phrase_from_path(video_path: Path) -> str:
    """Extract phrase from video path structure."""
    parts = video_path.parts
    phrases = ['doctor', 'glasses', 'help', 'i_need_to_move', 'my_mouth_is_dry', 'phone', 'pillow']
    for part in parts:
        if part in phrases:
            return part
        # Handle underscored phrases
        for phrase in phrases:
            if phrase.replace('_', ' ') in part.replace('_', ' '):
                return phrase
    return "unknown"


def get_mouth_landmarks(landmarks, image_shape):
    """Extract mouth landmark coordinates from MediaPipe face mesh."""
    h, w = image_shape[:2]
    
    # MediaPipe mouth landmark indices (outer + inner lip)
    MOUTH_INDICES = [
        # Outer lip
        61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
        # Inner lip  
        78, 95, 88, 178, 87, 14, 317, 402, 318, 324
    ]
    
    mouth_points = []
    for idx in MOUTH_INDICES:
        if idx < len(landmarks.landmark):
            x = landmarks.landmark[idx].x * w
            y = landmarks.landmark[idx].y * h
            mouth_points.append((x, y))
    
    return np.array(mouth_points) if mouth_points else None


def compute_mouth_features(mouth_points: np.ndarray) -> Dict[str, float]:
    """Compute mouth geometric features from landmarks."""
    if mouth_points is None or len(mouth_points) < 4:
        return {'mouth_height': 0, 'mouth_width': 0, 'mouth_area': 0}
    
    # Compute bounding box
    x_coords = mouth_points[:, 0]
    y_coords = mouth_points[:, 1]
    
    mouth_width = np.max(x_coords) - np.min(x_coords)
    mouth_height = np.max(y_coords) - np.min(y_coords)
    
    # Approximate mouth area using convex hull
    try:
        from scipy.spatial import ConvexHull
        hull = ConvexHull(mouth_points)
        mouth_area = hull.volume  # 2D area
    except:
        # Fallback: rectangle approximation
        mouth_area = mouth_width * mouth_height
    
    return {
        'mouth_height': mouth_height,
        'mouth_width': mouth_width, 
        'mouth_area': mouth_area
    }


def compute_motion_fallback_features(frames: List[np.ndarray]) -> Dict[str, List[float]]:
    """Fallback motion-based features when landmarks fail."""
    features = {'mouth_height': [], 'mouth_width': [], 'mouth_area': []}
    
    if len(frames) < 2:
        return features
    
    # Focus on mouth region: y∈[0.45H,0.85H], x∈[0.30W,0.70W]
    h, w = frames[0].shape[:2]
    y0, y1 = int(0.45 * h), int(0.85 * h)
    x0, x1 = int(0.30 * w), int(0.70 * w)
    
    for i in range(len(frames)):
        frame_gray = cv2.cvtColor(frames[i], cv2.COLOR_BGR2GRAY) if len(frames[i].shape) == 3 else frames[i]
        mouth_region = frame_gray[y0:y1, x0:x1]
        
        # Compute motion-based approximations
        if i > 0:
            prev_region = cv2.cvtColor(frames[i-1], cv2.COLOR_BGR2GRAY)[y0:y1, x0:x1] if len(frames[i-1].shape) == 3 else frames[i-1][y0:y1, x0:x1]
            diff = cv2.absdiff(mouth_region, prev_region)
            motion_pixels = np.sum(diff > 20)  # Threshold for motion
            
            # Approximate features based on motion distribution
            features['mouth_area'].append(motion_pixels)
            features['mouth_height'].append(np.sqrt(motion_pixels) * 0.7)  # Rough approximation
            features['mouth_width'].append(np.sqrt(motion_pixels) * 1.3)   # Wider than height
        else:
            # First frame - use region size as baseline
            features['mouth_area'].append(mouth_region.size * 0.1)
            features['mouth_height'].append(mouth_region.shape[0] * 0.3)
            features['mouth_width'].append(mouth_region.shape[1] * 0.4)
    
    return features


def extract_video_features(video_path: Path, upsample: float = 2.0, min_frames: int = 12) -> Dict[str, Any]:
    """Extract comprehensive features from a single video."""
    try:
        # Initialize MediaPipe if available
        face_mesh = None
        if MEDIAPIPE_AVAILABLE:
            mp_face_mesh = mp.solutions.face_mesh
            face_mesh = mp_face_mesh.FaceMesh(
                static_image_mode=False,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
        
        # Read video
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return {'error': f'Cannot open video: {video_path}'}
        
        fps = cap.get(cv2.CAP_PROP_FPS) or 25.0
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if total_frames < min_frames:
            cap.release()
            return {'error': f'Too few frames: {total_frames} < {min_frames}'}
        
        # Read frames (every 2nd frame for efficiency)
        frames = []
        frame_idx = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            if frame_idx % 2 == 0:  # Process every 2nd frame
                # Upsample if needed
                if upsample > 1.0:
                    h, w = frame.shape[:2]
                    new_h, new_w = int(h * upsample), int(w * upsample)
                    frame = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_CUBIC)
                frames.append(frame)
            
            frame_idx += 1
        
        cap.release()
        
        if len(frames) < min_frames // 2:  # Account for stride
            return {'error': f'Too few processed frames: {len(frames)}'}
        
        # Extract landmark-based features (if MediaPipe available)
        temporal_features = {'mouth_height': [], 'mouth_width': [], 'mouth_area': []}
        landmarks_ok = MEDIAPIPE_AVAILABLE
        landmark_failures = 0

        if MEDIAPIPE_AVAILABLE and face_mesh is not None:
            for frame in frames:
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = face_mesh.process(frame_rgb)

                if results.multi_face_landmarks:
                    landmarks = results.multi_face_landmarks[0]
                    mouth_points = get_mouth_landmarks(landmarks, frame.shape)

                    if mouth_points is not None:
                        features = compute_mouth_features(mouth_points)
                        temporal_features['mouth_height'].append(features['mouth_height'])
                        temporal_features['mouth_width'].append(features['mouth_width'])
                        temporal_features['mouth_area'].append(features['mouth_area'])
                    else:
                        landmark_failures += 1
                else:
                    landmark_failures += 1

            # Check if landmarks failed too often
            if landmark_failures > len(frames) * 0.5:  # More than 50% failures
                landmarks_ok = False
                print(f"⚠️  Landmark failures for {video_path.name}, using motion fallback")
                temporal_features = compute_motion_fallback_features(frames)
        else:
            # Use motion-only features when MediaPipe not available
            landmarks_ok = False
            temporal_features = compute_motion_fallback_features(frames)
        
        # Pad missing values if needed
        target_length = len(frames)
        for key in temporal_features:
            while len(temporal_features[key]) < target_length:
                if len(temporal_features[key]) > 0:
                    temporal_features[key].append(temporal_features[key][-1])  # Repeat last value
                else:
                    temporal_features[key].append(0.0)  # Default value
        
        # Compute aggregate features
        duration_frames = len(frames)
        
        # Velocity (frame-to-frame changes)
        velocities = []
        for key in ['mouth_height', 'mouth_width', 'mouth_area']:
            values = temporal_features[key]
            if len(values) > 1:
                velocity = [abs(values[i] - values[i-1]) for i in range(1, len(values))]
                velocities.extend(velocity)
        
        avg_velocity = np.mean(velocities) if velocities else 0.0
        
        # Peak detection (simple)
        mouth_heights = temporal_features['mouth_height']
        if len(mouth_heights) > 3:
            # Find local maxima
            peaks = []
            for i in range(1, len(mouth_heights) - 1):
                if mouth_heights[i] > mouth_heights[i-1] and mouth_heights[i] > mouth_heights[i+1]:
                    peaks.append(i)
            num_peaks = len(peaks)
            peak_position = np.mean(peaks) / len(mouth_heights) if peaks else 0.5
        else:
            num_peaks = 0
            peak_position = 0.5
        
        # Phonetic indicators
        # closure_score_end: High closure at end (for /p/ in "help")
        end_frames = mouth_heights[-3:] if len(mouth_heights) >= 3 else mouth_heights
        closure_score_end = 1.0 - (np.mean(end_frames) / (np.max(mouth_heights) + 1e-6))
        
        # vowel_tail_score: High opening at end (for /o/ in "hello")  
        vowel_tail_score = np.mean(end_frames) / (np.max(mouth_heights) + 1e-6)
        
        # Z-score normalize temporal features
        for key in temporal_features:
            values = np.array(temporal_features[key])
            if np.std(values) > 1e-6:
                temporal_features[key] = zscore(values).tolist()
            else:
                temporal_features[key] = [0.0] * len(values)
        
        return {
            'video_path': str(video_path),
            'duration_frames': duration_frames,
            'fps': fps,
            'landmarks_ok': landmarks_ok,
            'temporal_features': temporal_features,
            'avg_velocity': avg_velocity,
            'num_peaks': num_peaks,
            'peak_position': peak_position,
            'closure_score_end': closure_score_end,
            'vowel_tail_score': vowel_tail_score
        }
        
    except Exception as e:
        return {'error': f'Feature extraction failed for {video_path}: {e}'}


def compute_dtw_distance(seq1: List[float], seq2: List[float]) -> float:
    """Compute Dynamic Time Warping distance between two sequences."""
    try:
        from scipy.spatial.distance import euclidean
        
        n, m = len(seq1), len(seq2)
        if n == 0 or m == 0:
            return float('inf')
        
        # Create DTW matrix
        dtw_matrix = np.full((n + 1, m + 1), float('inf'))
        dtw_matrix[0, 0] = 0
        
        for i in range(1, n + 1):
            for j in range(1, m + 1):
                cost = abs(seq1[i-1] - seq2[j-1])
                dtw_matrix[i, j] = cost + min(
                    dtw_matrix[i-1, j],      # insertion
                    dtw_matrix[i, j-1],      # deletion
                    dtw_matrix[i-1, j-1]     # match
                )
        
        return dtw_matrix[n, m] / max(n, m)  # Normalize by length
        
    except Exception:
        # Fallback: simple euclidean distance with padding
        max_len = max(len(seq1), len(seq2))
        padded_seq1 = seq1 + [seq1[-1]] * (max_len - len(seq1)) if seq1 else [0] * max_len
        padded_seq2 = seq2 + [seq2[-1]] * (max_len - len(seq2)) if seq2 else [0] * max_len
        return euclidean(padded_seq1, padded_seq2) / max_len


def build_phrase_prototypes(features_by_phrase: Dict[str, List[Dict]], exclude_outlier_frac: float = 0.15) -> Dict[str, Dict]:
    """Build phrase prototypes using central 70% of clips (exclude outliers)."""
    prototypes = {}

    for phrase, phrase_features in features_by_phrase.items():
        if len(phrase_features) < 5:  # Need minimum samples
            continue

        # Extract temporal sequences for DTW alignment
        mouth_height_seqs = [f['temporal_features']['mouth_height'] for f in phrase_features if 'temporal_features' in f]

        if not mouth_height_seqs:
            continue

        # Compute pairwise DTW distances to find central clips
        n_clips = len(mouth_height_seqs)
        distances = np.zeros((n_clips, n_clips))

        for i in range(n_clips):
            for j in range(i+1, n_clips):
                dist = compute_dtw_distance(mouth_height_seqs[i], mouth_height_seqs[j])
                distances[i, j] = distances[j, i] = dist

        # Find clips with lowest average distance to others (most central)
        avg_distances = np.mean(distances, axis=1)
        central_indices = np.argsort(avg_distances)

        # Use central 70% (exclude top and bottom 15%)
        n_exclude = int(exclude_outlier_frac * n_clips)
        central_indices = central_indices[n_exclude:-n_exclude] if n_exclude > 0 else central_indices

        if len(central_indices) == 0:
            central_indices = [0]  # Fallback

        # Compute prototype as median of central clips
        central_features = [phrase_features[i] for i in central_indices]

        # Handle variable-length sequences for prototype trajectory
        trajectories = []
        for f in central_features:
            if 'temporal_features' in f and 'mouth_height' in f['temporal_features']:
                trajectories.append(f['temporal_features']['mouth_height'])
            else:
                # Skip features without temporal data
                continue

        # Find common length (use median length)
        lengths = [len(traj) for traj in trajectories]
        target_length = int(np.median(lengths))

        # Resample all trajectories to common length
        resampled_trajectories = []
        for traj in trajectories:
            if len(traj) == 0:
                resampled_trajectories.append([0.0] * target_length)
            elif len(traj) == target_length:
                resampled_trajectories.append(traj)
            else:
                # Simple linear interpolation/resampling
                indices = np.linspace(0, len(traj) - 1, target_length)
                resampled = np.interp(indices, range(len(traj)), traj)
                resampled_trajectories.append(resampled.tolist())

        # Now compute median trajectory
        if resampled_trajectories:
            prototype_trajectory = np.median(resampled_trajectories, axis=0).tolist()
        else:
            prototype_trajectory = [0.0] * target_length

        # Aggregate features (with error handling)
        def safe_median(values, default=0.0):
            valid_values = [v for v in values if v is not None and not np.isnan(v)]
            return np.median(valid_values) if valid_values else default

        def safe_temporal_median(features, key):
            values = []
            for f in features:
                if 'temporal_features' in f and key in f['temporal_features'] and f['temporal_features'][key]:
                    values.append(np.median(f['temporal_features'][key]))
            return safe_median(values)

        prototype = {
            'mouth_height_median': safe_temporal_median(central_features, 'mouth_height'),
            'mouth_width_median': safe_temporal_median(central_features, 'mouth_width'),
            'mouth_area_median': safe_temporal_median(central_features, 'mouth_area'),
            'avg_velocity_median': safe_median([f.get('avg_velocity', 0.0) for f in central_features]),
            'num_peaks_median': safe_median([f.get('num_peaks', 0.0) for f in central_features]),
            'peak_position_median': safe_median([f.get('peak_position', 0.5) for f in central_features]),
            'closure_score_end_median': safe_median([f.get('closure_score_end', 0.0) for f in central_features]),
            'vowel_tail_score_median': safe_median([f.get('vowel_tail_score', 0.0) for f in central_features]),
            'duration_frames_median': safe_median([f.get('duration_frames', 0.0) for f in central_features]),
            'prototype_trajectory': prototype_trajectory,
            'n_central_clips': len(central_indices),
            'n_total_clips': len(phrase_features)
        }

        prototypes[phrase] = prototype

    return prototypes


def compute_suspicion_scores(features_by_phrase: Dict[str, List[Dict]], prototypes: Dict[str, Dict]) -> List[Dict]:
    """Compute suspicion scores for all videos."""
    all_scores = []

    # Prepare data for discriminative model
    X_all = []
    y_all = []
    video_info = []

    for phrase, phrase_features in features_by_phrase.items():
        if phrase not in prototypes:
            continue

        prototype = prototypes[phrase]

        for feature_dict in phrase_features:
            if 'error' in feature_dict:
                continue

            # Feature vector for discriminative model
            feature_vector = [
                feature_dict['avg_velocity'],
                feature_dict['num_peaks'],
                feature_dict['peak_position'],
                feature_dict['closure_score_end'],
                feature_dict['vowel_tail_score'],
                feature_dict['duration_frames']
            ]

            X_all.append(feature_vector)
            y_all.append(phrase)
            video_info.append((phrase, feature_dict))

    # Train discriminative model with cross-validation
    discriminative_probs = {}
    if len(set(y_all)) > 1 and len(X_all) > 10:
        try:
            X_scaled = StandardScaler().fit_transform(X_all)
            cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
            lr = LogisticRegression(random_state=42, max_iter=1000)

            # Get cross-validated probabilities
            y_probs = cross_val_predict(lr, X_scaled, y_all, cv=cv, method='predict_proba')

            # Map probabilities back to videos
            for i, (phrase, feature_dict) in enumerate(video_info):
                video_path = feature_dict['video_path']
                # Get probability of correct class
                class_idx = list(lr.classes_).index(phrase) if phrase in lr.classes_ else 0
                correct_prob = y_probs[i][class_idx] if class_idx < len(y_probs[i]) else 0.5
                discriminative_probs[video_path] = correct_prob

        except Exception as e:
            print(f"⚠️  Discriminative model failed: {e}")
            discriminative_probs = {}

    # Compute suspicion scores for each video
    for phrase, phrase_features in features_by_phrase.items():
        if phrase not in prototypes:
            continue

        prototype = prototypes[phrase]
        phrase_scores = []

        # Collect feature values for outlier detection
        phrase_velocities = [f['avg_velocity'] for f in phrase_features if 'error' not in f]
        phrase_closures = [f['closure_score_end'] for f in phrase_features if 'error' not in f]
        phrase_vowels = [f['vowel_tail_score'] for f in phrase_features if 'error' not in f]

        for feature_dict in phrase_features:
            if 'error' in feature_dict:
                score_dict = {
                    'video_path': feature_dict.get('video_path', ''),
                    'phrase': phrase,
                    'suspicion_score': 1.0,  # Max suspicion for errors
                    'dtw_distance': 1.0,
                    'outlier_score': 1.0,
                    'discriminative_prob': 0.0,
                    'heuristic_penalty': 0.0,
                    'landmarks_ok': False,
                    'reason': feature_dict.get('error', 'Unknown error')
                }
                phrase_scores.append(score_dict)
                continue

            # 1. DTW distance to prototype (55% weight)
            video_trajectory = []
            if 'temporal_features' in feature_dict and 'mouth_height' in feature_dict['temporal_features']:
                video_trajectory = feature_dict['temporal_features']['mouth_height']

            prototype_trajectory = prototype['prototype_trajectory']

            # Handle empty trajectories
            if not video_trajectory or not prototype_trajectory:
                dtw_dist = 1.0  # Maximum distance for empty sequences
            else:
                dtw_dist = compute_dtw_distance(video_trajectory, prototype_trajectory)

            dtw_score = min(1.0, dtw_dist / 2.0)  # Normalize

            # 2. Feature outlier score within phrase (25% weight)
            outlier_components = []

            # Velocity outlier
            if phrase_velocities:
                velocity_zscore = abs((feature_dict['avg_velocity'] - np.mean(phrase_velocities)) / (np.std(phrase_velocities) + 1e-6))
                outlier_components.append(min(1.0, velocity_zscore / 3.0))

            # Closure outlier
            if phrase_closures:
                closure_zscore = abs((feature_dict['closure_score_end'] - np.mean(phrase_closures)) / (np.std(phrase_closures) + 1e-6))
                outlier_components.append(min(1.0, closure_zscore / 3.0))

            # Vowel outlier
            if phrase_vowels:
                vowel_zscore = abs((feature_dict['vowel_tail_score'] - np.mean(phrase_vowels)) / (np.std(phrase_vowels) + 1e-6))
                outlier_components.append(min(1.0, vowel_zscore / 3.0))

            outlier_score = np.mean(outlier_components) if outlier_components else 0.0

            # 3. Discriminative probability (20% weight)
            video_path = feature_dict['video_path']
            discriminative_prob = discriminative_probs.get(video_path, 0.5)
            discriminative_score = 1.0 - discriminative_prob  # Convert to suspicion

            # 4. Heuristic penalties for common confusions
            heuristic_penalty = 0.0

            # "help" with high vowel_tail_score (should end closed, not open)
            if phrase == 'help' and feature_dict['vowel_tail_score'] > 0.7:
                heuristic_penalty += 0.75

            # "hello" with high closure_score_end (should end open, not closed)
            if phrase == 'hello' and feature_dict['closure_score_end'] > 0.7:
                heuristic_penalty += 0.5

            # Combine scores
            suspicion_score = (
                0.55 * dtw_score +
                0.25 * outlier_score +
                0.20 * discriminative_score +
                heuristic_penalty
            )
            suspicion_score = min(1.0, suspicion_score)  # Cap at 1.0

            score_dict = {
                'video_path': video_path,
                'phrase': phrase,
                'suspicion_score': suspicion_score,
                'dtw_distance': dtw_dist,
                'outlier_score': outlier_score,
                'discriminative_prob': discriminative_prob,
                'heuristic_penalty': heuristic_penalty,
                'landmarks_ok': feature_dict['landmarks_ok'],
                'reason': ''
            }

            phrase_scores.append(score_dict)

        all_scores.extend(phrase_scores)

    return all_scores


def apply_decision_thresholds(scores: List[Dict], review_top_frac: float, reject_top_frac: float) -> Dict[str, List[Dict]]:
    """Apply decision thresholds per phrase to categorize videos."""
    decisions = {'keep': [], 'review': [], 'reject': []}

    # Group scores by phrase
    scores_by_phrase = {}
    for score in scores:
        phrase = score['phrase']
        if phrase not in scores_by_phrase:
            scores_by_phrase[phrase] = []
        scores_by_phrase[phrase].append(score)

    # Apply thresholds per phrase
    for phrase, phrase_scores in scores_by_phrase.items():
        # Sort by suspicion score (descending)
        phrase_scores.sort(key=lambda x: x['suspicion_score'], reverse=True)

        n_total = len(phrase_scores)
        n_reject = max(1, int(reject_top_frac * n_total))
        n_review = max(1, int(review_top_frac * n_total))

        # Assign decisions
        for i, score in enumerate(phrase_scores):
            if i < n_reject:
                score['decision'] = 'reject'
                decisions['reject'].append(score)
            elif i < n_reject + n_review:
                score['decision'] = 'review'
                decisions['review'].append(score)
            else:
                score['decision'] = 'keep'
                decisions['keep'].append(score)

    return decisions


def organize_videos(input_dir: str, output_dir: str, decisions: Dict[str, List[Dict]]):
    """Organize videos into keep/review/reject folders."""
    input_path = Path(input_dir)
    output_path = Path(output_dir)

    # Create output directories
    for decision in ['keep', 'review', 'reject']:
        (output_path / decision).mkdir(parents=True, exist_ok=True)

    # Copy videos to appropriate folders
    for decision, score_list in decisions.items():
        for score in score_list:
            video_path = Path(score['video_path'])
            rel_path = video_path.relative_to(input_path)

            # Preserve directory structure
            dest_dir = output_path / decision / rel_path.parent
            dest_dir.mkdir(parents=True, exist_ok=True)

            dest_path = dest_dir / video_path.name

            try:
                shutil.copy2(video_path, dest_path)
            except Exception as e:
                print(f"⚠️  Failed to copy {video_path}: {e}")


def generate_reports(scores: List[Dict], decisions: Dict[str, List[Dict]],
                    prototypes: Dict[str, Dict], audit_dir: str, frames_for_gallery: int = 3):
    """Generate comprehensive audit reports."""
    audit_path = Path(audit_dir)
    audit_path.mkdir(parents=True, exist_ok=True)

    # 1. Summary CSV
    summary_data = []
    for phrase in prototypes.keys():
        phrase_scores = [s for s in scores if s['phrase'] == phrase]
        phrase_decisions = {
            'keep': len([s for s in phrase_scores if s.get('decision') == 'keep']),
            'review': len([s for s in phrase_scores if s.get('decision') == 'review']),
            'reject': len([s for s in phrase_scores if s.get('decision') == 'reject'])
        }

        suspicion_scores = [s['suspicion_score'] for s in phrase_scores]

        summary_data.append({
            'phrase': phrase,
            'total_videos': len(phrase_scores),
            'keep_count': phrase_decisions['keep'],
            'review_count': phrase_decisions['review'],
            'reject_count': phrase_decisions['reject'],
            'avg_suspicion': np.mean(suspicion_scores) if suspicion_scores else 0,
            'max_suspicion': np.max(suspicion_scores) if suspicion_scores else 0,
            'prototype_clips': prototypes[phrase]['n_central_clips']
        })

    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv(audit_path / 'label_audit_summary.csv', index=False)

    # 2. Detailed scores CSV
    scores_df = pd.DataFrame(scores)
    scores_df.to_csv(audit_path / 'label_audit_scores.csv', index=False)

    # 3. HTML Dashboard
    generate_html_dashboard(scores, decisions, prototypes, audit_path, frames_for_gallery)

    print(f"📊 Reports generated in {audit_dir}")


def generate_html_dashboard(scores: List[Dict], decisions: Dict[str, List[Dict]],
                           prototypes: Dict[str, Dict], audit_path: Path, frames_for_gallery: int):
    """Generate interactive HTML dashboard."""

    template_str = """
<!DOCTYPE html>
<html>
<head>
    <title>Label Quality Audit Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .stats { display: flex; gap: 20px; margin: 10px 0; flex-wrap: wrap; }
        .stat { background: #e3f2fd; padding: 10px; border-radius: 4px; min-width: 120px; }
        .phrase-section { background: white; margin: 20px 0; padding: 15px; border-radius: 8px; }
        .phrase-title { font-size: 18px; font-weight: bold; color: #1976d2; margin-bottom: 10px; }
        .video-card { background: #f9f9f9; margin: 10px 0; padding: 10px; border-radius: 4px; border-left: 4px solid #4caf50; }
        .video-card.review { border-left-color: #ff9800; }
        .video-card.reject { border-left-color: #f44336; }
        .video-path { font-weight: bold; font-size: 12px; }
        .badges { display: flex; gap: 5px; margin: 5px 0; flex-wrap: wrap; }
        .badge { padding: 2px 6px; border-radius: 10px; font-size: 11px; color: white; }
        .badge.keep { background: #4caf50; }
        .badge.review { background: #ff9800; }
        .badge.reject { background: #f44336; }
        .badge.score { background: #2196f3; }
        .badge.landmarks { background: #9c27b0; }
        .metrics { font-size: 11px; color: #666; margin: 5px 0; }
        .top-suspects { margin: 10px 0; }
        .scatter-plot { margin: 20px 0; text-align: center; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Label Quality Audit Dashboard</h1>
        <div class="stats">
            <div class="stat">Total Videos: {{ total_videos }}</div>
            <div class="stat">Keep: {{ keep_count }} ({{ keep_pct }}%)</div>
            <div class="stat">Review: {{ review_count }} ({{ review_pct }}%)</div>
            <div class="stat">Reject: {{ reject_count }} ({{ reject_pct }}%)</div>
            <div class="stat">Avg Suspicion: {{ avg_suspicion }}</div>
        </div>
    </div>

    {% for phrase, phrase_data in phrases.items() %}
    <div class="phrase-section">
        <div class="phrase-title">{{ phrase.upper() }} ({{ phrase_data.total }} videos)</div>
        <div class="stats">
            <div class="stat">Keep: {{ phrase_data.keep }}</div>
            <div class="stat">Review: {{ phrase_data.review }}</div>
            <div class="stat">Reject: {{ phrase_data.reject }}</div>
            <div class="stat">Avg Suspicion: {{ "%.3f"|format(phrase_data.avg_suspicion) }}</div>
        </div>

        <div class="top-suspects">
            <strong>Top 5 Most Suspicious:</strong>
            {% for video in phrase_data.top_suspects %}
            <div class="video-card {{ video.decision }}">
                <div class="video-path">{{ video.video_path.split('/')[-1] }}</div>
                <div class="badges">
                    <span class="badge {{ video.decision }}">{{ video.decision.upper() }}</span>
                    <span class="badge score">{{ "%.3f"|format(video.suspicion_score) }}</span>
                    {% if video.landmarks_ok %}
                    <span class="badge landmarks">LANDMARKS</span>
                    {% else %}
                    <span class="badge landmarks">MOTION</span>
                    {% endif %}
                </div>
                <div class="metrics">
                    DTW: {{ "%.3f"|format(video.dtw_distance) }} |
                    Outlier: {{ "%.3f"|format(video.outlier_score) }} |
                    Discrim: {{ "%.3f"|format(video.discriminative_prob) }}
                    {% if video.heuristic_penalty > 0 %}
                    | Penalty: {{ "%.3f"|format(video.heuristic_penalty) }}
                    {% endif %}
                </div>
                {% if video.reason %}
                <div class="metrics" style="color: #f44336;">{{ video.reason }}</div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
    {% endfor %}

    <div class="phrase-section">
        <div class="phrase-title">HELLO vs HELP Diagnostic</div>
        <div class="scatter-plot">
            <p>Scatter plot of closure_score_end vs vowel_tail_score would go here</p>
            <p>Expected: HELLO (low closure, high vowel) vs HELP (high closure, low vowel)</p>
        </div>
    </div>
</body>
</html>
    """

    # Prepare template data
    total_videos = len(scores)
    keep_count = len(decisions['keep'])
    review_count = len(decisions['review'])
    reject_count = len(decisions['reject'])

    keep_pct = f"{keep_count/total_videos*100:.1f}" if total_videos > 0 else "0.0"
    review_pct = f"{review_count/total_videos*100:.1f}" if total_videos > 0 else "0.0"
    reject_pct = f"{reject_count/total_videos*100:.1f}" if total_videos > 0 else "0.0"

    avg_suspicion = f"{np.mean([s['suspicion_score'] for s in scores]):.3f}" if scores else "0.000"

    # Group by phrase
    phrases = {}
    for phrase in prototypes.keys():
        phrase_scores = [s for s in scores if s['phrase'] == phrase]
        phrase_scores.sort(key=lambda x: x['suspicion_score'], reverse=True)

        phrases[phrase] = {
            'total': len(phrase_scores),
            'keep': len([s for s in phrase_scores if s.get('decision') == 'keep']),
            'review': len([s for s in phrase_scores if s.get('decision') == 'review']),
            'reject': len([s for s in phrase_scores if s.get('decision') == 'reject']),
            'avg_suspicion': np.mean([s['suspicion_score'] for s in phrase_scores]) if phrase_scores else 0,
            'top_suspects': phrase_scores[:5]  # Top 5 most suspicious
        }

    template = Template(template_str)
    html_content = template.render(
        total_videos=total_videos,
        keep_count=keep_count,
        review_count=review_count,
        reject_count=reject_count,
        keep_pct=keep_pct,
        review_pct=review_pct,
        reject_pct=reject_pct,
        avg_suspicion=avg_suspicion,
        phrases=phrases
    )

    with open(audit_path / 'label_audit.html', 'w') as f:
        f.write(html_content)


def create_clean_manifest(original_manifest: Optional[str], scores: List[Dict], output_path: str):
    """Create cleaned manifest excluding rejected videos."""
    if original_manifest and os.path.exists(original_manifest):
        # Load original manifest
        df = pd.read_csv(original_manifest)

        # Create mapping of video paths to decisions
        decisions_map = {}
        for score in scores:
            video_path = score['video_path']
            decisions_map[video_path] = score.get('decision', 'keep')

        # Filter out rejected videos and add screen_decision column
        df['screen_decision'] = df.apply(
            lambda row: decisions_map.get(row.get('path_masked', ''), 'keep'), axis=1
        )

        # Keep only non-rejected videos
        df_clean = df[df['screen_decision'] != 'reject'].copy()

        # Save cleaned manifest
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        df_clean.to_csv(output_path, index=False)

        print(f"📝 Clean manifest saved: {len(df_clean)}/{len(df)} videos retained")
    else:
        print("⚠️  No original manifest found, skipping clean manifest generation")


def main():
    parser = argparse.ArgumentParser(description='Label Quality Audit System')
    parser.add_argument('--input_dir', required=True, help='Input directory with motion-refined crops')
    parser.add_argument('--manifest_in', help='Input manifest CSV (optional)')
    parser.add_argument('--audit_dir', required=True, help='Audit reports output directory')
    parser.add_argument('--output_dir', required=True, help='Screened videos output directory')
    parser.add_argument('--frames_for_gallery', type=int, default=3, help='Frames for gallery display')
    parser.add_argument('--upsample', type=float, default=2.0, help='Upsample factor for analysis')
    parser.add_argument('--min_frames', type=int, default=12, help='Minimum frames required')
    parser.add_argument('--review_top_frac', type=float, default=0.10, help='Fraction for review category')
    parser.add_argument('--reject_top_frac', type=float, default=0.03, help='Fraction for reject category')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')

    args = parser.parse_args()

    # Set random seed
    np.random.seed(args.seed)

    print("🔍 Label Quality Audit System")
    print(f"Input: {args.input_dir}")
    print(f"Output: {args.output_dir}")
    print(f"Audit: {args.audit_dir}")

    # Find videos
    extensions = ['.mp4', '.mov', '.mkv', '.avi', '.webm']
    videos = find_videos(args.input_dir, extensions)
    print(f"📹 Found {len(videos)} videos")

    if len(videos) == 0:
        print("❌ No videos found!")
        return

    # Extract features from all videos
    print("🔄 Extracting features...")
    all_features = []
    error_log = []

    for video in tqdm(videos, desc="Processing videos"):
        features = extract_video_features(video, args.upsample, args.min_frames)

        if 'error' in features:
            error_log.append(features['error'])
            # Still add to features for tracking
            features['video_path'] = str(video)
            features['phrase'] = extract_phrase_from_path(video)
        else:
            features['phrase'] = extract_phrase_from_path(video)

        all_features.append(features)

    # Log errors
    if error_log:
        error_log_path = Path(args.audit_dir) / 'skips.txt'
        error_log_path.parent.mkdir(parents=True, exist_ok=True)
        with open(error_log_path, 'w') as f:
            for error in error_log:
                f.write(f"{error}\n")
        print(f"⚠️  {len(error_log)} errors logged to {error_log_path}")

    # Group features by phrase
    features_by_phrase = {}
    for features in all_features:
        phrase = features['phrase']
        if phrase not in features_by_phrase:
            features_by_phrase[phrase] = []
        features_by_phrase[phrase].append(features)

    print(f"📊 Found phrases: {list(features_by_phrase.keys())}")
    for phrase, phrase_features in features_by_phrase.items():
        valid_count = len([f for f in phrase_features if 'error' not in f])
        print(f"  {phrase}: {valid_count}/{len(phrase_features)} valid videos")

    # Build phrase prototypes
    print("🏗️  Building phrase prototypes...")
    prototypes = build_phrase_prototypes(features_by_phrase)
    print(f"✅ Built prototypes for {len(prototypes)} phrases")

    # Compute suspicion scores
    print("🎯 Computing suspicion scores...")
    scores = compute_suspicion_scores(features_by_phrase, prototypes)
    print(f"📊 Computed scores for {len(scores)} videos")

    # Apply decision thresholds
    print("⚖️  Applying decision thresholds...")
    decisions = apply_decision_thresholds(scores, args.review_top_frac, args.reject_top_frac)

    # Print summary
    print(f"\n📈 Decision Summary:")
    print(f"  Keep: {len(decisions['keep'])} videos")
    print(f"  Review: {len(decisions['review'])} videos")
    print(f"  Reject: {len(decisions['reject'])} videos")

    # Check for phrases with too few samples after rejection
    for phrase in prototypes.keys():
        phrase_keep = len([s for s in decisions['keep'] if s['phrase'] == phrase])
        if phrase_keep < 150:
            print(f"⚠️  Warning: {phrase} has only {phrase_keep} videos after screening")

    # Print top suspects per phrase
    print(f"\n🔍 Top 5 Most Suspicious per Phrase:")
    for phrase in prototypes.keys():
        phrase_scores = [s for s in scores if s['phrase'] == phrase]
        phrase_scores.sort(key=lambda x: x['suspicion_score'], reverse=True)

        print(f"\n{phrase.upper()}:")
        for i, score in enumerate(phrase_scores[:5]):
            video_name = Path(score['video_path']).name
            reason = f" ({score['reason']})" if score['reason'] else ""
            print(f"  {i+1}. {video_name}: {score['suspicion_score']:.3f}{reason}")

    # Organize videos
    print(f"\n📁 Organizing videos...")
    organize_videos(args.input_dir, args.output_dir, decisions)

    # Generate reports
    print("📊 Generating reports...")
    generate_reports(scores, decisions, prototypes, args.audit_dir, args.frames_for_gallery)

    # Create clean manifest
    clean_manifest_path = args.manifest_in.replace('.csv', '_clean.csv') if args.manifest_in else 'data/manifests/stage3_manifest_clean.csv'
    create_clean_manifest(args.manifest_in, scores, clean_manifest_path)

    print(f"\n✅ Label quality audit complete!")
    print(f"🎨 Dashboard: {args.audit_dir}/label_audit.html")
    print(f"📁 Screened videos: {args.output_dir}")
    print(f"📝 Clean manifest: {clean_manifest_path}")


if __name__ == '__main__':
    main()
