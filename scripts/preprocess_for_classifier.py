#!/usr/bin/env python3
"""
Preprocessing <PERSON>rip<PERSON> for Classifier Training

Converts top-quality motion-detected videos from label audit results into 
numpy arrays for CNN classifier training.

Usage:
    python scripts/preprocess_for_classifier.py \
      --input_dir data/label_screened_v1/keep \
      --audit_scores reports/label_audit_v1/label_audit_scores.csv \
      --output_dir data/classifier_ready \
      --target_size 96 \
      --target_frames auto \
      --normalize

Requirements:
    opencv-python, numpy, pandas, tqdm
"""

import argparse
import cv2
import numpy as np
import pandas as pd
import os
import json
import csv
from pathlib import Path
from tqdm import tqdm
from typing import List, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')


def find_videos_by_phrase(input_dir: str, extensions: List[str]) -> Dict[str, List[Path]]:
    """Find videos organized by phrase from directory structure."""
    input_path = Path(input_dir)
    videos_by_phrase = {}
    
    # Expected phrases
    phrases = ['doctor', 'glasses', 'help', 'i_need_to_move', 'my_mouth_is_dry', 'phone', 'pillow']
    
    for phrase in phrases:
        videos_by_phrase[phrase] = []
    
    # Find all videos
    for ext in extensions:
        for video_path in input_path.rglob(f'*{ext}'):
            # Extract phrase from path
            path_parts = video_path.parts
            detected_phrase = None
            
            for part in path_parts:
                for phrase in phrases:
                    if phrase in part or phrase.replace('_', ' ') in part.replace('_', ' '):
                        detected_phrase = phrase
                        break
                if detected_phrase:
                    break
            
            if detected_phrase:
                videos_by_phrase[detected_phrase].append(video_path)
    
    return videos_by_phrase


def load_audit_scores(scores_path: str) -> pd.DataFrame:
    """Load audit scores CSV."""
    if not os.path.exists(scores_path):
        print(f"⚠️  Audit scores not found: {scores_path}")
        return pd.DataFrame()
    
    return pd.read_csv(scores_path)


def select_best_videos(videos_by_phrase: Dict[str, List[Path]], 
                      audit_scores: pd.DataFrame) -> Dict[str, Path]:
    """Select the best video per phrase based on audit scores."""
    best_videos = {}
    
    for phrase, video_paths in videos_by_phrase.items():
        if not video_paths:
            print(f"⚠️  No videos found for phrase: {phrase}")
            continue
        
        if audit_scores.empty:
            # Fallback: select first video if no audit scores
            best_videos[phrase] = video_paths[0]
            print(f"📹 Selected (no scores): {phrase} -> {video_paths[0].name}")
            continue
        
        # Find videos with lowest suspicion scores (highest quality)
        phrase_scores = []
        for video_path in video_paths:
            # Match by filename
            video_name = video_path.name
            matching_rows = audit_scores[audit_scores['video_path'].str.contains(video_name, na=False)]
            
            if not matching_rows.empty:
                suspicion_score = matching_rows.iloc[0]['suspicion_score']
                phrase_scores.append((video_path, suspicion_score))
        
        if phrase_scores:
            # Select video with lowest suspicion score
            best_video, best_score = min(phrase_scores, key=lambda x: x[1])
            best_videos[phrase] = best_video
            print(f"📹 Selected: {phrase} -> {best_video.name} (score: {best_score:.3f})")
        else:
            # Fallback: select first video
            best_videos[phrase] = video_paths[0]
            print(f"📹 Selected (fallback): {phrase} -> {video_paths[0].name}")
    
    return best_videos


def read_video_frames(video_path: Path, target_size: int) -> np.ndarray:
    """Read video frames and resize to target size."""
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        raise ValueError(f"Cannot open video: {video_path}")
    
    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Resize to target size
        frame_resized = cv2.resize(frame_rgb, (target_size, target_size), interpolation=cv2.INTER_AREA)
        
        frames.append(frame_resized)
    
    cap.release()
    
    if not frames:
        raise ValueError(f"No frames found in video: {video_path}")
    
    return np.array(frames, dtype=np.uint8)


def standardize_frame_count(videos_data: Dict[str, np.ndarray], target_frames: Optional[int] = None) -> Dict[str, np.ndarray]:
    """Standardize frame count across videos."""
    frame_counts = [video.shape[0] for video in videos_data.values()]
    
    if target_frames is None:
        # Use median frame count
        target_frames = int(np.median(frame_counts))
    
    print(f"🎬 Standardizing to {target_frames} frames")
    print(f"📊 Original frame counts: min={min(frame_counts)}, max={max(frame_counts)}, median={np.median(frame_counts):.1f}")
    
    standardized_videos = {}
    
    for phrase, video_data in videos_data.items():
        current_frames = video_data.shape[0]
        
        if current_frames == target_frames:
            standardized_videos[phrase] = video_data
        elif current_frames > target_frames:
            # Truncate: take evenly spaced frames
            indices = np.linspace(0, current_frames - 1, target_frames, dtype=int)
            standardized_videos[phrase] = video_data[indices]
            print(f"  📉 {phrase}: {current_frames} -> {target_frames} (truncated)")
        else:
            # Pad: repeat last frame
            padding_needed = target_frames - current_frames
            last_frame = video_data[-1:].repeat(padding_needed, axis=0)
            standardized_videos[phrase] = np.concatenate([video_data, last_frame], axis=0)
            print(f"  📈 {phrase}: {current_frames} -> {target_frames} (padded)")
    
    return standardized_videos


def normalize_videos(videos_data: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
    """Normalize pixel values to [0, 1] range."""
    normalized_videos = {}
    
    for phrase, video_data in videos_data.items():
        # Convert to float32 and normalize
        normalized = video_data.astype(np.float32) / 255.0
        normalized_videos[phrase] = normalized
        
        print(f"🔢 {phrase}: shape={normalized.shape}, range=[{normalized.min():.3f}, {normalized.max():.3f}]")
    
    return normalized_videos


def apply_data_augmentation_prep(videos_data: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
    """Prepare videos for data augmentation (contrast/brightness normalization)."""
    augmented_videos = {}
    
    for phrase, video_data in videos_data.items():
        # Apply histogram equalization per frame for better contrast
        processed_frames = []
        
        for frame in video_data:
            # Convert to uint8 for processing
            frame_uint8 = (frame * 255).astype(np.uint8)
            
            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) to each channel
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            
            enhanced_frame = np.zeros_like(frame_uint8)
            for c in range(3):  # RGB channels
                enhanced_frame[:, :, c] = clahe.apply(frame_uint8[:, :, c])
            
            # Convert back to float32 [0, 1]
            enhanced_frame = enhanced_frame.astype(np.float32) / 255.0
            processed_frames.append(enhanced_frame)
        
        augmented_videos[phrase] = np.array(processed_frames)
        print(f"✨ {phrase}: Applied contrast enhancement")
    
    return augmented_videos


def save_preprocessed_data(videos_data: Dict[str, np.ndarray], output_dir: str):
    """Save preprocessed videos as numpy arrays."""
    output_path = Path(output_dir)
    preprocessed_dir = output_path / "preprocessed_videos"
    preprocessed_dir.mkdir(parents=True, exist_ok=True)
    
    # Create phrase to class ID mapping
    phrases = sorted(videos_data.keys())
    labels_mapping = {phrase: idx for idx, phrase in enumerate(phrases)}
    
    # Save labels mapping
    labels_path = output_path / "labels.json"
    with open(labels_path, 'w') as f:
        json.dump(labels_mapping, f, indent=2)
    
    print(f"📋 Labels mapping saved: {labels_path}")
    for phrase, class_id in labels_mapping.items():
        print(f"  {class_id}: {phrase}")
    
    # Save preprocessed videos
    manifest_data = []
    
    for phrase, video_data in videos_data.items():
        # Save numpy array
        npy_filename = f"{phrase}_preprocessed.npy"
        npy_path = preprocessed_dir / npy_filename
        
        np.save(str(npy_path), video_data)
        
        # Add to manifest
        manifest_data.append({
            'phrase': phrase,
            'class_id': labels_mapping[phrase],
            'file_path': str(npy_path),
            'shape': str(video_data.shape),
            'dtype': str(video_data.dtype),
            'frames': video_data.shape[0],
            'height': video_data.shape[1],
            'width': video_data.shape[2],
            'channels': video_data.shape[3],
            'min_value': float(video_data.min()),
            'max_value': float(video_data.max()),
            'mean_value': float(video_data.mean()),
            'std_value': float(video_data.std())
        })
        
        print(f"💾 Saved: {npy_path} (shape: {video_data.shape})")
    
    # Save training manifest
    manifest_path = output_path / "training_manifest.csv"
    manifest_df = pd.DataFrame(manifest_data)
    manifest_df.to_csv(manifest_path, index=False)
    
    print(f"📊 Training manifest saved: {manifest_path}")
    
    return labels_mapping, manifest_data


def main():
    parser = argparse.ArgumentParser(description='Preprocess videos for classifier training')
    parser.add_argument('--input_dir', default='data/label_screened_v1/keep', 
                       help='Input directory with high-quality videos')
    parser.add_argument('--audit_scores', default='reports/label_audit_v1/label_audit_scores.csv',
                       help='Audit scores CSV file')
    parser.add_argument('--output_dir', default='data/classifier_ready',
                       help='Output directory for preprocessed data')
    parser.add_argument('--target_size', type=int, default=96,
                       help='Target video resolution (square)')
    parser.add_argument('--target_frames', type=int, default=None,
                       help='Target frame count (auto=median if None)')
    parser.add_argument('--normalize', action='store_true', default=True,
                       help='Normalize pixel values to [0,1]')
    parser.add_argument('--enhance_contrast', action='store_true', default=True,
                       help='Apply contrast enhancement')
    
    args = parser.parse_args()
    
    print("🎬 Video Preprocessing for Classifier Training")
    print(f"📁 Input: {args.input_dir}")
    print(f"📊 Audit scores: {args.audit_scores}")
    print(f"💾 Output: {args.output_dir}")
    print(f"🎯 Target size: {args.target_size}x{args.target_size}")
    
    # Find videos by phrase
    print("\n🔍 Finding videos by phrase...")
    extensions = ['.mp4', '.mov', '.mkv', '.avi', '.webm']
    videos_by_phrase = find_videos_by_phrase(args.input_dir, extensions)
    
    total_found = sum(len(videos) for videos in videos_by_phrase.values())
    print(f"📹 Found {total_found} videos across {len(videos_by_phrase)} phrases")
    
    # Load audit scores
    print("\n📊 Loading audit scores...")
    audit_scores = load_audit_scores(args.audit_scores)
    
    # Select best videos
    print("\n🏆 Selecting best videos per phrase...")
    best_videos = select_best_videos(videos_by_phrase, audit_scores)
    
    if len(best_videos) == 0:
        print("❌ No videos selected!")
        return
    
    # Process videos
    print(f"\n🎬 Processing {len(best_videos)} videos...")
    videos_data = {}
    
    for phrase, video_path in tqdm(best_videos.items(), desc="Reading videos"):
        try:
            frames = read_video_frames(video_path, args.target_size)
            videos_data[phrase] = frames
            print(f"✅ {phrase}: {frames.shape[0]} frames, {frames.shape[1]}x{frames.shape[2]}x{frames.shape[3]}")
        except Exception as e:
            print(f"❌ Failed to process {phrase}: {e}")
    
    if not videos_data:
        print("❌ No videos successfully processed!")
        return
    
    # Standardize frame count
    print("\n🎬 Standardizing frame counts...")
    videos_data = standardize_frame_count(videos_data, args.target_frames)
    
    # Normalize pixel values
    if args.normalize:
        print("\n🔢 Normalizing pixel values...")
        videos_data = normalize_videos(videos_data)
    
    # Apply contrast enhancement
    if args.enhance_contrast:
        print("\n✨ Applying contrast enhancement...")
        videos_data = apply_data_augmentation_prep(videos_data)
    
    # Save preprocessed data
    print(f"\n💾 Saving preprocessed data to {args.output_dir}...")
    labels_mapping, manifest_data = save_preprocessed_data(videos_data, args.output_dir)
    
    # Print summary
    print(f"\n✅ Preprocessing complete!")
    print(f"📊 Processed {len(videos_data)} phrases")
    print(f"🎯 Target resolution: {args.target_size}x{args.target_size}")
    print(f"🎬 Frame count: {list(videos_data.values())[0].shape[0]}")
    print(f"📁 Output directory: {args.output_dir}")
    print(f"🏷️  Labels: {list(labels_mapping.keys())}")


if __name__ == '__main__':
    main()
