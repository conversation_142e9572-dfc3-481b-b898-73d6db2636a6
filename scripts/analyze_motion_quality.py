#!/usr/bin/env python3
"""
Motion Quality Analysis

Analyzes motion-guided cropping results to identify low-quality videos
and provide actionable filtering recommendations.

Usage:
    python scripts/analyze_motion_quality.py \
      --summary "reports/stage1M_full_summary.csv" \
      --output_candidates "reports/low_motion_candidates.csv" \
      --output_summary "reports/motion_quality_summary.txt" \
      --motion_threshold 0.15 \
      --heatmap_threshold 100 \
      --min_duration_threshold 0.5
"""

import argparse
import csv
import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict, Counter
import os


def analyze_motion_patterns(df, motion_threshold, heatmap_threshold):
    """Analyze motion patterns and categorize videos by quality."""
    
    # Define quality categories
    categories = {
        'excellent': [],
        'good': [],
        'fair': [],
        'poor': [],
        'very_poor': []
    }
    
    low_motion_candidates = []
    
    for _, row in df.iterrows():
        video_path = row['video_path']
        motion_share = row['motion_share']
        heatmap_max = row['heatmap_max']
        chose_fallback = row['chose_fallback']
        detection_rate = row['detection_rate']
        
        # Calculate quality score (0-100)
        motion_score = min(100, motion_share * 100)
        heatmap_score = min(100, heatmap_max / 10)  # Scale heatmap to 0-100
        detection_score = detection_rate * 100
        fallback_penalty = -50 if chose_fallback else 0
        
        quality_score = (motion_score * 0.4 + heatmap_score * 0.3 + 
                        detection_score * 0.2 + fallback_penalty * 0.1)
        
        # Categorize by quality
        if quality_score >= 80:
            categories['excellent'].append(video_path)
        elif quality_score >= 60:
            categories['good'].append(video_path)
        elif quality_score >= 40:
            categories['fair'].append(video_path)
        elif quality_score >= 20:
            categories['poor'].append(video_path)
        else:
            categories['very_poor'].append(video_path)
        
        # Identify candidates for removal
        reasons = []
        recommendation = "keep"
        
        if motion_share < motion_threshold:
            reasons.append(f"low_motion_share({motion_share:.3f})")
        
        if heatmap_max < heatmap_threshold:
            reasons.append(f"low_heatmap_intensity({heatmap_max:.1f})")
        
        if chose_fallback:
            reasons.append("used_fallback_crop")
        
        if detection_rate < 0.1 and motion_share < 0.2:
            reasons.append("no_face_detection_and_low_motion")
        
        # Determine recommendation
        if len(reasons) >= 2:
            recommendation = "remove"
        elif len(reasons) == 1 and (motion_share < 0.1 or chose_fallback):
            recommendation = "review"
        
        if reasons:  # Only add if there are issues
            low_motion_candidates.append({
                'video_path': video_path,
                'motion_share': motion_share,
                'heatmap_max': heatmap_max,
                'chose_fallback': chose_fallback,
                'detection_rate': detection_rate,
                'quality_score': quality_score,
                'reasons': ';'.join(reasons),
                'recommendation': recommendation
            })
    
    return categories, low_motion_candidates


def analyze_by_phrase(df):
    """Analyze motion quality distribution by phrase."""
    phrase_stats = defaultdict(lambda: {
        'total': 0,
        'fallback_count': 0,
        'low_motion_count': 0,
        'avg_motion_share': 0,
        'avg_heatmap_max': 0,
        'avg_detection_rate': 0
    })
    
    for _, row in df.iterrows():
        # Extract phrase from path
        path_parts = Path(row['video_path']).parts
        phrase = None
        for part in path_parts:
            if part in ['pillow', 'phone', 'i_need_to_move', 'doctor', 
                       'glasses', 'my_mouth_is_dry', 'help']:
                phrase = part
                break
        
        if phrase:
            stats = phrase_stats[phrase]
            stats['total'] += 1
            stats['fallback_count'] += int(row['chose_fallback'])
            stats['low_motion_count'] += int(row['motion_share'] < 0.15)
            stats['avg_motion_share'] += row['motion_share']
            stats['avg_heatmap_max'] += row['heatmap_max']
            stats['avg_detection_rate'] += row['detection_rate']
    
    # Calculate averages
    for phrase, stats in phrase_stats.items():
        if stats['total'] > 0:
            stats['avg_motion_share'] /= stats['total']
            stats['avg_heatmap_max'] /= stats['total']
            stats['avg_detection_rate'] /= stats['total']
            stats['fallback_rate'] = stats['fallback_count'] / stats['total']
            stats['low_motion_rate'] = stats['low_motion_count'] / stats['total']
    
    return dict(phrase_stats)


def generate_summary_report(df, categories, low_motion_candidates, phrase_stats, output_path):
    """Generate comprehensive text summary report."""
    
    total_videos = len(df)
    fallback_count = df['chose_fallback'].sum()
    
    with open(output_path, 'w') as f:
        f.write("MOTION-GUIDED CROPPING QUALITY ANALYSIS REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        # Overall Statistics
        f.write("OVERALL STATISTICS\n")
        f.write("-" * 20 + "\n")
        f.write(f"Total videos processed: {total_videos:,}\n")
        f.write(f"Videos using motion guidance: {total_videos - fallback_count:,} ({(total_videos - fallback_count)/total_videos*100:.1f}%)\n")
        f.write(f"Videos using fallback crop: {fallback_count:,} ({fallback_count/total_videos*100:.1f}%)\n")
        f.write(f"Average motion share: {df['motion_share'].mean():.3f}\n")
        f.write(f"Average heatmap intensity: {df['heatmap_max'].mean():.1f}\n")
        f.write(f"Average detection rate: {df['detection_rate'].mean():.3f}\n\n")
        
        # Quality Distribution
        f.write("QUALITY DISTRIBUTION\n")
        f.write("-" * 20 + "\n")
        for category, videos in categories.items():
            percentage = len(videos) / total_videos * 100
            f.write(f"{category.capitalize()}: {len(videos):,} videos ({percentage:.1f}%)\n")
        f.write("\n")
        
        # Low Motion Candidates Summary
        f.write("LOW MOTION CANDIDATES SUMMARY\n")
        f.write("-" * 30 + "\n")
        remove_count = sum(1 for c in low_motion_candidates if c['recommendation'] == 'remove')
        review_count = sum(1 for c in low_motion_candidates if c['recommendation'] == 'review')
        
        f.write(f"Total flagged videos: {len(low_motion_candidates):,}\n")
        f.write(f"Recommended for removal: {remove_count:,} ({remove_count/total_videos*100:.1f}%)\n")
        f.write(f"Recommended for review: {review_count:,} ({review_count/total_videos*100:.1f}%)\n")
        f.write(f"Recommended to keep: {total_videos - len(low_motion_candidates):,} ({(total_videos - len(low_motion_candidates))/total_videos*100:.1f}%)\n\n")
        
        # Phrase-by-Phrase Analysis
        f.write("PHRASE-BY-PHRASE ANALYSIS\n")
        f.write("-" * 25 + "\n")
        for phrase, stats in sorted(phrase_stats.items()):
            f.write(f"\n{phrase.upper()}:\n")
            f.write(f"  Total videos: {stats['total']:,}\n")
            f.write(f"  Fallback rate: {stats['fallback_rate']*100:.1f}%\n")
            f.write(f"  Low motion rate: {stats['low_motion_rate']*100:.1f}%\n")
            f.write(f"  Avg motion share: {stats['avg_motion_share']:.3f}\n")
            f.write(f"  Avg heatmap intensity: {stats['avg_heatmap_max']:.1f}\n")
            f.write(f"  Avg detection rate: {stats['avg_detection_rate']:.3f}\n")
        
        # Common Issues
        f.write("\n\nCOMMON ISSUES IDENTIFIED\n")
        f.write("-" * 25 + "\n")
        
        reason_counts = Counter()
        for candidate in low_motion_candidates:
            for reason in candidate['reasons'].split(';'):
                if reason:
                    reason_counts[reason] += 1
        
        for reason, count in reason_counts.most_common():
            percentage = count / total_videos * 100
            f.write(f"{reason}: {count:,} videos ({percentage:.1f}%)\n")
        
        # Recommendations
        f.write("\n\nRECOMMENDATIONS\n")
        f.write("-" * 15 + "\n")
        f.write("1. IMMEDIATE ACTIONS:\n")
        f.write(f"   - Remove {remove_count:,} videos with multiple quality issues\n")
        f.write(f"   - Review {review_count:,} videos with single quality issues\n")
        f.write(f"   - Keep {total_videos - len(low_motion_candidates):,} videos with good motion patterns\n\n")
        
        f.write("2. THRESHOLD TUNING:\n")
        if df['motion_share'].quantile(0.1) < 0.15:
            f.write("   - Consider lowering motion_share threshold to 0.10 for more lenient filtering\n")
        if fallback_count / total_videos > 0.1:
            f.write("   - High fallback rate suggests motion detection parameters may need adjustment\n")
        
        f.write("\n3. PHRASE-SPECIFIC ISSUES:\n")
        for phrase, stats in phrase_stats.items():
            if stats['fallback_rate'] > 0.2:
                f.write(f"   - '{phrase}': High fallback rate ({stats['fallback_rate']*100:.1f}%) - check video quality\n")
            if stats['low_motion_rate'] > 0.3:
                f.write(f"   - '{phrase}': High low-motion rate ({stats['low_motion_rate']*100:.1f}%) - may need different thresholds\n")


def main():
    parser = argparse.ArgumentParser(description='Analyze Motion Quality Results')
    parser.add_argument('--summary', required=True, help='Input CSV summary file')
    parser.add_argument('--output_candidates', required=True, help='Output CSV for low motion candidates')
    parser.add_argument('--output_summary', required=True, help='Output text summary file')
    parser.add_argument('--motion_threshold', type=float, default=0.15, help='Motion share threshold')
    parser.add_argument('--heatmap_threshold', type=float, default=100, help='Heatmap intensity threshold')
    parser.add_argument('--min_duration_threshold', type=float, default=0.5, help='Minimum motion duration threshold')
    
    args = parser.parse_args()
    
    # Load data
    print(f"Loading data from {args.summary}...")
    df = pd.read_csv(args.summary)
    print(f"Loaded {len(df)} video records")
    
    # Analyze motion patterns
    print("Analyzing motion patterns...")
    categories, low_motion_candidates = analyze_motion_patterns(
        df, args.motion_threshold, args.heatmap_threshold
    )
    
    # Analyze by phrase
    print("Analyzing by phrase...")
    phrase_stats = analyze_by_phrase(df)
    
    # Write low motion candidates CSV
    print(f"Writing low motion candidates to {args.output_candidates}...")
    with open(args.output_candidates, 'w', newline='') as csvfile:
        fieldnames = ['video_path', 'motion_share', 'heatmap_max', 'chose_fallback', 
                     'detection_rate', 'quality_score', 'reasons', 'recommendation']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        # Sort by quality score (worst first)
        low_motion_candidates.sort(key=lambda x: x['quality_score'])
        for candidate in low_motion_candidates:
            writer.writerow(candidate)
    
    # Generate summary report
    print(f"Generating summary report to {args.output_summary}...")
    generate_summary_report(df, categories, low_motion_candidates, phrase_stats, args.output_summary)
    
    # Print summary
    print(f"\nAnalysis complete!")
    print(f"Total videos analyzed: {len(df):,}")
    print(f"Low motion candidates identified: {len(low_motion_candidates):,}")
    print(f"Recommended for removal: {sum(1 for c in low_motion_candidates if c['recommendation'] == 'remove'):,}")
    print(f"Recommended for review: {sum(1 for c in low_motion_candidates if c['recommendation'] == 'review'):,}")
    print(f"Quality distribution:")
    for category, videos in categories.items():
        print(f"  {category.capitalize()}: {len(videos):,} videos")


if __name__ == '__main__':
    main()
