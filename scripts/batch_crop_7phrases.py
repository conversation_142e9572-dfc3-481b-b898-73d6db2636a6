#!/usr/bin/env python3
"""
Batch Crop Top 7 Phrases - Stage 1 Cropping

Recursively finds videos for 7 specific phrases, crops every frame to top-middle third
with downward shift, letterboxes to square, and writes MP4s with multiprocessing.

Usage:
    python -u scripts/batch_crop_7phrases.py \
      --input_dir "~/Desktop/top10dataset6.9.25" \
      --phrases "pillow,phone,i_need_to_move,doctor,glasses,my_mouth_is_dry,help" \
      --output_dir "data/stage1_cropped_top7" \
      --size 96 --shift_y_frac 0.08 \
      --exts ".mp4,.mov,.mkv,.avi,.webm" \
      --workers 4
"""

import argparse
import cv2
import numpy as np
import os
from pathlib import Path
from tqdm import tqdm
import multiprocessing as mp
from typing import List, Tuple, Optional
import logging


def compute_roi_with_shift(width: int, height: int, shift_y_frac: float = 0.08) -> Tuple[int, int, int, int]:
    """Compute ROI coordinates for top-middle third with downward shift."""
    # Base ROI: top-middle third
    x0 = width // 3
    x1 = (2 * width) // 3
    y0 = 0
    y1 = height // 3
    
    # Apply downward shift
    dy = int(height * shift_y_frac)
    y0 += dy
    y1 += dy
    
    # Clamp to image bounds
    x0 = max(0, x0)
    x1 = min(width, x1)
    y0 = max(0, y0)
    y1 = min(height, y1)
    
    return x0, y0, x1, y1


def letterbox_to_square(image: np.ndarray, target_size: int) -> np.ndarray:
    """Letterbox image to square while preserving aspect ratio."""
    h, w = image.shape[:2]
    
    # Scale by longest side to fit within target_size
    scale = target_size / max(h, w)
    new_h, new_w = int(h * scale), int(w * scale)
    
    # Resize
    resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
    
    # Create square canvas with edge pixel padding
    canvas = np.zeros((target_size, target_size, 3), dtype=np.uint8)
    
    # Calculate padding to center the image
    pad_h = (target_size - new_h) // 2
    pad_w = (target_size - new_w) // 2
    
    # Place resized image in center
    canvas[pad_h:pad_h + new_h, pad_w:pad_w + new_w] = resized
    
    # Fill padding with edge pixels
    if pad_h > 0:
        # Top and bottom padding
        canvas[:pad_h, pad_w:pad_w + new_w] = resized[0:1, :]
        canvas[pad_h + new_h:, pad_w:pad_w + new_w] = resized[-1:, :]
    
    if pad_w > 0:
        # Left and right padding
        canvas[:, :pad_w] = canvas[:, pad_w:pad_w + 1]
        canvas[:, pad_w + new_w:] = canvas[:, pad_w + new_w - 1:pad_w + new_w]
    
    return canvas


def find_phrase_videos(input_dir: str, phrases: List[str], extensions: List[str]) -> List[Path]:
    """Find videos matching any of the phrases (case-insensitive)."""
    input_path = Path(input_dir).expanduser()
    videos = []
    
    # Get all video files
    for ext in extensions:
        videos.extend(input_path.rglob(f'*{ext}'))
        videos.extend(input_path.rglob(f'*{ext.upper()}'))
    
    # Filter by phrase match (case-insensitive substring in path)
    phrase_videos = []
    for video in videos:
        video_str = str(video).lower()
        if any(phrase.lower() in video_str for phrase in phrases):
            phrase_videos.append(video)
    
    return sorted(phrase_videos)


def process_single_video(args) -> Optional[str]:
    """Process a single video - designed for multiprocessing."""
    video_path, output_dir, input_dir, size, shift_y_frac = args
    
    try:
        # Open video
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return f"Failed to open: {video_path}"
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0 or np.isnan(fps):
            fps = 25.0  # Fallback
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Handle invalid frame count
        if total_frames <= 0 or total_frames > 1e10:
            total_frames = 1000  # Fallback estimate
        
        if width <= 0 or height <= 0:
            cap.release()
            return f"Invalid dimensions {width}x{height}: {video_path}"
        
        # Compute ROI
        x0, y0, x1, y1 = compute_roi_with_shift(width, height, shift_y_frac)
        
        # Mirror directory structure
        rel_path = video_path.relative_to(input_dir)
        output_video_path = output_dir / rel_path.with_suffix('.mp4')
        output_video_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Setup video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_video_path), fourcc, fps, (size, size))
        
        # Process all frames
        frames_processed = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Crop to ROI
            cropped = frame[y0:y1, x0:x1]
            
            # Letterbox to square
            letterboxed = letterbox_to_square(cropped, size)
            
            # Write frame
            out.write(letterboxed)
            frames_processed += 1
        
        cap.release()
        out.release()
        
        return None  # Success
        
    except Exception as e:
        if 'cap' in locals():
            cap.release()
        if 'out' in locals():
            out.release()
        return f"Exception processing {video_path}: {e}"


def main():
    parser = argparse.ArgumentParser(description='Batch Crop Top 7 Phrases - Stage 1')
    parser.add_argument('--input_dir', required=True, help='Input directory with videos')
    parser.add_argument('--phrases', required=True, help='Comma-separated phrases to match')
    parser.add_argument('--output_dir', required=True, help='Output directory')
    parser.add_argument('--size', type=int, default=96, help='Target crop size (square)')
    parser.add_argument('--shift_y_frac', type=float, default=0.08, help='Downward ROI shift fraction')
    parser.add_argument('--exts', default='.mp4,.mov,.mkv,.avi,.webm', help='Video extensions')
    parser.add_argument('--workers', type=int, default=4, help='Number of worker processes')
    
    args = parser.parse_args()
    
    # Setup paths
    input_dir = Path(args.input_dir).expanduser()
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    reports_dir = Path('reports')
    reports_dir.mkdir(exist_ok=True)
    
    # Parse phrases and extensions
    phrases = [p.strip() for p in args.phrases.split(',')]
    extensions = [e.strip() for e in args.exts.split(',')]
    
    print(f"Scanning for videos matching phrases: {phrases}")
    print(f"Extensions: {extensions}")
    
    # Find matching videos
    videos = find_phrase_videos(str(input_dir), phrases, extensions)
    print(f"Found {len(videos)} videos matching phrases")
    
    if not videos:
        print("No matching videos found!")
        return
    
    # Prepare arguments for multiprocessing
    process_args = [(video, output_dir, input_dir, args.size, args.shift_y_frac) 
                   for video in videos]
    
    # Process videos with multiprocessing
    print(f"Processing {len(videos)} videos with {args.workers} workers...")
    
    skipped = []
    processed_count = 0
    
    with mp.Pool(args.workers) as pool:
        results = list(tqdm(
            pool.imap(process_single_video, process_args),
            total=len(videos),
            desc="Cropping videos"
        ))
    
    # Collect results
    for i, result in enumerate(results):
        if result is None:
            processed_count += 1
        else:
            skipped.append(result)
    
    # Write skips log
    if skipped:
        with open(reports_dir / 'crop_top7_skips.txt', 'w') as f:
            f.write("Skipped videos (errors during processing):\n")
            for skip in skipped:
                f.write(f"{skip}\n")
    
    print(f"\nProcessing complete!")
    print(f"Successfully cropped: {processed_count} videos")
    print(f"Skipped: {len(skipped)} videos")
    print(f"Output directory: {output_dir}")
    if skipped:
        print(f"Skips log: reports/crop_top7_skips.txt")


if __name__ == '__main__':
    main()
