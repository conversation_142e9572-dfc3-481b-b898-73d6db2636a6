#!/usr/bin/env python3
"""
Balanced Dataset Creation Script

Creates a balanced version of the clean dataset through controlled data augmentation
while preserving demographic stratification and maintaining SageMaker compatibility.

Usage:
    python scripts/create_balanced_dataset.py \
      --manifests_dir "datasets/manifests" \
      --source_dataset "datasets/dataset_clean_2025.09.08" \
      --output_dataset "datasets/dataset_clean_balanced_2025.09.08" \
      --plan_file "reports/dataset_balancing_plan.txt"
"""

import argparse
import pandas as pd
import numpy as np
import cv2
import shutil
from pathlib import Path
from collections import defaultdict
import logging
from datetime import datetime
import json
from tqdm import tqdm
from concurrent.futures import ProcessPoolExecutor, as_completed
import random

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoAugmenter:
    """Handles video augmentation with conservative techniques."""
    
    def __init__(self):
        self.techniques = {
            'brightness_adj': self.brightness_adjustment,
            'temporal_jitter': self.temporal_jitter,
            'minor_zoom': self.minor_zoom,
            'gaussian_blur': self.gaussian_blur,
            'horizontal_flip': self.horizontal_flip
        }
    
    def brightness_adjustment(self, frames: np.ndarray, params: dict = None) -> np.ndarray:
        """Apply brightness adjustment (±10-15%)."""
        if params is None:
            brightness_factor = random.uniform(0.85, 1.15)
        else:
            brightness_factor = params.get('brightness_factor', 1.0)
        
        # Apply brightness adjustment
        adjusted = frames.astype(np.float32) * brightness_factor
        adjusted = np.clip(adjusted, 0, 255).astype(np.uint8)
        return adjusted
    
    def temporal_jitter(self, frames: np.ndarray, params: dict = None) -> np.ndarray:
        """Apply temporal jitter (±2-3 frames at start/end)."""
        if len(frames) <= 6:  # Too short for jitter
            return frames
        
        if params is None:
            start_jitter = random.randint(0, 3)
            end_jitter = random.randint(0, 3)
        else:
            start_jitter = params.get('start_jitter', 0)
            end_jitter = params.get('end_jitter', 0)
        
        # Apply jitter by trimming frames
        start_idx = min(start_jitter, len(frames) // 4)
        end_idx = max(len(frames) - end_jitter, 3 * len(frames) // 4)
        
        return frames[start_idx:end_idx]
    
    def minor_zoom(self, frames: np.ndarray, params: dict = None) -> np.ndarray:
        """Apply minor zoom (±5-8%)."""
        if params is None:
            zoom_factor = random.uniform(0.92, 1.08)
        else:
            zoom_factor = params.get('zoom_factor', 1.0)
        
        h, w = frames.shape[1:3]
        new_h, new_w = int(h * zoom_factor), int(w * zoom_factor)
        
        zoomed_frames = []
        for frame in frames:
            # Resize frame
            resized = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
            
            if zoom_factor > 1.0:
                # Crop to original size (zoom in)
                start_y = (new_h - h) // 2
                start_x = (new_w - w) // 2
                cropped = resized[start_y:start_y+h, start_x:start_x+w]
            else:
                # Pad to original size (zoom out)
                pad_y = (h - new_h) // 2
                pad_x = (w - new_w) // 2
                cropped = cv2.copyMakeBorder(resized, pad_y, h-new_h-pad_y, 
                                           pad_x, w-new_w-pad_x, cv2.BORDER_REFLECT)
            
            zoomed_frames.append(cropped)
        
        return np.array(zoomed_frames)
    
    def gaussian_blur(self, frames: np.ndarray, params: dict = None) -> np.ndarray:
        """Apply slight Gaussian blur (σ=0.5-1.0)."""
        if params is None:
            sigma = random.uniform(0.5, 1.0)
        else:
            sigma = params.get('sigma', 0.7)
        
        kernel_size = int(2 * np.ceil(2 * sigma) + 1)
        if kernel_size % 2 == 0:
            kernel_size += 1
        
        blurred_frames = []
        for frame in frames:
            blurred = cv2.GaussianBlur(frame, (kernel_size, kernel_size), sigma)
            blurred_frames.append(blurred)
        
        return np.array(blurred_frames)
    
    def horizontal_flip(self, frames: np.ndarray, params: dict = None) -> np.ndarray:
        """Apply horizontal flip (mirror)."""
        return np.flip(frames, axis=2)  # Flip along width axis
    
    def augment_video(self, video_path: str, technique: str, output_path: str) -> bool:
        """Augment a single video using specified technique."""
        try:
            # Read video
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error(f"Cannot open video: {video_path}")
                return False
            
            frames = []
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            cap.release()
            
            if not frames:
                logger.error(f"No frames in video: {video_path}")
                return False
            
            frames = np.array(frames)
            
            # Apply augmentation
            if technique in self.techniques:
                augmented_frames = self.techniques[technique](frames)
            else:
                logger.error(f"Unknown technique: {technique}")
                return False
            
            # Write augmented video
            if len(augmented_frames) == 0:
                logger.error(f"Augmentation resulted in empty video: {video_path}")
                return False
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS) or 30.0
            h, w = augmented_frames.shape[1:3]
            
            # Create output directory
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Write video
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(output_path, fourcc, fps, (w, h))
            
            for frame in augmented_frames:
                out.write(frame)
            
            out.release()
            return True
            
        except Exception as e:
            logger.error(f"Error augmenting {video_path}: {e}")
            return False

def load_augmentation_plan(manifests_dir: Path) -> dict:
    """Load manifests and calculate augmentation requirements."""
    plan = {}
    
    for split in ['train', 'val', 'test']:
        manifest_path = manifests_dir / f"manifest_clean_{split}.csv"
        if not manifest_path.exists():
            continue
            
        df = pd.read_csv(manifest_path)
        phrase_counts = df['phrase'].value_counts()
        target_count = phrase_counts.max()
        
        plan[split] = {}
        for phrase in phrase_counts.index:
            current_count = phrase_counts[phrase]
            required_augs = target_count - current_count
            
            if required_augs > 0:
                phrase_df = df[df['phrase'] == phrase]
                plan[split][phrase] = {
                    'target_count': target_count,
                    'current_count': current_count,
                    'required_augmentations': required_augs,
                    'videos': phrase_df.to_dict('records')
                }
    
    return plan

def select_videos_for_augmentation(videos: list, required_augs: int) -> list:
    """Select videos for augmentation maintaining demographic balance."""
    if required_augs <= len(videos):
        # Simple case: select random subset
        return random.sample(videos, required_augs)
    
    # Need to augment some videos multiple times
    augmentation_list = []
    videos_cycle = videos.copy()
    random.shuffle(videos_cycle)
    
    for i in range(required_augs):
        video = videos_cycle[i % len(videos_cycle)]
        aug_count = (i // len(videos_cycle)) + 1
        augmentation_list.append({
            'video': video,
            'aug_suffix': f'aug{aug_count}',
            'technique': 'brightness_adj'  # Primary technique
        })
    
    return augmentation_list

def process_augmentation_task(task):
    """Process a single augmentation task."""
    video_info, source_dataset, output_dataset, aug_suffix, technique = task
    
    # Construct paths
    source_path = Path(source_dataset) / video_info['split'] / Path(video_info['video_path']).relative_to(Path(video_info['video_path']).parts[0])
    
    # Create augmented filename
    original_path = Path(video_info['video_path'])
    aug_filename = original_path.stem + f'_{aug_suffix}.mp4'
    
    output_path = Path(output_dataset) / video_info['split'] / Path(video_info['video_path']).relative_to(Path(video_info['video_path']).parts[0]).parent / aug_filename
    
    # Augment video
    augmenter = VideoAugmenter()
    success = augmenter.augment_video(str(source_path), technique, str(output_path))
    
    if success:
        # Create manifest entry for augmented video
        aug_entry = video_info.copy()
        aug_entry['video_path'] = str(output_path.relative_to(output_dataset))
        aug_entry['filename'] = aug_filename
        aug_entry['augmentation_type'] = technique
        return aug_entry
    else:
        return None

def copy_original_videos(source_dataset: Path, output_dataset: Path, manifests: dict) -> dict:
    """Copy all original videos to the balanced dataset."""
    stats = {'copied': 0, 'failed': 0}
    
    logger.info("📁 Copying original videos...")
    
    for split, df in manifests.items():
        for _, row in tqdm(df.iterrows(), total=len(df), desc=f"Copying {split} videos"):
            source_path = source_dataset / split / Path(row['video_path']).relative_to(Path(row['video_path']).parts[0])
            output_path = output_dataset / split / Path(row['video_path']).relative_to(Path(row['video_path']).parts[0])
            
            # Create output directory
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            try:
                if source_path.exists():
                    shutil.copy2(source_path, output_path)
                    stats['copied'] += 1
                else:
                    logger.warning(f"Source not found: {source_path}")
                    stats['failed'] += 1
            except Exception as e:
                logger.error(f"Failed to copy {source_path}: {e}")
                stats['failed'] += 1
    
    return stats

def create_balanced_manifests(original_manifests: dict, augmented_entries: dict, output_dir: Path) -> None:
    """Create balanced manifests including original and augmented videos."""

    for split, df in original_manifests.items():
        # Add augmentation_type column to original entries
        df_copy = df.copy()
        df_copy['augmentation_type'] = 'original'

        # Add augmented entries
        if split in augmented_entries and augmented_entries[split]:
            aug_df = pd.DataFrame(augmented_entries[split])
            balanced_df = pd.concat([df_copy, aug_df], ignore_index=True)
        else:
            balanced_df = df_copy

        # Sort by phrase and filename for consistency
        balanced_df = balanced_df.sort_values(['phrase', 'filename'])

        # Save balanced manifest
        output_path = output_dir / f"manifest_clean_balanced_{split}.csv"
        balanced_df.to_csv(output_path, index=False)

        logger.info(f"✅ Created balanced {split} manifest: {len(balanced_df)} samples")

def create_dataset_metadata(output_dataset: Path, original_stats: dict, aug_stats: dict) -> None:
    """Create metadata for the balanced dataset."""

    metadata = {
        'dataset_name': f"dataset_clean_balanced_{datetime.now().strftime('%Y.%m.%d')}",
        'dataset_type': 'clean_balanced',
        'creation_date': datetime.now().isoformat(),
        'version': '1.0',
        'description': 'ICU Lipreading clean dataset with balanced phrase distribution through controlled augmentation',
        'original_videos': original_stats['copied'],
        'augmented_videos': aug_stats['created'],
        'total_videos': original_stats['copied'] + aug_stats['created'],
        'failed_copies': original_stats['failed'],
        'failed_augmentations': aug_stats['failed'],
        'phrases': ['pillow', 'phone', 'doctor', 'glasses', 'help', 'i_need_to_move', 'my_mouth_is_dry'],
        'num_classes': 7,
        'splits': ['train', 'val', 'test'],
        'split_ratios': {'train': 0.7, 'val': 0.15, 'test': 0.15},
        'demographics': {
            'age_groups': ['18to39', '40to64', '65plus'],
            'genders': ['male', 'female'],
            'ethnicities': ['caucasian', 'asian', 'aboriginal', 'not_specified']
        },
        'augmentation_policy': {
            'strategy': 'phrase_balancing',
            'target': 'highest_count_per_split',
            'techniques': ['brightness_adj', 'temporal_jitter', 'minor_zoom', 'gaussian_blur', 'horizontal_flip'],
            'primary_technique': 'brightness_adj',
            'demographic_preservation': 'proportional',
            'expansion_factor': (original_stats['copied'] + aug_stats['created']) / original_stats['copied'] if original_stats['copied'] > 0 else 1.0
        }
    }

    with open(output_dataset / 'dataset_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)

def main():
    parser = argparse.ArgumentParser(description='Create balanced dataset through controlled augmentation')
    parser.add_argument('--manifests_dir', default='datasets/manifests',
                       help='Directory containing manifest files')
    parser.add_argument('--source_dataset', default='datasets/dataset_clean_2025.09.08',
                       help='Source clean dataset directory')
    parser.add_argument('--output_dataset', default='datasets/dataset_clean_balanced_2025.09.08',
                       help='Output balanced dataset directory')
    parser.add_argument('--plan_file', default='reports/dataset_balancing_plan.txt',
                       help='Augmentation plan file')
    parser.add_argument('--workers', type=int, default=4,
                       help='Number of worker processes for augmentation')

    args = parser.parse_args()

    logger.info("🚀 Starting balanced dataset creation...")

    # Setup paths
    manifests_dir = Path(args.manifests_dir)
    source_dataset = Path(args.source_dataset)
    output_dataset = Path(args.output_dataset)

    # Create output directory
    output_dataset.mkdir(parents=True, exist_ok=True)

    # Load manifests
    logger.info("📊 Loading manifests...")
    manifests = {}
    for split in ['train', 'val', 'test']:
        manifest_path = manifests_dir / f"manifest_clean_{split}.csv"
        if manifest_path.exists():
            manifests[split] = pd.read_csv(manifest_path)
            logger.info(f"Loaded {split}: {len(manifests[split])} samples")

    # Copy original videos
    logger.info("📁 Copying original videos...")
    copy_stats = copy_original_videos(source_dataset, output_dataset, manifests)
    logger.info(f"Copied {copy_stats['copied']} videos, {copy_stats['failed']} failed")

    # Load augmentation plan
    logger.info("📋 Loading augmentation plan...")
    aug_plan = load_augmentation_plan(manifests_dir)

    # Prepare augmentation tasks
    logger.info("🎯 Preparing augmentation tasks...")
    augmentation_tasks = []
    total_augmentations = 0

    for split, split_plan in aug_plan.items():
        for phrase, phrase_plan in split_plan.items():
            required_augs = phrase_plan['required_augmentations']
            videos = phrase_plan['videos']

            # Select videos for augmentation
            aug_selections = select_videos_for_augmentation(videos, required_augs)

            for selection in aug_selections:
                task = (
                    selection['video'],
                    source_dataset,
                    output_dataset,
                    selection['aug_suffix'],
                    selection['technique']
                )
                augmentation_tasks.append(task)
                total_augmentations += 1

    logger.info(f"Prepared {total_augmentations} augmentation tasks")

    # Process augmentations
    logger.info("🔄 Processing augmentations...")
    augmented_entries = defaultdict(list)
    aug_stats = {'created': 0, 'failed': 0}

    with ProcessPoolExecutor(max_workers=args.workers) as executor:
        future_to_task = {executor.submit(process_augmentation_task, task): task for task in augmentation_tasks}

        for future in tqdm(as_completed(future_to_task), total=len(augmentation_tasks), desc="Augmenting videos"):
            try:
                result = future.result()
                if result:
                    split = result['split']
                    augmented_entries[split].append(result)
                    aug_stats['created'] += 1
                else:
                    aug_stats['failed'] += 1
            except Exception as e:
                logger.error(f"Augmentation task failed: {e}")
                aug_stats['failed'] += 1

    # Create balanced manifests
    logger.info("📝 Creating balanced manifests...")
    create_balanced_manifests(manifests, augmented_entries, manifests_dir)

    # Create dataset metadata
    logger.info("📋 Creating dataset metadata...")
    create_dataset_metadata(output_dataset, copy_stats, aug_stats)

    # Generate summary report
    logger.info("📊 Generating summary report...")
    total_original = sum(len(df) for df in manifests.values())
    total_balanced = copy_stats['copied'] + aug_stats['created']

    summary_report = f"""BALANCED DATASET CREATION SUMMARY
{'='*50}
Creation Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

DATASET STATISTICS:
  Original videos: {total_original:,}
  Augmented videos: {aug_stats['created']:,}
  Total balanced videos: {total_balanced:,}
  Expansion factor: {total_balanced/total_original:.2f}x

PROCESSING RESULTS:
  Videos copied: {copy_stats['copied']:,}
  Copy failures: {copy_stats['failed']:,}
  Augmentations created: {aug_stats['created']:,}
  Augmentation failures: {aug_stats['failed']:,}

BALANCED DISTRIBUTION:
"""

    # Add phrase counts for each split
    for split in ['train', 'val', 'test']:
        if split in manifests:
            phrase_counts = manifests[split]['phrase'].value_counts()
            target_count = phrase_counts.max()
            summary_report += f"\n{split.upper()} Split (target: {target_count} per phrase):\n"
            for phrase in sorted(phrase_counts.index):
                current = phrase_counts[phrase]
                needed = target_count - current
                summary_report += f"  {phrase}: {current} + {needed} = {target_count}\n"

    summary_report += f"""
OUTPUT STRUCTURE:
  Dataset: {output_dataset}
  Manifests: {manifests_dir}/manifest_clean_balanced_*.csv
  Metadata: {output_dataset}/dataset_metadata.json

SUCCESS: Balanced dataset ready for SageMaker training!
"""

    # Save summary report
    summary_path = Path("reports/balanced_dataset_creation_summary.txt")
    summary_path.parent.mkdir(parents=True, exist_ok=True)
    with open(summary_path, 'w') as f:
        f.write(summary_report)

    # Final summary
    logger.info("=" * 60)
    logger.info("✅ BALANCED DATASET CREATION COMPLETE")
    logger.info("=" * 60)
    logger.info(f"📁 Balanced dataset: {output_dataset}")
    logger.info(f"📊 Original videos: {total_original:,}")
    logger.info(f"📊 Augmented videos: {aug_stats['created']:,}")
    logger.info(f"📊 Total videos: {total_balanced:,}")
    logger.info(f"📊 Expansion factor: {total_balanced/total_original:.2f}x")
    logger.info(f"📝 Summary report: {summary_path}")
    logger.info("🚀 Ready for SageMaker training!")

    return 0

if __name__ == "__main__":
    exit(main())
