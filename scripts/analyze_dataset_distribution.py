#!/usr/bin/env python3
"""
Dataset Distribution Analysis Script

Analyzes the current clean dataset distribution and creates a detailed augmentation plan
to balance phrase counts while preserving demographic stratification.

Usage:
    python scripts/analyze_dataset_distribution.py \
      --manifests_dir "datasets/manifests" \
      --output_report "reports/dataset_balancing_plan.txt"
"""

import argparse
import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict, Counter
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_manifests(manifests_dir: Path) -> dict:
    """Load all clean dataset manifests."""
    manifests = {}
    
    for split in ['train', 'val', 'test']:
        manifest_path = manifests_dir / f"manifest_clean_{split}.csv"
        if manifest_path.exists():
            df = pd.read_csv(manifest_path)
            manifests[split] = df
            logger.info(f"Loaded {split} manifest: {len(df)} samples")
        else:
            logger.error(f"Manifest not found: {manifest_path}")
            manifests[split] = pd.DataFrame()
    
    return manifests

def analyze_phrase_distribution(manifests: dict) -> dict:
    """Analyze phrase distribution across all splits."""
    analysis = {}
    
    for split, df in manifests.items():
        if len(df) == 0:
            continue
            
        phrase_counts = df['phrase'].value_counts().sort_index()
        analysis[split] = {
            'phrase_counts': phrase_counts.to_dict(),
            'total_samples': len(df),
            'max_count': phrase_counts.max(),
            'min_count': phrase_counts.min(),
            'target_count': phrase_counts.max()  # Balance to highest count
        }
        
        logger.info(f"{split.upper()} split analysis:")
        logger.info(f"  Total samples: {len(df)}")
        logger.info(f"  Phrase range: {phrase_counts.min()} - {phrase_counts.max()}")
        for phrase, count in phrase_counts.items():
            logger.info(f"    {phrase}: {count}")
    
    return analysis

def analyze_demographic_distribution(manifests: dict) -> dict:
    """Analyze demographic distribution for each phrase in each split."""
    demographic_analysis = {}
    
    for split, df in manifests.items():
        if len(df) == 0:
            continue
            
        demographic_analysis[split] = {}
        
        for phrase in df['phrase'].unique():
            phrase_df = df[df['phrase'] == phrase]
            
            # Analyze demographic combinations
            demo_combinations = phrase_df.groupby(['age_group', 'gender', 'ethnicity']).size()
            
            demographic_analysis[split][phrase] = {
                'total_count': len(phrase_df),
                'demographic_combinations': demo_combinations.to_dict(),
                'age_groups': phrase_df['age_group'].value_counts().to_dict(),
                'genders': phrase_df['gender'].value_counts().to_dict(),
                'ethnicities': phrase_df['ethnicity'].value_counts().to_dict()
            }
    
    return demographic_analysis

def calculate_augmentation_requirements(analysis: dict) -> dict:
    """Calculate required augmentations for each phrase in each split."""
    augmentation_plan = {}
    
    for split, split_analysis in analysis.items():
        target_count = split_analysis['target_count']
        current_counts = split_analysis['phrase_counts']
        
        augmentation_plan[split] = {}
        
        for phrase, current_count in current_counts.items():
            required_augmentations = target_count - current_count
            augmentation_multiplier = target_count / current_count if current_count > 0 else 0
            
            augmentation_plan[split][phrase] = {
                'current_count': current_count,
                'target_count': target_count,
                'required_augmentations': required_augmentations,
                'augmentation_multiplier': round(augmentation_multiplier, 2),
                'needs_augmentation': required_augmentations > 0,
                'problematic': augmentation_multiplier > 3.0  # Flag if >3x augmentation needed
            }
    
    return augmentation_plan

def generate_augmentation_strategy(augmentation_plan: dict, demographic_analysis: dict) -> dict:
    """Generate detailed augmentation strategy preserving demographics."""
    strategy = {}
    
    # Define augmentation techniques in order of preference
    augmentation_techniques = [
        'brightness_adj',
        'temporal_jitter', 
        'minor_zoom',
        'gaussian_blur',
        'horizontal_flip'
    ]
    
    for split, split_plan in augmentation_plan.items():
        strategy[split] = {}
        
        for phrase, phrase_plan in split_plan.items():
            if not phrase_plan['needs_augmentation']:
                strategy[split][phrase] = {
                    'action': 'no_augmentation_needed',
                    'techniques': [],
                    'demographic_preservation': 'original_only'
                }
                continue
            
            required_augs = phrase_plan['required_augmentations']
            current_count = phrase_plan['current_count']
            
            # Determine which techniques to use
            techniques_needed = min(len(augmentation_techniques), 
                                  int(np.ceil(required_augs / current_count)))
            
            selected_techniques = augmentation_techniques[:techniques_needed]
            
            # Calculate how many augmentations per technique
            augs_per_technique = required_augs // len(selected_techniques)
            remaining_augs = required_augs % len(selected_techniques)
            
            technique_distribution = {}
            for i, technique in enumerate(selected_techniques):
                count = augs_per_technique
                if i < remaining_augs:  # Distribute remainder
                    count += 1
                technique_distribution[technique] = count
            
            # Get demographic info for this phrase
            demo_info = demographic_analysis[split][phrase]
            
            strategy[split][phrase] = {
                'action': 'augment',
                'required_augmentations': required_augs,
                'techniques': selected_techniques,
                'technique_distribution': technique_distribution,
                'demographic_combinations': len(demo_info['demographic_combinations']),
                'demographic_preservation': 'proportional',
                'problematic': phrase_plan['problematic']
            }
    
    return strategy

def generate_detailed_report(analysis: dict, augmentation_plan: dict, strategy: dict, 
                           demographic_analysis: dict) -> str:
    """Generate comprehensive analysis report."""
    report = []
    report.append("DATASET BALANCING ANALYSIS REPORT")
    report.append("=" * 50)
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # Current distribution summary
    report.append("CURRENT DISTRIBUTION SUMMARY")
    report.append("-" * 30)
    total_original = 0
    total_target = 0
    
    for split, split_analysis in analysis.items():
        report.append(f"\n{split.upper()} Split:")
        report.append(f"  Current total: {split_analysis['total_samples']:,}")
        report.append(f"  Target per phrase: {split_analysis['target_count']}")
        report.append(f"  Phrase distribution:")
        
        for phrase, count in split_analysis['phrase_counts'].items():
            target = split_analysis['target_count']
            report.append(f"    {phrase}: {count} → {target} (+{target-count})")
        
        total_original += split_analysis['total_samples']
        total_target += split_analysis['target_count'] * len(split_analysis['phrase_counts'])
    
    report.append(f"\nOVERALL SUMMARY:")
    report.append(f"  Original dataset: {total_original:,} videos")
    report.append(f"  Balanced dataset: {total_target:,} videos")
    report.append(f"  Total augmentations needed: {total_target - total_original:,}")
    report.append(f"  Expansion factor: {total_target/total_original:.2f}x")
    
    # Augmentation strategy
    report.append("\n\nAUGMENTATION STRATEGY")
    report.append("-" * 25)
    
    problematic_phrases = []
    
    for split, split_strategy in strategy.items():
        report.append(f"\n{split.upper()} Split Strategy:")
        
        for phrase, phrase_strategy in split_strategy.items():
            if phrase_strategy['action'] == 'no_augmentation_needed':
                report.append(f"  {phrase}: No augmentation needed")
            else:
                req_augs = phrase_strategy['required_augmentations']
                techniques = phrase_strategy['techniques']
                report.append(f"  {phrase}: +{req_augs} augmentations using {len(techniques)} techniques")
                
                for technique, count in phrase_strategy['technique_distribution'].items():
                    report.append(f"    - {technique}: {count} augmentations")
                
                if phrase_strategy['problematic']:
                    problematic_phrases.append(f"{split}/{phrase}")
                    report.append(f"    ⚠️  WARNING: >3x augmentation required")
    
    # Problematic phrases warning
    if problematic_phrases:
        report.append("\n\nPROBLEMATIC PHRASES (>3x augmentation)")
        report.append("-" * 40)
        for phrase in problematic_phrases:
            report.append(f"  ⚠️  {phrase}")
        report.append("\nConsider manual review of these phrases for quality concerns.")
    
    # Demographic preservation
    report.append("\n\nDEMOGRAPHIC PRESERVATION STRATEGY")
    report.append("-" * 35)
    report.append("Augmentation will maintain proportional demographic representation:")
    report.append("- Age groups: Proportional to original distribution")
    report.append("- Gender: Proportional to original distribution") 
    report.append("- Ethnicity: Proportional to original distribution")
    report.append("- Combinations: All existing combinations preserved")
    
    # Technical details
    report.append("\n\nAUGMENTATION TECHNIQUES")
    report.append("-" * 25)
    report.append("1. brightness_adj: ±10-15% brightness adjustment")
    report.append("2. temporal_jitter: ±2-3 frames at start/end")
    report.append("3. minor_zoom: ±5-8% zoom factor")
    report.append("4. gaussian_blur: σ=0.5-1.0 blur")
    report.append("5. horizontal_flip: Mirror flip (if appropriate)")
    
    return "\n".join(report)

def main():
    parser = argparse.ArgumentParser(description='Analyze dataset distribution for balancing')
    parser.add_argument('--manifests_dir', default='datasets/manifests',
                       help='Directory containing manifest files')
    parser.add_argument('--output_report', default='reports/dataset_balancing_plan.txt',
                       help='Output report file path')
    
    args = parser.parse_args()
    
    logger.info("🔍 Starting dataset distribution analysis...")
    
    # Load manifests
    manifests_dir = Path(args.manifests_dir)
    manifests = load_manifests(manifests_dir)
    
    if not any(len(df) > 0 for df in manifests.values()):
        logger.error("No valid manifests found!")
        return 1
    
    # Analyze distributions
    logger.info("📊 Analyzing phrase distributions...")
    phrase_analysis = analyze_phrase_distribution(manifests)
    
    logger.info("👥 Analyzing demographic distributions...")
    demographic_analysis = analyze_demographic_distribution(manifests)
    
    # Calculate augmentation requirements
    logger.info("🎯 Calculating augmentation requirements...")
    augmentation_plan = calculate_augmentation_requirements(phrase_analysis)
    
    # Generate strategy
    logger.info("📋 Generating augmentation strategy...")
    strategy = generate_augmentation_strategy(augmentation_plan, demographic_analysis)
    
    # Generate report
    logger.info("📝 Generating detailed report...")
    report = generate_detailed_report(phrase_analysis, augmentation_plan, strategy, demographic_analysis)
    
    # Save report
    output_path = Path(args.output_report)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w') as f:
        f.write(report)
    
    # Save detailed data as JSON
    detailed_data = {
        'analysis_date': datetime.now().isoformat(),
        'phrase_analysis': phrase_analysis,
        'demographic_analysis': {k: {phrase: {**data, 'demographic_combinations': dict(data['demographic_combinations'])} 
                                   for phrase, data in v.items()} for k, v in demographic_analysis.items()},
        'augmentation_plan': augmentation_plan,
        'strategy': strategy
    }
    
    json_path = output_path.with_suffix('.json')
    with open(json_path, 'w') as f:
        json.dump(detailed_data, f, indent=2)
    
    logger.info("=" * 60)
    logger.info("✅ ANALYSIS COMPLETE")
    logger.info("=" * 60)
    logger.info(f"📄 Report saved: {output_path}")
    logger.info(f"📊 Detailed data: {json_path}")
    
    # Summary statistics
    total_original = sum(analysis['total_samples'] for analysis in phrase_analysis.values())
    total_target = sum(analysis['target_count'] * len(analysis['phrase_counts']) 
                      for analysis in phrase_analysis.values())
    
    logger.info(f"📈 Original videos: {total_original:,}")
    logger.info(f"📈 Target videos: {total_target:,}")
    logger.info(f"📈 Augmentations needed: {total_target - total_original:,}")
    logger.info(f"📈 Expansion factor: {total_target/total_original:.2f}x")
    
    return 0

if __name__ == "__main__":
    exit(main())
