#!/usr/bin/env python3
"""
Quick Crop 50 - Fast Stage 1 Sanity Check

A lightweight script to quickly test ROI cropping on a small sample of videos.
No heavy dependencies (MediaPipe/YOLO/SAM) - just OpenCV for fast validation.

Usage:
    python scripts/quick_crop50.py --input_dir /path/to/videos --output_dir data/quick_cropped
"""

import argparse
import cv2
import numpy as np
import os
import random
from pathlib import Path
from tqdm import tqdm
import json
import base64
from typing import List, Tuple, Optional


def compute_roi_with_shift(width: int, height: int, shift_y_frac: float = 0.08) -> Tuple[int, int, int, int]:
    """
    Compute ROI coordinates for top-middle third with downward shift.
    
    Args:
        width: Frame width
        height: Frame height
        shift_y_frac: Fraction of height to shift ROI downward
        
    Returns:
        (x0, y0, x1, y1) ROI coordinates
    """
    # Base ROI: top-middle third
    x0 = width // 3
    x1 = (2 * width) // 3
    y0 = 0
    y1 = height // 3
    
    # Apply downward shift
    dy = int(height * shift_y_frac)
    y0 += dy
    y1 += dy
    
    # Clamp to image bounds
    x0 = max(0, x0)
    x1 = min(width, x1)
    y0 = max(0, y0)
    y1 = min(height, y1)
    
    return (x0, y0, x1, y1)


def letterbox_crop(frame: np.ndarray, roi: Tuple[int, int, int, int], target_size: int) -> np.ndarray:
    """
    Crop ROI and letterbox to square while preserving aspect ratio.
    
    Args:
        frame: Input frame
        roi: ROI coordinates (x0, y0, x1, y1)
        target_size: Target square size
        
    Returns:
        Letterboxed square frame
    """
    x0, y0, x1, y1 = roi
    
    # Extract ROI
    cropped = frame[y0:y1, x0:x1]
    
    if cropped.size == 0:
        return np.zeros((target_size, target_size, 3), dtype=np.uint8)
    
    h, w = cropped.shape[:2]
    
    # Scale to fit target size (preserve aspect ratio)
    scale = target_size / max(h, w)
    new_w = int(w * scale)
    new_h = int(h * scale)
    
    # Resize
    resized = cv2.resize(cropped, (new_w, new_h))
    
    # Create square canvas and center the resized image
    canvas = np.zeros((target_size, target_size, 3), dtype=np.uint8)
    
    # Calculate padding
    pad_x = (target_size - new_w) // 2
    pad_y = (target_size - new_h) // 2
    
    # Place resized image in center
    canvas[pad_y:pad_y + new_h, pad_x:pad_x + new_w] = resized
    
    # Fill padding with edge pixels
    if pad_y > 0:  # Top padding
        canvas[:pad_y, :] = canvas[pad_y, :]
    if pad_y + new_h < target_size:  # Bottom padding
        canvas[pad_y + new_h:, :] = canvas[pad_y + new_h - 1, :]
    if pad_x > 0:  # Left padding
        canvas[:, :pad_x] = canvas[:, pad_x:pad_x + 1]
    if pad_x + new_w < target_size:  # Right padding
        canvas[:, pad_x + new_w:] = canvas[:, pad_x + new_w - 1:pad_x + new_w]
    
    return canvas


def find_videos(input_dir: str, extensions: List[str] = None) -> List[Path]:
    """Find all video files in directory recursively."""
    if extensions is None:
        extensions = ['.mp4', '.mov', '.avi', '.mkv']
    
    input_path = Path(input_dir)
    videos = []
    
    for ext in extensions:
        videos.extend(input_path.rglob(f"*{ext}"))
        videos.extend(input_path.rglob(f"*{ext.upper()}"))
    
    return videos


def extract_sample_frames(video_path: str, n_frames: int = 3) -> List[np.ndarray]:
    """Extract evenly spaced sample frames from video."""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return []
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total_frames <= n_frames:
        frame_indices = list(range(total_frames))
    else:
        frame_indices = np.linspace(0, total_frames - 1, n_frames, dtype=int)
    
    frames = []
    for frame_idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if ret:
            frames.append(frame)
    
    cap.release()
    return frames


def frame_to_base64(frame: np.ndarray, max_size: int = 150) -> str:
    """Convert frame to base64 for HTML embedding."""
    h, w = frame.shape[:2]
    scale = max_size / max(h, w)
    if scale < 1:
        new_w = int(w * scale)
        new_h = int(h * scale)
        frame = cv2.resize(frame, (new_w, new_h))
    
    _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
    img_base64 = base64.b64encode(buffer).decode('utf-8')
    return f"data:image/jpeg;base64,{img_base64}"


def draw_roi_overlay(frame: np.ndarray, roi: Tuple[int, int, int, int]) -> np.ndarray:
    """Draw ROI rectangle on frame for visualization."""
    frame_copy = frame.copy()
    x0, y0, x1, y1 = roi

    # Draw ROI rectangle
    cv2.rectangle(frame_copy, (x0, y0), (x1, y1), (0, 255, 0), 2)

    # Add ROI label
    cv2.putText(frame_copy, 'ROI', (x0, y0 - 10),
                cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    return frame_copy


def process_video(video_path: Path, input_dir: Path, output_dir: Path,
                 target_size: int, shift_y_frac: float, frames_per_video: int) -> Optional[dict]:
    """Process a single video through ROI cropping."""

    try:
        # Calculate relative path and output path
        rel_path = video_path.relative_to(input_dir)
        output_path = output_dir / rel_path.with_suffix('.mp4')
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Open input video
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return None

        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0 or fps > 1000:  # Fallback for invalid FPS
            fps = 25.0

        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        if width <= 0 or height <= 0:
            cap.release()
            return None

        # Compute ROI
        roi = compute_roi_with_shift(width, height, shift_y_frac)

        # Setup output video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (target_size, target_size))

        # Process all frames and collect samples
        frame_count = 0
        original_samples = []
        cropped_samples = []
        sample_indices = np.linspace(0, max(1, total_frames - 1), frames_per_video, dtype=int) if total_frames > 0 else [0]

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # Crop and letterbox
            processed_frame = letterbox_crop(frame, roi, target_size)
            out.write(processed_frame)

            # Save sample frames for gallery (both original with ROI and cropped)
            if frame_count in sample_indices:
                # Original frame with ROI overlay
                original_with_roi = draw_roi_overlay(frame, roi)
                original_samples.append(original_with_roi)

                # Cropped result
                cropped_samples.append(processed_frame.copy())

            frame_count += 1

        cap.release()
        out.release()

        # Convert sample frames to base64
        original_thumbs = [frame_to_base64(frame, 120) for frame in original_samples]
        cropped_thumbs = [frame_to_base64(frame, 120) for frame in cropped_samples]

        return {
            'video_path': str(video_path),
            'output_path': str(output_path),
            'rel_path': str(rel_path),
            'frame_count': frame_count,
            'original_size': (width, height),
            'roi': roi,
            'original_thumbs': original_thumbs,
            'cropped_thumbs': cropped_thumbs,
            'success': True
        }

    except Exception as e:
        return {
            'video_path': str(video_path),
            'error': str(e),
            'success': False
        }


def generate_html_gallery(results: List[dict], output_path: str):
    """Generate minimal HTML gallery."""
    
    successful = [r for r in results if r.get('success', False)]
    failed = [r for r in results if not r.get('success', False)]
    
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Quick Crop 50 - Visual Lip Cropping Gallery</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }}
        .header {{ background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #2196f3; }}
        .header h1 {{ color: #1565c0; margin: 0 0 10px 0; }}
        .header p {{ color: #424242; margin: 0; }}
        .stats {{ display: flex; gap: 20px; margin-bottom: 30px; }}
        .stat-box {{ background: #fff; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        .stat-box h3 {{ margin: 0 0 5px 0; font-size: 24px; color: #1976d2; }}
        .stat-box p {{ margin: 0; color: #666; font-size: 14px; }}
        .gallery {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(500px, 1fr)); gap: 20px; }}
        .video-item {{ background: #fff; border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }}
        .video-header {{ font-weight: bold; margin-bottom: 15px; font-size: 16px; color: #333; }}
        .comparison {{ display: flex; gap: 20px; align-items: flex-start; }}
        .frame-section {{ flex: 1; }}
        .frame-section h4 {{ margin: 0 0 10px 0; font-size: 14px; color: #666; text-align: center; }}
        .frame-row {{ display: flex; gap: 8px; justify-content: center; }}
        .frame-row img {{ width: 100px; height: 100px; object-fit: cover; border: 2px solid #ddd; border-radius: 4px; }}
        .original-frames img {{ border-color: #4caf50; }}
        .cropped-frames img {{ border-color: #ff9800; }}
        .video-info {{ margin-top: 15px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-size: 12px; color: #666; }}
        .failures {{ margin-top: 30px; background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; }}
        .roi-info {{ background: #e8f5e8; padding: 8px; border-radius: 4px; margin-top: 10px; font-size: 11px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎬 Quick Crop 50 - Visual Lip Cropping Gallery</h1>
        <p>Side-by-side comparison: Original frames with ROI overlay → Final cropped lip regions</p>
    </div>

    <div class="stats">
        <div class="stat-box">
            <h3>{len(successful)}</h3>
            <p>Successful</p>
        </div>
        <div class="stat-box">
            <h3>{len(failed)}</h3>
            <p>Failed</p>
        </div>
        <div class="stat-box">
            <h3>{len(results)}</h3>
            <p>Total Processed</p>
        </div>
        <div class="stat-box">
            <h3>{len(successful)/len(results)*100:.1f}%</h3>
            <p>Success Rate</p>
        </div>
    </div>

    <div class="gallery">
"""
    
    for result in successful:
        rel_path = result['rel_path']
        frame_count = result['frame_count']
        original_size = result['original_size']
        roi = result['roi']

        html_content += f"""
        <div class="video-item">
            <div class="video-header">📁 {rel_path}</div>

            <div class="comparison">
                <div class="frame-section">
                    <h4>🎯 Original + ROI Overlay</h4>
                    <div class="frame-row original-frames">
"""

        for thumb in result.get('original_thumbs', []):
            html_content += f'                        <img src="{thumb}" alt="Original with ROI" title="Green box shows ROI region">\n'

        html_content += f"""
                    </div>
                </div>

                <div class="frame-section">
                    <h4>👄 Final Cropped Lip Region</h4>
                    <div class="frame-row cropped-frames">
"""

        for thumb in result.get('cropped_thumbs', []):
            html_content += f'                        <img src="{thumb}" alt="Cropped lip region" title="96x96 cropped result">\n'

        html_content += f"""
                    </div>
                </div>
            </div>

            <div class="video-info">
                📊 <strong>Stats:</strong> {frame_count} frames | {original_size[0]}×{original_size[1]} → 96×96
                <div class="roi-info">
                    🎯 <strong>ROI:</strong> x={roi[0]}-{roi[2]}, y={roi[1]}-{roi[3]} | Size: {roi[2]-roi[0]}×{roi[3]-roi[1]}px
                </div>
            </div>
        </div>
"""
    
    html_content += """
    </div>
"""
    
    if failed:
        html_content += f"""
    <div class="failures">
        <h3>Processing Failures ({len(failed)})</h3>
        <ul>
"""
        for failure in failed:
            html_content += f"            <li><strong>{Path(failure['video_path']).name}</strong>: {failure.get('error', 'Unknown error')}</li>\n"
        
        html_content += """
        </ul>
    </div>
"""
    
    html_content += """
</body>
</html>
"""
    
    with open(output_path, 'w') as f:
        f.write(html_content)


def main():
    parser = argparse.ArgumentParser(description='Quick Crop 50 - Fast Stage 1 Sanity Check')
    parser.add_argument('--input_dir', required=True, help='Input directory with videos')
    parser.add_argument('--output_dir', default='data/quick_stage1_cropped', help='Output directory')
    parser.add_argument('--sample', type=int, default=50, help='Number of videos to sample')
    parser.add_argument('--size', type=int, default=96, help='Target crop size (square)')
    parser.add_argument('--shift_y_frac', type=float, default=0.08, help='Downward ROI shift fraction')
    parser.add_argument('--frames_per_video', type=int, default=3, help='Sample frames for gallery')
    parser.add_argument('--seed', type=int, default=42, help='Random seed for sampling')
    
    args = parser.parse_args()
    
    # Set random seed
    random.seed(args.seed)
    np.random.seed(args.seed)
    
    print(f"🎬 Quick Crop 50 - Sampling {args.sample} videos from {args.input_dir}")
    
    # Find all videos
    all_videos = find_videos(args.input_dir)
    print(f"📁 Found {len(all_videos)} total videos")
    
    if len(all_videos) == 0:
        print("❌ No videos found!")
        return 1
    
    # Sample videos
    sample_size = min(args.sample, len(all_videos))
    sampled_videos = random.sample(all_videos, sample_size)
    print(f"🎯 Sampling {sample_size} videos")
    
    # Create output directories
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    reports_dir = Path("reports")
    reports_dir.mkdir(exist_ok=True)
    
    # Process videos
    results = []
    skipped = []
    
    print("🚀 Processing videos...")
    for video_path in tqdm(sampled_videos, desc="Cropping"):
        result = process_video(
            video_path, Path(args.input_dir), output_dir,
            args.size, args.shift_y_frac, args.frames_per_video
        )
        
        if result:
            results.append(result)
            if not result.get('success', False):
                skipped.append(result)
        else:
            skipped.append({
                'video_path': str(video_path),
                'error': 'Failed to process',
                'success': False
            })
    
    # Save skip log
    if skipped:
        skip_log_path = reports_dir / "quick_crop50_skips.txt"
        with open(skip_log_path, 'w') as f:
            for skip in skipped:
                f.write(f"{skip['video_path']}: {skip.get('error', 'Unknown error')}\n")
        print(f"⚠️  Logged {len(skipped)} skips to {skip_log_path}")
    
    # Generate HTML gallery
    gallery_path = reports_dir / "quick_crop_gallery.html"
    generate_html_gallery(results, gallery_path)
    
    # Summary
    successful = len([r for r in results if r.get('success', False)])
    print(f"✅ Cropped {successful} videos → {args.output_dir}. Gallery: {gallery_path}")
    
    return 0


if __name__ == "__main__":
    import sys
    sys.exit(main())


# USAGE EXAMPLE:
# python -u scripts/quick_crop50.py \
#   --input_dir "/PATH/TO/icu-videos" \
#   --output_dir "data/quick_stage1_cropped" \
#   --sample 50 \
#   --size 96 \
#   --shift_y_frac 0.08 \
#   --frames_per_video 3
# open reports/quick_crop_gallery.html   # on macOS
