#!/usr/bin/env python3
"""
Dataset Video Organization Script

Organizes existing videos into the proper train/val/test folder structure
based on the generated manifests, preparing for SageMaker migration.

Usage:
    python scripts/organize_dataset_videos.py \
      --manifests_dir "datasets/manifests" \
      --clean_source "data/stage1M_motion_refined_full" \
      --flagged_source "data/validation_flagged/stage1M_motion_refined_full" \
      --output_base "datasets"
"""

import argparse
import pandas as pd
import shutil
from pathlib import Path
from tqdm import tqdm
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def organize_videos_by_manifest(manifest_path: Path, source_dir: str, dest_base: Path, 
                               dataset_type: str) -> dict:
    """Organize videos according to manifest splits."""
    
    # Load manifest
    df = pd.read_csv(manifest_path)
    logger.info(f"Processing {len(df)} videos from {manifest_path.name}")
    
    stats = {'copied': 0, 'failed': 0, 'skipped': 0}
    
    for _, row in tqdm(df.iterrows(), total=len(df), desc=f"Organizing {dataset_type} videos"):
        source_path = Path(row['video_path'])
        split = row['split']
        
        # Check if source exists
        if not source_path.exists():
            stats['skipped'] += 1
            continue
        
        # Create destination path maintaining folder structure
        rel_path = source_path.relative_to(source_path.parts[0])  # Remove 'data' prefix
        dest_path = dest_base / split / rel_path
        
        # Create destination directory
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            # Copy video to organized structure
            shutil.copy2(source_path, dest_path)
            stats['copied'] += 1
        except Exception as e:
            logger.error(f"Failed to copy {source_path}: {e}")
            stats['failed'] += 1
    
    return stats

def create_dataset_metadata(dataset_path: Path, dataset_type: str, stats: dict) -> None:
    """Create dataset metadata file."""
    metadata = {
        'dataset_name': f"dataset_{dataset_type}_{datetime.now().strftime('%Y.%m.%d')}",
        'dataset_type': dataset_type,
        'creation_date': datetime.now().isoformat(),
        'version': '1.0',
        'description': f"ICU Lipreading {dataset_type} dataset with demographic-based splits",
        'total_videos': stats['copied'],
        'failed_copies': stats['failed'],
        'skipped_videos': stats['skipped'],
        'phrases': ['pillow', 'phone', 'doctor', 'glasses', 'help', 'i_need_to_move', 'my_mouth_is_dry'],
        'num_classes': 7,
        'splits': ['train', 'val', 'test'],
        'split_ratios': {'train': 0.7, 'val': 0.15, 'test': 0.15},
        'demographics': {
            'age_groups': ['18to39', '40to64', '65plus'],
            'genders': ['male', 'female'],
            'ethnicities': ['caucasian', 'asian', 'aboriginal', 'not_specified']
        }
    }
    
    with open(dataset_path / 'dataset_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)

def calculate_dataset_stats(dataset_path: Path) -> dict:
    """Calculate basic statistics for the organized dataset."""
    stats = {}
    
    for split in ['train', 'val', 'test']:
        split_path = dataset_path / split
        if split_path.exists():
            video_files = list(split_path.rglob('*.mp4'))
            stats[f"{split}_videos"] = len(video_files)
            
            # Calculate size
            total_size = sum(f.stat().st_size for f in video_files if f.exists())
            stats[f"{split}_size_mb"] = round(total_size / (1024 * 1024), 2)
        else:
            stats[f"{split}_videos"] = 0
            stats[f"{split}_size_mb"] = 0
    
    return stats

def main():
    parser = argparse.ArgumentParser(description='Organize dataset videos by manifest splits')
    parser.add_argument('--manifests_dir', default='datasets/manifests',
                       help='Directory containing manifest files')
    parser.add_argument('--clean_source', default='data/stage1M_motion_refined_full',
                       help='Source directory for clean videos')
    parser.add_argument('--flagged_source', default='data/validation_flagged/stage1M_motion_refined_full',
                       help='Source directory for flagged videos')
    parser.add_argument('--output_base', default='datasets',
                       help='Base output directory')
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting dataset video organization...")
    
    # Setup paths
    manifests_dir = Path(args.manifests_dir)
    output_base = Path(args.output_base)
    date_stamp = datetime.now().strftime('%Y.%m.%d')
    
    clean_dataset_dir = output_base / f"dataset_clean_{date_stamp}"
    flagged_dataset_dir = output_base / f"dataset_flagged_{date_stamp}"
    
    # Create dataset directories
    clean_dataset_dir.mkdir(parents=True, exist_ok=True)
    flagged_dataset_dir.mkdir(parents=True, exist_ok=True)
    
    # Process clean dataset
    logger.info("📁 Organizing clean dataset videos...")
    clean_manifests = [
        manifests_dir / "manifest_clean_train.csv",
        manifests_dir / "manifest_clean_val.csv", 
        manifests_dir / "manifest_clean_test.csv"
    ]
    
    clean_stats = {'copied': 0, 'failed': 0, 'skipped': 0}
    for manifest_path in clean_manifests:
        if manifest_path.exists():
            stats = organize_videos_by_manifest(
                manifest_path, args.clean_source, clean_dataset_dir, "clean"
            )
            for key in clean_stats:
                clean_stats[key] += stats[key]
    
    # Process flagged dataset
    logger.info("📁 Organizing flagged dataset videos...")
    flagged_manifests = [
        manifests_dir / "manifest_flagged_train.csv",
        manifests_dir / "manifest_flagged_val.csv",
        manifests_dir / "manifest_flagged_test.csv"
    ]
    
    flagged_stats = {'copied': 0, 'failed': 0, 'skipped': 0}
    for manifest_path in flagged_manifests:
        if manifest_path.exists():
            stats = organize_videos_by_manifest(
                manifest_path, args.flagged_source, flagged_dataset_dir, "flagged"
            )
            for key in flagged_stats:
                flagged_stats[key] += stats[key]
    
    # Create metadata files
    logger.info("📋 Creating dataset metadata...")
    create_dataset_metadata(clean_dataset_dir, "clean", clean_stats)
    create_dataset_metadata(flagged_dataset_dir, "flagged", flagged_stats)
    
    # Calculate final statistics
    logger.info("📊 Calculating dataset statistics...")
    clean_final_stats = calculate_dataset_stats(clean_dataset_dir)
    flagged_final_stats = calculate_dataset_stats(flagged_dataset_dir)
    
    # Create organization report
    report = {
        'organization_date': datetime.now().isoformat(),
        'clean_dataset': {
            'path': str(clean_dataset_dir),
            'organization_stats': clean_stats,
            'final_stats': clean_final_stats
        },
        'flagged_dataset': {
            'path': str(flagged_dataset_dir),
            'organization_stats': flagged_stats,
            'final_stats': flagged_final_stats
        }
    }
    
    with open(manifests_dir / "dataset_organization_report.json", 'w') as f:
        json.dump(report, f, indent=2)
    
    # Final summary
    logger.info("=" * 60)
    logger.info("✅ DATASET ORGANIZATION COMPLETE")
    logger.info("=" * 60)
    logger.info(f"📁 Clean dataset: {clean_dataset_dir}")
    logger.info(f"   - Videos organized: {clean_stats['copied']:,}")
    logger.info(f"   - Train: {clean_final_stats.get('train_videos', 0):,} videos ({clean_final_stats.get('train_size_mb', 0)} MB)")
    logger.info(f"   - Val: {clean_final_stats.get('val_videos', 0):,} videos ({clean_final_stats.get('val_size_mb', 0)} MB)")
    logger.info(f"   - Test: {clean_final_stats.get('test_videos', 0):,} videos ({clean_final_stats.get('test_size_mb', 0)} MB)")
    
    logger.info(f"📁 Flagged dataset: {flagged_dataset_dir}")
    logger.info(f"   - Videos organized: {flagged_stats['copied']:,}")
    logger.info(f"   - Train: {flagged_final_stats.get('train_videos', 0):,} videos ({flagged_final_stats.get('train_size_mb', 0)} MB)")
    logger.info(f"   - Val: {flagged_final_stats.get('val_videos', 0):,} videos ({flagged_final_stats.get('val_size_mb', 0)} MB)")
    logger.info(f"   - Test: {flagged_final_stats.get('test_videos', 0):,} videos ({flagged_final_stats.get('test_size_mb', 0)} MB)")
    
    logger.info(f"📝 Organization report: {manifests_dir / 'dataset_organization_report.json'}")
    logger.info("🚀 Datasets ready for SageMaker migration!")
    
    return 0

if __name__ == "__main__":
    exit(main())
