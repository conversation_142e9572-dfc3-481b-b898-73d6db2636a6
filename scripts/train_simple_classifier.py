#!/usr/bin/env python3
"""
Simple CNN Classifier Training Script

Demonstrates how to train a lightweight CNN classifier using the preprocessed
video data. This is a proof-of-concept for the classifier training pipeline.

Usage:
    python scripts/train_simple_classifier.py --data_dir data/classifier_ready
"""

import argparse
import numpy as np
import pandas as pd
import json
import os
from pathlib import Path
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns


def load_preprocessed_data(data_dir: str):
    """Load preprocessed video data for training."""
    # Load labels mapping
    labels_path = Path(data_dir) / "labels.json"
    with open(labels_path, 'r') as f:
        labels_mapping = json.load(f)
    
    # Load manifest
    manifest_path = Path(data_dir) / "training_manifest.csv"
    manifest = pd.read_csv(manifest_path)
    
    # Load video data
    X_data = []
    y_data = []
    
    for _, row in manifest.iterrows():
        # Load numpy array
        array = np.load(row['file_path'])
        X_data.append(array)
        y_data.append(row['class_id'])
    
    X = np.array(X_data)  # Shape: (n_samples, frames, height, width, channels)
    y = np.array(y_data)
    
    return X, y, labels_mapping


def create_3d_cnn_model(input_shape, num_classes):
    """Create a simple 3D CNN model for video classification."""
    model = keras.Sequential([
        # Input layer
        layers.Input(shape=input_shape),
        
        # 3D Convolutional layers
        layers.Conv3D(32, (3, 3, 3), activation='relu', padding='same'),
        layers.MaxPooling3D((2, 2, 2)),
        layers.BatchNormalization(),
        
        layers.Conv3D(64, (3, 3, 3), activation='relu', padding='same'),
        layers.MaxPooling3D((2, 2, 2)),
        layers.BatchNormalization(),
        
        layers.Conv3D(128, (3, 3, 3), activation='relu', padding='same'),
        layers.MaxPooling3D((2, 2, 2)),
        layers.BatchNormalization(),
        
        # Global average pooling to reduce parameters
        layers.GlobalAveragePooling3D(),
        
        # Dense layers
        layers.Dense(256, activation='relu'),
        layers.Dropout(0.5),
        layers.Dense(128, activation='relu'),
        layers.Dropout(0.3),
        
        # Output layer
        layers.Dense(num_classes, activation='softmax')
    ])
    
    return model


def create_2d_cnn_model(input_shape, num_classes):
    """Create a 2D CNN model that processes frames independently."""
    # Input shape: (frames, height, width, channels)
    frames, height, width, channels = input_shape
    
    # Create a model that processes each frame
    frame_input = layers.Input(shape=(height, width, channels))
    
    # 2D CNN for single frame
    x = layers.Conv2D(32, (3, 3), activation='relu', padding='same')(frame_input)
    x = layers.MaxPooling2D((2, 2))(x)
    x = layers.BatchNormalization()(x)
    
    x = layers.Conv2D(64, (3, 3), activation='relu', padding='same')(x)
    x = layers.MaxPooling2D((2, 2))(x)
    x = layers.BatchNormalization()(x)
    
    x = layers.Conv2D(128, (3, 3), activation='relu', padding='same')(x)
    x = layers.GlobalAveragePooling2D()(x)
    
    frame_model = keras.Model(frame_input, x)
    
    # Now create the full model that processes all frames
    video_input = layers.Input(shape=input_shape)
    
    # Apply frame model to each frame using TimeDistributed
    frame_features = layers.TimeDistributed(frame_model)(video_input)
    
    # Temporal processing with LSTM
    x = layers.LSTM(128, return_sequences=True)(frame_features)
    x = layers.LSTM(64)(x)
    
    # Final classification
    x = layers.Dense(128, activation='relu')(x)
    x = layers.Dropout(0.5)(x)
    output = layers.Dense(num_classes, activation='softmax')(x)
    
    model = keras.Model(video_input, output)
    return model


def create_simple_model(input_shape, num_classes):
    """Create a very simple model for proof of concept."""
    model = keras.Sequential([
        layers.Input(shape=input_shape),
        
        # Flatten temporal dimension by averaging
        layers.Lambda(lambda x: tf.reduce_mean(x, axis=1)),  # Average across frames
        
        # Simple 2D CNN
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2)),
        layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2)),
        layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
        layers.GlobalAveragePooling2D(),
        
        # Classification head
        layers.Dense(128, activation='relu'),
        layers.Dropout(0.5),
        layers.Dense(num_classes, activation='softmax')
    ])
    
    return model


def plot_training_history(history, output_dir):
    """Plot training history."""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # Plot accuracy
    ax1.plot(history.history['accuracy'], label='Training Accuracy')
    if 'val_accuracy' in history.history:
        ax1.plot(history.history['val_accuracy'], label='Validation Accuracy')
    ax1.set_title('Model Accuracy')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Accuracy')
    ax1.legend()
    
    # Plot loss
    ax2.plot(history.history['loss'], label='Training Loss')
    if 'val_loss' in history.history:
        ax2.plot(history.history['val_loss'], label='Validation Loss')
    ax2.set_title('Model Loss')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    
    plt.tight_layout()
    
    # Save plot
    plot_path = Path(output_dir) / "training_history.png"
    plt.savefig(str(plot_path), dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"📊 Training history saved: {plot_path}")


def main():
    parser = argparse.ArgumentParser(description='Train simple CNN classifier')
    parser.add_argument('--data_dir', default='data/classifier_ready',
                       help='Directory containing preprocessed data')
    parser.add_argument('--model_type', choices=['simple', '2d_cnn', '3d_cnn'], default='simple',
                       help='Type of model to train')
    parser.add_argument('--epochs', type=int, default=50,
                       help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=2,
                       help='Batch size for training')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                       help='Learning rate')
    
    args = parser.parse_args()
    
    print("🧠 Simple CNN Classifier Training")
    print(f"📁 Data directory: {args.data_dir}")
    print(f"🏗️  Model type: {args.model_type}")
    print(f"🔄 Epochs: {args.epochs}")
    print(f"📦 Batch size: {args.batch_size}")
    
    # Load data
    print("\n📦 Loading preprocessed data...")
    X, y, labels_mapping = load_preprocessed_data(args.data_dir)
    
    print(f"✅ Loaded data: X shape={X.shape}, y shape={y.shape}")
    print(f"🏷️  Classes: {list(labels_mapping.keys())}")
    
    # Note: With only 7 samples, we can't do proper train/test split
    # This is just a proof of concept
    print(f"\n⚠️  Note: Only {len(X)} samples available - this is a proof of concept")
    print(f"💡 For real training, you would need many more samples per class")
    
    # Create model
    print(f"\n🏗️  Creating {args.model_type} model...")
    input_shape = X.shape[1:]  # (frames, height, width, channels)
    num_classes = len(labels_mapping)
    
    if args.model_type == 'simple':
        model = create_simple_model(input_shape, num_classes)
    elif args.model_type == '2d_cnn':
        model = create_2d_cnn_model(input_shape, num_classes)
    elif args.model_type == '3d_cnn':
        model = create_3d_cnn_model(input_shape, num_classes)
    
    # Compile model
    model.compile(
        optimizer=keras.optimizers.Adam(learning_rate=args.learning_rate),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # Print model summary
    print(f"\n📋 Model Summary:")
    model.summary()
    
    # Train model (with all data since we have so few samples)
    print(f"\n🚀 Training model...")
    
    # Create output directory
    output_dir = Path(args.data_dir) / "training_results"
    output_dir.mkdir(exist_ok=True)
    
    # Train
    history = model.fit(
        X, y,
        epochs=args.epochs,
        batch_size=args.batch_size,
        verbose=1
    )
    
    # Plot training history
    plot_training_history(history, output_dir)
    
    # Make predictions
    print(f"\n🎯 Making predictions...")
    predictions = model.predict(X)
    predicted_classes = np.argmax(predictions, axis=1)
    
    # Print results
    print(f"\n📊 Results:")
    for i, (true_label, pred_label, confidence) in enumerate(zip(y, predicted_classes, predictions)):
        true_phrase = [k for k, v in labels_mapping.items() if v == true_label][0]
        pred_phrase = [k for k, v in labels_mapping.items() if v == pred_label][0]
        max_confidence = np.max(confidence)
        
        status = "✅" if true_label == pred_label else "❌"
        print(f"  {status} Sample {i}: True={true_phrase}, Pred={pred_phrase}, Conf={max_confidence:.3f}")
    
    # Calculate accuracy
    accuracy = np.mean(y == predicted_classes)
    print(f"\n🎯 Training Accuracy: {accuracy:.3f} ({accuracy*100:.1f}%)")
    
    # Save model
    model_path = output_dir / f"{args.model_type}_classifier.h5"
    model.save(str(model_path))
    print(f"💾 Model saved: {model_path}")
    
    print(f"\n✅ Training complete!")
    print(f"📁 Results saved in: {output_dir}")
    
    return 0


if __name__ == '__main__':
    import sys
    sys.exit(main())
