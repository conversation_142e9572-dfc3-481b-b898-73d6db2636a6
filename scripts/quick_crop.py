#!/usr/bin/env python3
"""
Quick Stage-1 Crop - Fast Sanity Check for 20 Desktop Videos

A minimal, fast script to test Stage-1 cropping on a small sample of videos.
Crops frames to top-middle third with downward shift, letterboxes to square,
and generates an HTML gallery for visual confirmation.

Usage:
    python scripts/quick_crop.py \
      --input_dir "~/Desktop/top10dataset6.9.25" \
      --output_dir "data/quick_stage1_cropped" \
      --sample 20 \
      --size 96 \
      --shift_y_frac 0.08 \
      --frames_per_video 3 \
      --seed 42
"""

import argparse
import cv2
import numpy as np
import os
import random
from pathlib import Path
from tqdm import tqdm
import json
import base64
from typing import List, Tuple, Optional


def compute_roi_with_shift(width: int, height: int, shift_y_frac: float = 0.08) -> Tuple[int, int, int, int]:
    """
    Compute ROI coordinates for top-middle third with downward shift.
    
    Args:
        width: Frame width
        height: Frame height
        shift_y_frac: Fraction of height to shift ROI downward
        
    Returns:
        (x0, y0, x1, y1) ROI coordinates
    """
    # Base ROI: top-middle third
    x0 = width // 3
    x1 = (2 * width) // 3
    y0 = 0
    y1 = height // 3
    
    # Apply downward shift
    dy = int(height * shift_y_frac)
    y0 += dy
    y1 += dy
    
    # Clamp to image bounds
    x0 = max(0, x0)
    x1 = min(width, x1)
    y0 = max(0, y0)
    y1 = min(height, y1)
    
    return x0, y0, x1, y1


def letterbox_to_square(image: np.ndarray, target_size: int) -> np.ndarray:
    """
    Letterbox image to square while preserving aspect ratio.
    Pads with black pixels to maintain square dimensions.

    Args:
        image: Input image (H, W, C)
        target_size: Target square size

    Returns:
        Letterboxed square image
    """
    h, w = image.shape[:2]

    # Scale by shortest side to fit within target_size
    scale = target_size / max(h, w)
    new_h, new_w = int(h * scale), int(w * scale)

    # Resize
    resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)

    # Create square canvas with black background
    canvas = np.zeros((target_size, target_size, 3), dtype=np.uint8)

    # Calculate padding to center the image
    pad_h = (target_size - new_h) // 2
    pad_w = (target_size - new_w) // 2

    # Place resized image in center
    canvas[pad_h:pad_h + new_h, pad_w:pad_w + new_w] = resized

    return canvas


def find_videos(input_dir: str) -> List[Path]:
    """Find all video files recursively."""
    input_path = Path(input_dir).expanduser()
    video_extensions = {'.mp4', '.mov', '.avi', '.mkv', '.webm'}

    videos = []
    for ext in video_extensions:
        videos.extend(input_path.rglob(f'*{ext}'))
        videos.extend(input_path.rglob(f'*{ext.upper()}'))

    return sorted(videos)


def extract_sample_frames(cap: cv2.VideoCapture, total_frames: int, num_samples: int) -> List[int]:
    """Extract evenly spaced frame indices for thumbnails."""
    if total_frames <= num_samples:
        return list(range(total_frames))
    
    # Evenly spaced indices
    indices = np.linspace(0, total_frames - 1, num_samples, dtype=int)
    return indices.tolist()


def process_video(video_path: Path, output_dir: Path, size: int, shift_y_frac: float,
                 frames_per_video: int, input_dir: Path) -> Optional[dict]:
    """
    Process a single video: crop, letterbox, and save.

    Returns:
        Dict with processing info or None if failed
    """
    try:
        # Open video
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return None
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0 or np.isnan(fps):
            fps = 25.0  # Fallback
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        # Handle invalid frame count (common with some webm files)
        if total_frames <= 0 or total_frames > 1e10:
            total_frames = 1000  # Fallback estimate

        if width <= 0 or height <= 0:
            cap.release()
            return None
        
        # Compute ROI
        x0, y0, x1, y1 = compute_roi_with_shift(width, height, shift_y_frac)
        
        # Mirror directory structure
        rel_path = video_path.relative_to(input_dir)
        output_video_path = output_dir / rel_path.with_suffix('.mp4')
        output_video_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Setup video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_video_path), fourcc, fps, (size, size))
        
        # Get sample frame indices for thumbnails
        sample_indices = extract_sample_frames(cap, total_frames, frames_per_video)
        thumbnails = []
        
        frame_idx = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Crop to ROI
            cropped = frame[y0:y1, x0:x1]
            
            # Letterbox to square
            letterboxed = letterbox_to_square(cropped, size)
            
            # Write frame
            out.write(letterboxed)
            
            # Save thumbnail if this is a sample frame
            if frame_idx in sample_indices:
                thumbnail_path = output_video_path.parent / f"{output_video_path.stem}_thumb_{len(thumbnails)}.png"
                cv2.imwrite(str(thumbnail_path), letterboxed)
                thumbnails.append(thumbnail_path)
            
            frame_idx += 1
        
        cap.release()
        out.release()
        
        return {
            'input_path': str(video_path),
            'output_path': str(output_video_path),
            'thumbnails': [str(t) for t in thumbnails],
            'frames_processed': frame_idx,
            'roi': (x0, y0, x1, y1),
            'original_size': (width, height)
        }
        
    except Exception as e:
        if 'cap' in locals():
            cap.release()
        if 'out' in locals():
            out.release()
        return None


def generate_html_gallery(results: List[dict], output_path: str):
    """Generate HTML gallery showing cropped results."""
    html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>Quick Stage-1 Crop Gallery</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { text-align: center; margin-bottom: 30px; }
        .video-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        .video-card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .video-title { font-weight: bold; margin-bottom: 10px; word-break: break-all; }
        .thumbnails { display: flex; gap: 10px; margin: 15px 0; }
        .thumbnail { max-width: 96px; max-height: 96px; border: 1px solid #ddd; }
        .info { font-size: 12px; color: #666; }
        .roi-info { background: #f0f0f0; padding: 8px; border-radius: 4px; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Quick Stage-1 Crop Results</h1>
        <p>Showing cropped mouth regions from sampled videos</p>
    </div>
    <div class="video-grid">
"""
    
    for result in results:
        rel_input = os.path.relpath(result['input_path'])
        rel_output = os.path.relpath(result['output_path'])
        
        html_content += f"""
        <div class="video-card">
            <div class="video-title">{rel_input}</div>
            <div class="thumbnails">
"""
        
        for thumb_path in result['thumbnails']:
            rel_thumb = os.path.relpath(thumb_path)
            html_content += f'                <img src="../{rel_thumb}" class="thumbnail" alt="Frame sample">\n'
        
        roi = result['roi']
        orig_size = result['original_size']
        
        html_content += f"""
            </div>
            <div class="info">
                <strong>Output:</strong> {rel_output}<br>
                <strong>Frames:</strong> {result['frames_processed']}<br>
                <strong>Original:</strong> {orig_size[0]}×{orig_size[1]}
            </div>
            <div class="roi-info">
                <strong>ROI:</strong> ({roi[0]}, {roi[1]}) → ({roi[2]}, {roi[3]})<br>
                <strong>Size:</strong> {roi[2]-roi[0]}×{roi[3]-roi[1]}
            </div>
        </div>
"""
    
    html_content += """
    </div>
</body>
</html>
"""
    
    with open(output_path, 'w') as f:
        f.write(html_content)


def main():
    parser = argparse.ArgumentParser(description='Quick Stage-1 Crop - Fast Sanity Check')
    parser.add_argument('--input_dir', required=True, help='Input directory with videos')
    parser.add_argument('--output_dir', required=True, help='Output directory')
    parser.add_argument('--sample', type=int, default=20, help='Number of videos to sample')
    parser.add_argument('--size', type=int, default=96, help='Target crop size (square)')
    parser.add_argument('--shift_y_frac', type=float, default=0.08, help='Downward ROI shift fraction')
    parser.add_argument('--frames_per_video', type=int, default=3, help='Sample frames for gallery')
    parser.add_argument('--seed', type=int, default=42, help='Random seed for sampling')
    
    args = parser.parse_args()
    
    # Set random seed
    random.seed(args.seed)
    np.random.seed(args.seed)
    
    # Setup paths
    input_dir = Path(args.input_dir).expanduser()
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    reports_dir = Path('reports')
    reports_dir.mkdir(exist_ok=True)
    
    # Find videos
    print(f"Scanning for videos in {input_dir}...")
    all_videos = find_videos(str(input_dir))
    print(f"Found {len(all_videos)} videos")
    
    if not all_videos:
        print("No videos found!")
        return
    
    # Sample videos
    sample_size = min(args.sample, len(all_videos))
    sampled_videos = random.sample(all_videos, sample_size)
    print(f"Processing {sample_size} sampled videos...")
    
    # Process videos
    results = []
    skipped = []
    
    for video_path in tqdm(sampled_videos, desc="Processing videos"):
        result = process_video(video_path, output_dir, args.size, args.shift_y_frac, 
                             args.frames_per_video, input_dir)
        if result:
            results.append(result)
        else:
            skipped.append(str(video_path))
    
    # Write skips log
    if skipped:
        with open(reports_dir / 'quick_crop_skips.txt', 'w') as f:
            f.write("Skipped videos (unreadable/corrupt):\n")
            for skip in skipped:
                f.write(f"{skip}\n")
    
    # Generate gallery
    if results:
        gallery_path = reports_dir / 'quick_crop_gallery.html'
        generate_html_gallery(results, str(gallery_path))
        print(f"\nCropped {len(results)} videos → {output_dir}")
        print(f"Gallery → {gallery_path}")
        if skipped:
            print(f"Skips log → reports/quick_crop_skips.txt")
    else:
        print("No videos were successfully processed!")


if __name__ == '__main__':
    main()


# Usage example:
# python -u scripts/quick_crop.py \
#   --input_dir "~/Desktop/top10dataset6.9.25" \
#   --output_dir "data/quick_stage1_cropped" \
#   --sample 20 \
#   --size 96 \
#   --shift_y_frac 0.08 \
#   --frames_per_video 3 \
#   --seed 42
# open reports/quick_crop_gallery.html   # macOS
