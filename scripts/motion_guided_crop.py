#!/usr/bin/env python3
"""
Motion-Guided Stage-1 Crop Refinement

Refines existing Stage-1 crops by detecting lip movement hotspots and re-centering
the ROI around them. Uses MediaPipe landmarks for confirmation only.

Usage:
    python -u scripts/motion_guided_crop.py \
      --input_dir "data/stage1_cropped_top7" \
      --output_dir "data/stage1M_motion_refined" \
      --gallery "reports/stage1M_gallery.html" \
      --summary "reports/stage1M_summary.csv" \
      --size 96 --upsample 2.0 --motion_percentile 85 \
      --min_motion_share 0.12 --ema 0.2 --margin 1.4 \
      --min_det_rate 0.60 --frames_for_gallery 3 --workers 4 \
      --sample 20
"""

import argparse
import cv2
import numpy as np
import os
import csv
import random
from pathlib import Path
from tqdm import tqdm
import multiprocessing as mp
from typing import List, Tuple, Optional, Dict, Any
import mediapipe as mp_face
from jinja2 import Template
import base64


def find_videos(input_dir: str, extensions: List[str]) -> List[Path]:
    """Find all video files recursively."""
    input_path = Path(input_dir)
    videos = []
    for ext in extensions:
        videos.extend(input_path.rglob(f'*{ext}'))
        videos.extend(input_path.rglob(f'*{ext.upper()}'))
    return sorted(videos)


def compute_optical_flow(prev_gray: np.ndarray, curr_gray: np.ndarray) -> np.ndarray:
    """Compute dense optical flow using Farneback method."""
    try:
        # Use Farneback method for dense optical flow
        flow = cv2.calcOpticalFlowPyrLK(
            prev_gray, curr_gray,
            pyr_scale=0.5, levels=3, winsize=15,
            iterations=3, poly_n=5, poly_sigma=1.2
        )

        # Calculate flow magnitude
        magnitude = np.sqrt(flow[..., 0]**2 + flow[..., 1]**2)
        return magnitude.astype(np.float32)

    except:
        # Fallback: simple frame difference
        diff = cv2.absdiff(prev_gray, curr_gray)
        return diff.astype(np.float32)


def remove_global_motion(flow_magnitude: np.ndarray) -> np.ndarray:
    """Remove global camera motion by subtracting median flow."""
    if flow_magnitude.size == 0:
        return flow_magnitude
    
    median_flow = np.median(flow_magnitude[flow_magnitude > 0])
    if not np.isnan(median_flow):
        flow_magnitude = np.maximum(0, flow_magnitude - median_flow)
    
    return flow_magnitude


def get_motion_hotspot(motion_heatmap: np.ndarray, percentile: int) -> Tuple[int, int, int, int]:
    """Extract bounding box from motion hotspot."""
    h, w = motion_heatmap.shape
    
    # Zero out top 30% to focus on lower-mid mouth region
    top_30_percent = int(0.3 * h)
    motion_heatmap[:top_30_percent, :] = 0
    
    # Threshold at percentile
    if np.max(motion_heatmap) > 0:
        threshold = np.percentile(motion_heatmap[motion_heatmap > 0], percentile)
        binary_mask = (motion_heatmap >= threshold).astype(np.uint8)
    else:
        binary_mask = np.zeros_like(motion_heatmap, dtype=np.uint8)
    
    # Morphological operations
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)
    
    # Find largest connected component
    contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if contours:
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        return x, y, x + w, y + h
    else:
        # Return center region as fallback
        return w//4, h//4, 3*w//4, 3*h//4


def expand_box(x0: int, y0: int, x1: int, y1: int, margin: float, 
               frame_w: int, frame_h: int) -> Tuple[int, int, int, int]:
    """Expand bounding box by margin factor and clamp to frame boundaries."""
    center_x, center_y = (x0 + x1) // 2, (y0 + y1) // 2
    width, height = x1 - x0, y1 - y0
    
    new_width = int(width * margin)
    new_height = int(height * margin)
    
    new_x0 = max(0, center_x - new_width // 2)
    new_y0 = max(0, center_y - new_height // 2)
    new_x1 = min(frame_w, center_x + new_width // 2)
    new_y1 = min(frame_h, center_y + new_height // 2)
    
    return new_x0, new_y0, new_x1, new_y1


def apply_ema_smoothing(new_box: Tuple[int, int, int, int], 
                       prev_box: Optional[Tuple[int, int, int, int]], 
                       ema: float) -> Tuple[int, int, int, int]:
    """Apply exponential moving average smoothing to box coordinates."""
    if prev_box is None:
        return new_box
    
    x0, y0, x1, y1 = new_box
    px0, py0, px1, py1 = prev_box
    
    smooth_x0 = int(ema * x0 + (1 - ema) * px0)
    smooth_y0 = int(ema * y0 + (1 - ema) * py0)
    smooth_x1 = int(ema * x1 + (1 - ema) * px1)
    smooth_y1 = int(ema * y1 + (1 - ema) * py1)
    
    return smooth_x0, smooth_y0, smooth_x1, smooth_y1


def get_mouth_landmarks(landmarks, image_shape):
    """Extract mouth landmark coordinates from MediaPipe face mesh."""
    h, w = image_shape[:2]
    
    # MediaPipe mouth landmark indices
    MOUTH_INDICES = [
        61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,  # Outer lip
        78, 95, 88, 178, 87, 14, 317, 402, 318, 324  # Inner lip
    ]
    
    mouth_points = []
    for idx in MOUTH_INDICES:
        if idx < len(landmarks.landmark):
            x = int(landmarks.landmark[idx].x * w)
            y = int(landmarks.landmark[idx].y * h)
            mouth_points.append((x, y))
    
    return mouth_points


def compute_iou(box1: Tuple[int, int, int, int], box2: Tuple[int, int, int, int]) -> float:
    """Compute Intersection over Union between two bounding boxes."""
    x1_min, y1_min, x1_max, y1_max = box1
    x2_min, y2_min, x2_max, y2_max = box2
    
    # Intersection
    inter_x_min = max(x1_min, x2_min)
    inter_y_min = max(y1_min, y2_min)
    inter_x_max = min(x1_max, x2_max)
    inter_y_max = min(y1_max, y2_max)
    
    if inter_x_max <= inter_x_min or inter_y_max <= inter_y_min:
        return 0.0
    
    inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)
    
    # Union
    area1 = (x1_max - x1_min) * (y1_max - y1_min)
    area2 = (x2_max - x2_min) * (y2_max - y2_min)
    union_area = area1 + area2 - inter_area
    
    return inter_area / union_area if union_area > 0 else 0.0


def letterbox_to_square(image: np.ndarray, target_size: int) -> np.ndarray:
    """Letterbox image to square while preserving aspect ratio."""
    h, w = image.shape[:2]
    
    # Scale by longest side to fit within target_size
    scale = target_size / max(h, w)
    new_h, new_w = int(h * scale), int(w * scale)
    
    # Resize
    resized = cv2.resize(image, (new_w, new_h), interpolation=cv2.INTER_AREA)
    
    # Create square canvas
    canvas = np.zeros((target_size, target_size, 3), dtype=np.uint8)
    
    # Calculate padding to center the image
    pad_h = (target_size - new_h) // 2
    pad_w = (target_size - new_w) // 2
    
    # Place resized image in center
    canvas[pad_h:pad_h + new_h, pad_w:pad_w + new_w] = resized
    
    return canvas


def generate_html_gallery(results: List[Dict], output_path: str):
    """Generate HTML gallery showing motion analysis results."""
    # Sort: fallback videos first (suspects), then by motion share
    sorted_results = sorted(results, key=lambda x: (not x.get('chose_fallback', False), -x.get('motion_share', 0)))

    html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>Motion-Guided Crop Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { text-align: center; margin-bottom: 30px; }
        .video-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(600px, 1fr)); gap: 20px; }
        .video-card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .fallback { border-left: 5px solid #ff4444; }
        .motion-guided { border-left: 5px solid #44ff44; }
        .video-title { font-weight: bold; margin-bottom: 10px; word-break: break-all; }
        .thumbnails { display: flex; gap: 10px; margin: 15px 0; flex-wrap: wrap; }
        .thumbnail { max-width: 120px; max-height: 120px; border: 1px solid #ddd; }
        .metrics { display: flex; gap: 10px; margin: 10px 0; flex-wrap: wrap; }
        .metric { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .metric.good { background: #e8f5e8; color: #2d5a2d; }
        .metric.bad { background: #ffe8e8; color: #5a2d2d; }
        .metric.neutral { background: #f0f0f0; color: #333; }
        .info { font-size: 12px; color: #666; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Motion-Guided Crop Results</h1>
        <p>Fallback videos shown first - videos that used original crop instead of motion guidance</p>
    </div>
    <div class="video-grid">
    {% for result in results %}
        <div class="video-card {{ 'fallback' if result.chose_fallback else 'motion-guided' }}">
            <div class="video-title">{{ result.video_path }}</div>
            <div class="thumbnails">
                {% for frame in result.sample_frames %}
                <img src="data:image/png;base64,{{ frame }}" class="thumbnail" alt="Analysis frame">
                {% endfor %}
            </div>
            <div class="metrics">
                <span class="metric {{ 'bad' if result.detection_rate < 0.6 else 'good' }}">
                    Detection: {{ "%.3f"|format(result.detection_rate) }}
                </span>
                <span class="metric {{ 'bad' if result.motion_share < 0.12 else 'good' }}">
                    Motion Share: {{ "%.3f"|format(result.motion_share) }}
                </span>
                <span class="metric {{ 'bad' if result.iou_with_mouth_bbox < 0.2 else 'good' }}">
                    IoU: {{ "%.3f"|format(result.iou_with_mouth_bbox) }}
                </span>
                <span class="metric neutral">
                    Max Motion: {{ "%.1f"|format(result.heatmap_max) }}
                </span>
            </div>
            <div class="info">
                <strong>Frames:</strong> {{ result.frames }} |
                <strong>Method:</strong> {{ 'Fallback (Original)' if result.chose_fallback else 'Motion-Guided' }}
            </div>
        </div>
    {% endfor %}
    </div>
</body>
</html>
"""

    # Convert sample frames to base64
    for result in sorted_results:
        if 'sample_frames' in result:
            b64_frames = []
            for frame in result['sample_frames']:
                _, buffer = cv2.imencode('.png', frame)
                b64_frames.append(base64.b64encode(buffer).decode('utf-8'))
            result['sample_frames'] = b64_frames

    template = Template(html_template)
    html_content = template.render(results=sorted_results)

    with open(output_path, 'w') as f:
        f.write(html_content)


def process_single_video(args) -> Dict[str, Any]:
    """Process a single video with motion-guided cropping."""
    (video_path, output_dir, input_dir, size, upsample, motion_percentile, 
     min_motion_share, ema, margin, min_det_rate, frames_for_gallery) = args
    
    try:
        # Initialize MediaPipe Face Mesh
        mp_face_mesh = mp_face.solutions.face_mesh
        face_mesh = mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return {'error': f'Failed to open {video_path}'}
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0 or np.isnan(fps):
            fps = 25.0
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames <= 0:
            total_frames = 1000
        
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Setup output path
        rel_path = video_path.relative_to(input_dir)
        output_video_path = output_dir / rel_path
        output_video_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Read all frames for analysis
        frames = []
        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        
        cap.release()
        
        if len(frames) < 2:
            face_mesh.close()
            return {'error': f'Insufficient frames in {video_path}'}
        
        # Motion analysis
        motion_heatmap = np.zeros((height, width), dtype=np.float32)
        prev_gray = None
        
        for frame in frames:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            if upsample > 1.0:
                new_h, new_w = int(height * upsample), int(width * upsample)
                gray = cv2.resize(gray, (new_w, new_h))
            
            if prev_gray is not None:
                flow_magnitude = compute_optical_flow(prev_gray, gray)
                flow_magnitude = remove_global_motion(flow_magnitude)
                
                if upsample > 1.0:
                    flow_magnitude = cv2.resize(flow_magnitude, (width, height))
                
                motion_heatmap += flow_magnitude
            
            prev_gray = gray
        
        # Get motion hotspot
        x0, y0, x1, y1 = get_motion_hotspot(motion_heatmap, motion_percentile)
        motion_box = expand_box(x0, y0, x1, y1, margin, width, height)
        
        # Calculate motion share
        total_motion = np.sum(motion_heatmap)
        if total_motion > 0:
            mx0, my0, mx1, my1 = motion_box
            motion_inside = np.sum(motion_heatmap[my0:my1, mx0:mx1])
            motion_share = motion_inside / total_motion
        else:
            motion_share = 0.0
        
        # MediaPipe landmark confirmation
        detected_frames = 0
        mouth_boxes = []
        sample_frames = list(range(0, len(frames), 5))  # Every 5th frame
        
        for i in sample_frames:
            rgb_frame = cv2.cvtColor(frames[i], cv2.COLOR_BGR2RGB)
            results = face_mesh.process(rgb_frame)
            
            if results.multi_face_landmarks:
                detected_frames += 1
                landmarks = results.multi_face_landmarks[0]
                mouth_points = get_mouth_landmarks(landmarks, frames[i].shape)
                
                if mouth_points:
                    mouth_points = np.array(mouth_points)
                    mx0, my0 = np.min(mouth_points, axis=0)
                    mx1, my1 = np.max(mouth_points, axis=0)
                    mouth_boxes.append((mx0, my0, mx1, my1))
        
        detection_rate = detected_frames / len(sample_frames) if sample_frames else 0.0
        
        # Compute IoU with mouth landmarks
        iou_with_mouth = 0.0
        if mouth_boxes:
            avg_mouth_box = np.mean(mouth_boxes, axis=0).astype(int)
            iou_with_mouth = compute_iou(motion_box, tuple(avg_mouth_box))
        
        # Accept/reject decision
        chose_fallback = False
        if (motion_share >= min_motion_share and 
            (iou_with_mouth >= 0.2 or detection_rate >= min_det_rate or not mouth_boxes)):
            final_box = motion_box
        else:
            # Fallback to center crop
            final_box = (width//4, height//4, 3*width//4, 3*height//4)
            chose_fallback = True
        
        # Generate output video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_video_path), fourcc, fps, (size, size))
        
        fx0, fy0, fx1, fy1 = final_box
        for frame in frames:
            cropped = frame[fy0:fy1, fx0:fx1]
            letterboxed = letterbox_to_square(cropped, size)
            out.write(letterboxed)
        
        out.release()
        
        # Generate sample frames for gallery
        sample_indices = [0, len(frames)//2, len(frames)-1]
        sample_frames_data = []
        
        for i in sample_indices:
            if i < len(frames):
                # Original with box overlay
                overlay = frames[i].copy()
                cv2.rectangle(overlay, (fx0, fy0), (fx1, fy1), (0, 255, 0), 2)
                
                # Motion heatmap visualization
                heatmap_norm = cv2.normalize(motion_heatmap, None, 0, 255, cv2.NORM_MINMAX)
                heatmap_color = cv2.applyColorMap(heatmap_norm.astype(np.uint8), cv2.COLORMAP_JET)
                
                sample_frames_data.extend([overlay, heatmap_color])
        
        face_mesh.close()
        
        return {
            'video_path': str(video_path),
            'frames': len(frames),
            'detection_rate': detection_rate,
            'iou_with_mouth_bbox': iou_with_mouth,
            'motion_share': motion_share,
            'heatmap_max': float(np.max(motion_heatmap)),
            'chose_fallback': chose_fallback,
            'sample_frames': sample_frames_data
        }
        
    except Exception as e:
        if 'face_mesh' in locals():
            face_mesh.close()
        return {'error': f'Exception processing {video_path}: {e}'}


def main():
    parser = argparse.ArgumentParser(description='Motion-Guided Stage-1 Crop Refinement')
    parser.add_argument('--input_dir', required=True, help='Input directory with Stage-1 crops')
    parser.add_argument('--output_dir', required=True, help='Output directory for refined crops')
    parser.add_argument('--gallery', required=True, help='HTML gallery output path')
    parser.add_argument('--summary', required=True, help='CSV summary output path')
    parser.add_argument('--size', type=int, default=96, help='Output square dimensions')
    parser.add_argument('--upsample', type=float, default=2.0, help='Scale factor for analysis')
    parser.add_argument('--motion_percentile', type=int, default=85, help='Motion threshold percentile')
    parser.add_argument('--min_motion_share', type=float, default=0.12, help='Min motion share threshold')
    parser.add_argument('--ema', type=float, default=0.2, help='EMA coefficient for smoothing')
    parser.add_argument('--margin', type=float, default=1.4, help='Box expansion factor')
    parser.add_argument('--min_det_rate', type=float, default=0.60, help='Min detection rate threshold')
    parser.add_argument('--frames_for_gallery', type=int, default=3, help='Frames per video for gallery')
    parser.add_argument('--exts', default='.mp4,.mov,.mkv,.avi,.webm', help='Video extensions')
    parser.add_argument('--workers', type=int, default=4, help='Number of worker processes')
    parser.add_argument('--sample', type=int, default=20, help='Number of videos to process for testing')
    
    args = parser.parse_args()
    
    # Setup paths
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    reports_dir = Path('reports')
    reports_dir.mkdir(exist_ok=True)
    
    # Parse extensions
    extensions = [e.strip() for e in args.exts.split(',')]
    
    # Find videos
    print("Scanning for videos...")
    all_videos = find_videos(str(input_dir), extensions)
    print(f"Found {len(all_videos)} videos")
    
    if not all_videos:
        print("No videos found!")
        return
    
    # Sample videos for testing
    if args.sample > 0 and len(all_videos) > args.sample:
        videos = random.sample(all_videos, args.sample)
        print(f"Randomly sampled {args.sample} videos for testing")
    else:
        videos = all_videos
    
    # Prepare arguments for multiprocessing
    process_args = [(video, output_dir, input_dir, args.size, args.upsample,
                    args.motion_percentile, args.min_motion_share, args.ema,
                    args.margin, args.min_det_rate, args.frames_for_gallery)
                   for video in videos]
    
    # Process videos
    print(f"Processing {len(videos)} videos with {args.workers} workers...")
    
    with mp.Pool(args.workers) as pool:
        results = list(tqdm(
            pool.imap(process_single_video, process_args),
            total=len(videos),
            desc="Processing videos"
        ))
    
    # Filter results
    valid_results = []
    errors = []
    
    for result in results:
        if 'error' in result:
            errors.append(result['error'])
        else:
            valid_results.append(result)
    
    # Write CSV summary
    with open(args.summary, 'w', newline='') as csvfile:
        fieldnames = ['video_path', 'frames', 'detection_rate', 'iou_with_mouth_bbox',
                     'motion_share', 'heatmap_max', 'chose_fallback']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for result in valid_results:
            writer.writerow({
                'video_path': result['video_path'],
                'frames': result['frames'],
                'detection_rate': result['detection_rate'],
                'iou_with_mouth_bbox': result['iou_with_mouth_bbox'],
                'motion_share': result['motion_share'],
                'heatmap_max': result['heatmap_max'],
                'chose_fallback': result['chose_fallback']
            })

    # Generate HTML gallery
    generate_html_gallery(valid_results, args.gallery)

    print(f"\nProcessing complete!")
    print(f"Successfully processed: {len(valid_results)} videos")
    print(f"Errors: {len(errors)}")
    print(f"Output directory: {output_dir}")
    print(f"Gallery: {args.gallery}")
    print(f"Summary: {args.summary}")

    if errors:
        with open(reports_dir / 'stage1M_skips.txt', 'w') as f:
            f.write("Processing errors:\n")
            for error in errors:
                f.write(f"{error}\n")
        print(f"Error log: reports/stage1M_skips.txt")


if __name__ == '__main__':
    main()
