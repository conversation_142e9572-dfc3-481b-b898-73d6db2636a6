#!/usr/bin/env python3
"""
Simple Manifest Creation Script

Creates training manifests from validation results without copying videos or processing.
This is a simplified version to test the core splitting and manifest generation logic.

Usage:
    python scripts/create_manifests_only.py \
      --validation_csv "reports/validation_flagged_videos.csv" \
      --output_base "datasets"
"""

import argparse
import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict
from datetime import datetime
from typing import Dict, List, Tuple
import logging

from sklearn.model_selection import train_test_split

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Phrase to class mapping
PHRASE_LABELS = {
    'pillow': 0,
    'phone': 1, 
    'doctor': 2,
    'glasses': 3,
    'help': 4,
    'i_need_to_move': 5,
    'my_mouth_is_dry': 6
}

def parse_video_path(video_path: str) -> Dict[str, str]:
    """Parse video path to extract demographics."""
    try:
        path_parts = Path(video_path).parts
        
        # Find the phrase directory (should appear twice)
        phrase = None
        for part in path_parts:
            if part in PHRASE_LABELS:
                phrase = part
                break
        
        if not phrase:
            raise ValueError(f"No valid phrase found in path: {video_path}")
        
        # Extract demographics from path structure
        phrase_idx = None
        for i, part in enumerate(path_parts):
            if part == phrase:
                phrase_idx = i
                break
        
        if phrase_idx is None or phrase_idx + 3 >= len(path_parts):
            raise ValueError(f"Invalid path structure: {video_path}")
        
        age_group = path_parts[phrase_idx + 1]
        gender = path_parts[phrase_idx + 2] 
        ethnicity = path_parts[phrase_idx + 3]
        
        return {
            'phrase': phrase,
            'age_group': age_group,
            'gender': gender,
            'ethnicity': ethnicity,
            'filename': Path(video_path).name
        }
    except Exception as e:
        logger.error(f"Error parsing video path {video_path}: {e}")
        return {
            'phrase': 'unknown',
            'age_group': 'unknown',
            'gender': 'unknown', 
            'ethnicity': 'unknown',
            'filename': Path(video_path).name
        }

def create_stratified_splits(df: pd.DataFrame, test_size: float = 0.15, val_size: float = 0.15, 
                           random_state: int = 42) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """Create stratified train/val/test splits ensuring demographic balance."""
    df = df.copy()
    
    # Try phrase-only stratification (more robust)
    df['strat_key'] = df['phrase']
    strat_counts = df['strat_key'].value_counts()
    min_samples = strat_counts.min()
    
    if min_samples < 2:
        logger.warning("⚠️  Even phrase-only stratification not possible, using random split")
        # Random split without stratification
        train_val_df, test_df = train_test_split(
            df, test_size=test_size, random_state=random_state
        )
        val_size_adjusted = val_size / (1 - test_size)
        train_df, val_df = train_test_split(
            train_val_df, test_size=val_size_adjusted, random_state=random_state
        )
    else:
        logger.info(f"✅ Using phrase-based stratification (min samples: {min_samples})")
        train_val_df, test_df = train_test_split(
            df, test_size=test_size, stratify=df['strat_key'], 
            random_state=random_state
        )
        val_size_adjusted = val_size / (1 - test_size)
        train_df, val_df = train_test_split(
            train_val_df, test_size=val_size_adjusted, stratify=train_val_df['strat_key'],
            random_state=random_state
        )
    
    # Add split labels
    train_df = train_df.copy()
    val_df = val_df.copy()
    test_df = test_df.copy()
    
    train_df['split'] = 'train'
    val_df['split'] = 'val'
    test_df['split'] = 'test'
    
    # Drop temporary stratification key
    for split_df in [train_df, val_df, test_df]:
        if 'strat_key' in split_df.columns:
            split_df.drop('strat_key', axis=1, inplace=True)
    
    return train_df, val_df, test_df

def verify_male_18to39_presence(train_df: pd.DataFrame, dataset_name: str) -> bool:
    """Verify that male 18-39 demographic is present in training set."""
    male_18to39 = train_df[(train_df['gender'] == 'male') & (train_df['age_group'] == '18to39')]
    
    if len(male_18to39) == 0:
        logger.error(f"❌ CRITICAL: No male 18-39 samples in {dataset_name} training set!")
        return False
    
    logger.info(f"✅ {dataset_name} training set contains {len(male_18to39)} male 18-39 samples")
    return True

def generate_split_statistics(train_df: pd.DataFrame, val_df: pd.DataFrame, test_df: pd.DataFrame, 
                             dataset_type: str) -> str:
    """Generate detailed statistics for dataset splits."""
    stats = []
    stats.append(f"\n{dataset_type.upper()} DATASET STATISTICS")
    stats.append("=" * 50)
    
    # Overall counts
    total = len(train_df) + len(val_df) + len(test_df)
    stats.append(f"Total videos: {total:,}")
    stats.append(f"Train: {len(train_df):,} ({len(train_df)/total*100:.1f}%)")
    stats.append(f"Val: {len(val_df):,} ({len(val_df)/total*100:.1f}%)")
    stats.append(f"Test: {len(test_df):,} ({len(test_df)/total*100:.1f}%)")
    
    # Phrase distribution
    stats.append(f"\nPHRASE DISTRIBUTION:")
    stats.append("-" * 20)
    for split_name, split_df in [("Train", train_df), ("Val", val_df), ("Test", test_df)]:
        phrase_counts = split_df['phrase'].value_counts().sort_index()
        stats.append(f"{split_name}:")
        for phrase, count in phrase_counts.items():
            stats.append(f"  {phrase}: {count}")
    
    # Demographic distribution
    stats.append(f"\nDEMOGRAPHIC DISTRIBUTION:")
    stats.append("-" * 25)
    
    # Age groups
    stats.append("Age Groups:")
    for split_name, split_df in [("Train", train_df), ("Val", val_df), ("Test", test_df)]:
        age_counts = split_df['age_group'].value_counts().sort_index()
        stats.append(f"  {split_name}: {dict(age_counts)}")
    
    # Gender
    stats.append("Gender:")
    for split_name, split_df in [("Train", train_df), ("Val", val_df), ("Test", test_df)]:
        gender_counts = split_df['gender'].value_counts().sort_index()
        stats.append(f"  {split_name}: {dict(gender_counts)}")
    
    # Ethnicity
    stats.append("Ethnicity:")
    for split_name, split_df in [("Train", train_df), ("Val", val_df), ("Test", test_df)]:
        ethnicity_counts = split_df['ethnicity'].value_counts().sort_index()
        stats.append(f"  {split_name}: {dict(ethnicity_counts)}")
    
    # Critical demographic check
    stats.append(f"\nCRITICAL DEMOGRAPHIC VERIFICATION:")
    stats.append("-" * 35)
    male_18to39_train = train_df[(train_df['gender'] == 'male') & (train_df['age_group'] == '18to39')]
    stats.append(f"Male 18-39 in training set: {len(male_18to39_train)} samples")
    if len(male_18to39_train) > 0:
        stats.append("✅ REQUIREMENT MET: Male 18-39 demographic present in training")
    else:
        stats.append("❌ REQUIREMENT FAILED: Male 18-39 demographic missing from training")
    
    return "\n".join(stats)

def save_manifests(train_df: pd.DataFrame, val_df: pd.DataFrame, test_df: pd.DataFrame,
                  dataset_type: str, manifests_dir: Path) -> None:
    """Save individual and combined manifests."""
    
    # Add dataset type column
    for df in [train_df, val_df, test_df]:
        df['dataset_type'] = dataset_type
    
    # Save individual split manifests
    train_df.to_csv(manifests_dir / f"manifest_{dataset_type}_train.csv", index=False)
    val_df.to_csv(manifests_dir / f"manifest_{dataset_type}_val.csv", index=False)
    test_df.to_csv(manifests_dir / f"manifest_{dataset_type}_test.csv", index=False)
    
    logger.info(f"✅ Saved {dataset_type} manifests:")
    logger.info(f"  - Train: {len(train_df):,} samples")
    logger.info(f"  - Val: {len(val_df):,} samples") 
    logger.info(f"  - Test: {len(test_df):,} samples")

def main():
    parser = argparse.ArgumentParser(description='Create training manifests from validation results')
    parser.add_argument('--validation_csv', required=True, 
                       help='Path to validation results CSV')
    parser.add_argument('--output_base', default='datasets',
                       help='Base output directory for manifests')
    parser.add_argument('--test_size', type=float, default=0.15,
                       help='Test set proportion')
    parser.add_argument('--val_size', type=float, default=0.15,
                       help='Validation set proportion')
    parser.add_argument('--random_state', type=int, default=42,
                       help='Random state for reproducible splits')
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting manifest creation...")
    
    # Create output directories
    output_base = Path(args.output_base)
    manifests_dir = output_base / "manifests"
    manifests_dir.mkdir(parents=True, exist_ok=True)
    
    # Load validation results
    logger.info(f"📊 Loading validation results from {args.validation_csv}")
    validation_df = pd.read_csv(args.validation_csv)
    
    # Parse demographics from video paths
    logger.info("🔍 Parsing demographics from video paths...")
    demographics = []
    for _, row in validation_df.iterrows():
        demo = parse_video_path(row['video_path'])
        demo.update({
            'video_path': row['video_path'],
            'frames': row['frames'],
            'duration_ratio': row['duration_ratio'],
            'motion_share': row['motion_share'],
            'heatmap_max': row['heatmap_max'],
            'issues': row['issues'],
            'recommendation': row['recommendation']
        })
        demographics.append(demo)
    
    demo_df = pd.DataFrame(demographics)
    
    # Split into clean and flagged datasets
    clean_df = demo_df[demo_df['recommendation'] == 'keep'].copy()
    flagged_df = demo_df[demo_df['recommendation'].isin(['review', 'remove'])].copy()
    
    logger.info(f"📈 Dataset sizes:")
    logger.info(f"  Clean dataset: {len(clean_df):,} videos")
    logger.info(f"  Flagged dataset: {len(flagged_df):,} videos")
    
    # Create stratified splits for both datasets
    logger.info("🎯 Creating stratified demographic splits...")
    
    # Clean dataset splits
    clean_train, clean_val, clean_test = create_stratified_splits(
        clean_df, args.test_size, args.val_size, args.random_state
    )
    
    # Flagged dataset splits  
    flagged_train, flagged_val, flagged_test = create_stratified_splits(
        flagged_df, args.test_size, args.val_size, args.random_state
    )
    
    # Verify critical demographic requirements
    logger.info("✅ Verifying demographic requirements...")
    clean_male_ok = verify_male_18to39_presence(clean_train, "clean")
    flagged_male_ok = verify_male_18to39_presence(flagged_train, "flagged")
    
    if not (clean_male_ok and flagged_male_ok):
        logger.error("❌ Critical demographic requirements not met!")
        return 1
    
    # Save individual manifests
    logger.info("📝 Generating training manifests...")
    save_manifests(clean_train, clean_val, clean_test, "clean", manifests_dir)
    save_manifests(flagged_train, flagged_val, flagged_test, "flagged", manifests_dir)
    
    # Create combined master manifest
    all_splits = pd.concat([
        clean_train, clean_val, clean_test,
        flagged_train, flagged_val, flagged_test
    ], ignore_index=True)
    all_splits.to_csv(manifests_dir / "manifest_combined_all.csv", index=False)
    
    # Generate statistics
    logger.info("📊 Generating split statistics...")
    clean_stats_text = generate_split_statistics(clean_train, clean_val, clean_test, "clean")
    flagged_stats_text = generate_split_statistics(flagged_train, flagged_val, flagged_test, "flagged")
    
    with open(manifests_dir / "split_statistics_summary.txt", 'w') as f:
        f.write(clean_stats_text)
        f.write("\n\n")
        f.write(flagged_stats_text)
    
    # Final summary
    logger.info("=" * 60)
    logger.info("✅ MANIFEST CREATION COMPLETE")
    logger.info("=" * 60)
    logger.info(f"📝 Manifests directory: {manifests_dir}")
    logger.info(f"📊 Clean dataset: {len(clean_df):,} videos")
    logger.info(f"📊 Flagged dataset: {len(flagged_df):,} videos")
    logger.info("🎯 Ready for video processing and SageMaker migration!")
    
    return 0

if __name__ == "__main__":
    exit(main())
