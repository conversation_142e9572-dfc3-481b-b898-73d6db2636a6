#!/usr/bin/env python3
"""
Create Suspect Gallery

Creates an HTML gallery showing only the low-motion candidate videos
for manual inspection and quality review.

Usage:
    python scripts/create_suspect_gallery.py \
      --candidates "reports/low_motion_candidates.csv" \
      --input_dir "data/stage1M_motion_refined_full" \
      --output_gallery "reports/suspect_gallery.html" \
      --max_videos 50
"""

import argparse
import csv
import cv2
import numpy as np
import base64
from pathlib import Path
from jinja2 import Template
from typing import List, Dict


def extract_sample_frames(video_path: Path, num_frames: int = 3) -> List[np.ndarray]:
    """Extract evenly spaced sample frames from video."""
    cap = cv2.VideoCapture(str(video_path))
    if not cap.isOpened():
        return []
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total_frames <= 0:
        total_frames = 100  # Fallback
    
    # Get evenly spaced frame indices
    if total_frames <= num_frames:
        indices = list(range(total_frames))
    else:
        indices = np.linspace(0, total_frames - 1, num_frames, dtype=int)
    
    frames = []
    for idx in indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
        ret, frame = cap.read()
        if ret:
            frames.append(frame)
    
    cap.release()
    return frames


def create_suspect_gallery(candidates: List[Dict], input_dir: Path, max_videos: int) -> str:
    """Create HTML gallery for suspect videos."""
    
    # Limit number of videos to display
    display_candidates = candidates[:max_videos]
    
    # Extract sample frames for each video
    for candidate in display_candidates:
        video_path = Path(candidate['video_path'])
        # Convert to motion-refined path
        refined_path = input_dir / video_path.relative_to(Path('data/stage1_cropped_top7'))
        
        if refined_path.exists():
            frames = extract_sample_frames(refined_path, 3)
            
            # Convert frames to base64
            b64_frames = []
            for frame in frames:
                _, buffer = cv2.imencode('.png', frame)
                b64_frames.append(base64.b64encode(buffer).decode('utf-8'))
            
            candidate['sample_frames'] = b64_frames
            candidate['refined_path'] = str(refined_path)
        else:
            candidate['sample_frames'] = []
            candidate['refined_path'] = "File not found"
    
    html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>Suspect Videos - Low Motion Candidates</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { text-align: center; margin-bottom: 30px; }
        .stats { background: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .video-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(600px, 1fr)); gap: 20px; }
        .video-card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .remove { border-left: 5px solid #ff4444; }
        .review { border-left: 5px solid #ffaa44; }
        .video-title { font-weight: bold; margin-bottom: 10px; word-break: break-all; font-size: 14px; }
        .thumbnails { display: flex; gap: 10px; margin: 15px 0; flex-wrap: wrap; }
        .thumbnail { max-width: 96px; max-height: 96px; border: 1px solid #ddd; }
        .metrics { display: flex; gap: 10px; margin: 10px 0; flex-wrap: wrap; }
        .metric { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .metric.bad { background: #ffe8e8; color: #5a2d2d; }
        .metric.warning { background: #fff3cd; color: #856404; }
        .metric.neutral { background: #f0f0f0; color: #333; }
        .reasons { font-size: 12px; color: #666; margin-top: 5px; background: #f8f9fa; padding: 8px; border-radius: 4px; }
        .recommendation { font-weight: bold; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .rec-remove { background: #ff4444; color: white; }
        .rec-review { background: #ffaa44; color: white; }
        .quality-score { font-size: 14px; font-weight: bold; }
        .score-bad { color: #dc3545; }
        .score-warning { color: #fd7e14; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Suspect Videos - Low Motion Candidates</h1>
        <p>Videos flagged for potential removal or review due to insufficient motion patterns</p>
    </div>
    
    <div class="stats">
        <h3>Summary Statistics</h3>
        <p><strong>Total suspects:</strong> {{ candidates|length }}</p>
        <p><strong>Recommended for removal:</strong> {{ candidates|selectattr('recommendation', 'equalto', 'remove')|list|length }}</p>
        <p><strong>Recommended for review:</strong> {{ candidates|selectattr('recommendation', 'equalto', 'review')|list|length }}</p>
        <p><strong>Displaying:</strong> {{ display_count }} videos (sorted by quality score, worst first)</p>
    </div>
    
    <div class="video-grid">
    {% for candidate in candidates %}
        <div class="video-card {{ candidate.recommendation }}">
            <div class="video-title">{{ candidate.video_path }}</div>
            
            <div class="thumbnails">
                {% if candidate.sample_frames %}
                    {% for frame in candidate.sample_frames %}
                    <img src="data:image/png;base64,{{ frame }}" class="thumbnail" alt="Sample frame">
                    {% endfor %}
                {% else %}
                    <p style="color: #999; font-style: italic;">No frames available</p>
                {% endif %}
            </div>
            
            <div class="metrics">
                <span class="metric bad">
                    Motion Share: {{ "%.3f"|format(candidate.motion_share) }}
                </span>
                <span class="metric {{ 'bad' if candidate.heatmap_max < 100 else 'warning' }}">
                    Heatmap Max: {{ "%.1f"|format(candidate.heatmap_max) }}
                </span>
                <span class="metric {{ 'bad' if candidate.chose_fallback else 'neutral' }}">
                    {{ 'Fallback Crop' if candidate.chose_fallback else 'Motion Guided' }}
                </span>
                <span class="metric bad">
                    Detection: {{ "%.3f"|format(candidate.detection_rate) }}
                </span>
                <span class="recommendation rec-{{ candidate.recommendation }}">
                    {{ candidate.recommendation.upper() }}
                </span>
            </div>
            
            <div class="quality-score {{ 'score-bad' if candidate.quality_score < 0 else 'score-warning' }}">
                Quality Score: {{ "%.1f"|format(candidate.quality_score) }}
            </div>
            
            <div class="reasons">
                <strong>Issues:</strong> {{ candidate.reasons.replace(';', ', ') }}
            </div>
            
            <div style="font-size: 11px; color: #999; margin-top: 10px;">
                <strong>Refined Path:</strong> {{ candidate.refined_path }}
            </div>
        </div>
    {% endfor %}
    </div>
    
    <div style="margin-top: 30px; padding: 20px; background: white; border-radius: 8px;">
        <h3>Legend</h3>
        <p><span style="color: #ff4444;">●</span> <strong>Remove:</strong> Videos with multiple quality issues</p>
        <p><span style="color: #ffaa44;">●</span> <strong>Review:</strong> Videos with single quality issues that may be salvageable</p>
        <p><strong>Quality Score:</strong> Composite score based on motion share (40%), heatmap intensity (30%), detection rate (20%), and fallback penalty (10%)</p>
    </div>
</body>
</html>
"""
    
    template = Template(html_template)
    html_content = template.render(
        candidates=display_candidates,
        display_count=len(display_candidates)
    )
    
    return html_content


def main():
    parser = argparse.ArgumentParser(description='Create Suspect Gallery for Low Motion Videos')
    parser.add_argument('--candidates', required=True, help='CSV file with low motion candidates')
    parser.add_argument('--input_dir', required=True, help='Directory with motion-refined videos')
    parser.add_argument('--output_gallery', required=True, help='Output HTML gallery path')
    parser.add_argument('--max_videos', type=int, default=50, help='Maximum videos to display')
    
    args = parser.parse_args()
    
    # Load candidates
    print(f"Loading candidates from {args.candidates}...")
    candidates = []
    with open(args.candidates, 'r') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            # Convert string values to appropriate types
            row['motion_share'] = float(row['motion_share'])
            row['heatmap_max'] = float(row['heatmap_max'])
            row['chose_fallback'] = row['chose_fallback'].lower() == 'true'
            row['detection_rate'] = float(row['detection_rate'])
            row['quality_score'] = float(row['quality_score'])
            candidates.append(row)
    
    print(f"Loaded {len(candidates)} candidates")
    
    # Create gallery
    print(f"Creating suspect gallery...")
    input_dir = Path(args.input_dir)
    html_content = create_suspect_gallery(candidates, input_dir, args.max_videos)
    
    # Write gallery
    with open(args.output_gallery, 'w') as f:
        f.write(html_content)
    
    print(f"Suspect gallery created: {args.output_gallery}")
    print(f"Displaying {min(len(candidates), args.max_videos)} videos")


if __name__ == '__main__':
    main()
