#!/usr/bin/env python3
"""
Simple Balanced Dataset Creation Script

Creates a balanced version of the clean dataset through controlled data augmentation
using only the videos that actually exist in the organized dataset.

Usage:
    python scripts/create_balanced_dataset_simple.py \
      --source_dataset "datasets/dataset_clean_2025.09.08" \
      --output_dataset "datasets/dataset_clean_balanced_2025.09.08" \
      --manifests_dir "datasets/manifests"
"""

import argparse
import pandas as pd
import numpy as np
import cv2
import shutil
from pathlib import Path
from collections import defaultdict
import logging
from datetime import datetime
import json
from tqdm import tqdm
import random

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def brightness_adjustment(frames: np.ndarray, brightness_factor: float = None) -> np.ndarray:
    """Apply brightness adjustment (±10-15%)."""
    if brightness_factor is None:
        brightness_factor = random.uniform(0.85, 1.15)
    
    # Apply brightness adjustment
    adjusted = frames.astype(np.float32) * brightness_factor
    adjusted = np.clip(adjusted, 0, 255).astype(np.uint8)
    return adjusted

def augment_video_simple(video_path: str, output_path: str, technique: str = 'brightness_adj') -> bool:
    """Augment a single video using brightness adjustment."""
    try:
        # Read video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"Cannot open video: {video_path}")
            return False
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS) or 30.0
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        cap.release()
        
        if not frames:
            logger.error(f"No frames in video: {video_path}")
            return False
        
        frames = np.array(frames)
        
        # Apply brightness adjustment
        augmented_frames = brightness_adjustment(frames)
        
        # Create output directory
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Write augmented video
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
        
        for frame in augmented_frames:
            out.write(frame)
        
        out.release()
        return True
        
    except Exception as e:
        logger.error(f"Error augmenting {video_path}: {e}")
        return False

def scan_existing_videos(dataset_path: Path) -> dict:
    """Scan the dataset to find actually existing videos by split and phrase."""
    existing_videos = defaultdict(lambda: defaultdict(list))
    
    for split in ['train', 'val', 'test']:
        split_path = dataset_path / split
        if split_path.exists():
            # Find all mp4 files
            for video_file in split_path.rglob('*.mp4'):
                # Extract phrase from path
                path_parts = video_file.parts
                phrase = None
                for part in path_parts:
                    if part in ['pillow', 'phone', 'doctor', 'glasses', 'help', 'i_need_to_move', 'my_mouth_is_dry']:
                        phrase = part
                        break
                
                if phrase:
                    existing_videos[split][phrase].append(str(video_file))
    
    return existing_videos

def create_balanced_dataset(source_dataset: Path, output_dataset: Path, manifests_dir: Path):
    """Create balanced dataset by augmenting existing videos."""
    
    # Scan existing videos
    logger.info("🔍 Scanning existing videos...")
    existing_videos = scan_existing_videos(source_dataset)
    
    # Calculate current counts and targets
    logger.info("📊 Calculating augmentation requirements...")
    augmentation_plan = {}
    
    for split in ['train', 'val', 'test']:
        if split not in existing_videos:
            continue
            
        phrase_counts = {phrase: len(videos) for phrase, videos in existing_videos[split].items()}
        if not phrase_counts:
            continue
            
        target_count = max(phrase_counts.values())
        
        logger.info(f"{split.upper()} split:")
        logger.info(f"  Target count per phrase: {target_count}")
        
        augmentation_plan[split] = {}
        for phrase, current_count in phrase_counts.items():
            required_augs = target_count - current_count
            augmentation_plan[split][phrase] = {
                'current_count': current_count,
                'target_count': target_count,
                'required_augmentations': required_augs,
                'videos': existing_videos[split][phrase]
            }
            logger.info(f"    {phrase}: {current_count} → {target_count} (+{required_augs})")
    
    # Create output dataset directory
    output_dataset.mkdir(parents=True, exist_ok=True)
    
    # Copy all original videos
    logger.info("📁 Copying original videos...")
    copy_stats = {'copied': 0, 'failed': 0}
    
    for split in ['train', 'val', 'test']:
        split_source = source_dataset / split
        split_output = output_dataset / split
        
        if split_source.exists():
            for video_file in tqdm(list(split_source.rglob('*.mp4')), desc=f"Copying {split} videos"):
                rel_path = video_file.relative_to(split_source)
                output_path = split_output / rel_path
                
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                try:
                    shutil.copy2(video_file, output_path)
                    copy_stats['copied'] += 1
                except Exception as e:
                    logger.error(f"Failed to copy {video_file}: {e}")
                    copy_stats['failed'] += 1
    
    logger.info(f"Copied {copy_stats['copied']} original videos")
    
    # Perform augmentations
    logger.info("🔄 Creating augmented videos...")
    aug_stats = {'created': 0, 'failed': 0}
    all_augmented_entries = defaultdict(list)
    
    for split, split_plan in augmentation_plan.items():
        for phrase, phrase_plan in split_plan.items():
            required_augs = phrase_plan['required_augmentations']
            if required_augs <= 0:
                continue
                
            available_videos = phrase_plan['videos']
            if not available_videos:
                continue
            
            logger.info(f"Augmenting {phrase} in {split}: need {required_augs} augmentations from {len(available_videos)} videos")
            
            # Create augmentations
            for i in range(required_augs):
                # Select source video (cycle through available videos)
                source_video = available_videos[i % len(available_videos)]
                source_path = Path(source_video)
                
                # Create augmented filename
                aug_suffix = f"aug{(i // len(available_videos)) + 1}"
                aug_filename = source_path.stem + f'_{aug_suffix}.mp4'
                
                # Construct output path
                rel_path = source_path.relative_to(source_dataset)
                output_path = output_dataset / rel_path.parent / aug_filename
                
                # Augment video
                success = augment_video_simple(str(source_path), str(output_path))
                
                if success:
                    aug_stats['created'] += 1
                    
                    # Create manifest entry
                    aug_entry = {
                        'video_path': str(rel_path.parent / aug_filename),
                        'phrase': phrase,
                        'split': split,
                        'augmentation_type': 'brightness_adj',
                        'source_video': str(rel_path),
                        'filename': aug_filename
                    }
                    all_augmented_entries[split].append(aug_entry)
                else:
                    aug_stats['failed'] += 1
    
    logger.info(f"Created {aug_stats['created']} augmented videos, {aug_stats['failed']} failed")
    
    # Create updated manifests
    logger.info("📝 Creating balanced manifests...")
    
    for split in ['train', 'val', 'test']:
        # Load original manifest
        manifest_path = manifests_dir / f"manifest_clean_{split}.csv"
        if manifest_path.exists():
            original_df = pd.read_csv(manifest_path)
            
            # Filter to only existing videos
            existing_paths = set()
            split_path = source_dataset / split
            if split_path.exists():
                for video_file in split_path.rglob('*.mp4'):
                    rel_path = video_file.relative_to(source_dataset)
                    existing_paths.add(str(rel_path))
            
            # Filter original manifest to existing videos
            original_df['relative_path'] = original_df['video_path'].apply(
                lambda x: str(Path(x).relative_to(Path(x).parts[0]))
            )
            filtered_df = original_df[original_df['relative_path'].isin(existing_paths)].copy()
            filtered_df['augmentation_type'] = 'original'
            
            # Add augmented entries
            if split in all_augmented_entries and all_augmented_entries[split]:
                aug_df = pd.DataFrame(all_augmented_entries[split])
                
                # Add missing columns from original manifest
                for col in filtered_df.columns:
                    if col not in aug_df.columns:
                        if col in ['age_group', 'gender', 'ethnicity']:
                            aug_df[col] = 'unknown'  # Will be filled from source
                        else:
                            aug_df[col] = None
                
                # Combine original and augmented
                balanced_df = pd.concat([filtered_df, aug_df], ignore_index=True)
            else:
                balanced_df = filtered_df
            
            # Sort by phrase and filename
            balanced_df = balanced_df.sort_values(['phrase', 'filename'])
            
            # Save balanced manifest
            output_manifest = manifests_dir / f"manifest_clean_balanced_{split}.csv"
            balanced_df.to_csv(output_manifest, index=False)
            
            logger.info(f"✅ Created balanced {split} manifest: {len(balanced_df)} samples")
    
    # Create dataset metadata
    logger.info("📋 Creating dataset metadata...")
    metadata = {
        'dataset_name': f"dataset_clean_balanced_{datetime.now().strftime('%Y.%m.%d')}",
        'dataset_type': 'clean_balanced',
        'creation_date': datetime.now().isoformat(),
        'version': '1.0',
        'description': 'ICU Lipreading clean dataset with balanced phrase distribution through controlled augmentation',
        'original_videos': copy_stats['copied'],
        'augmented_videos': aug_stats['created'],
        'total_videos': copy_stats['copied'] + aug_stats['created'],
        'failed_copies': copy_stats['failed'],
        'failed_augmentations': aug_stats['failed'],
        'phrases': ['pillow', 'phone', 'doctor', 'glasses', 'help', 'i_need_to_move', 'my_mouth_is_dry'],
        'num_classes': 7,
        'splits': ['train', 'val', 'test'],
        'augmentation_policy': {
            'strategy': 'phrase_balancing',
            'target': 'highest_count_per_split',
            'primary_technique': 'brightness_adj',
            'expansion_factor': (copy_stats['copied'] + aug_stats['created']) / copy_stats['copied'] if copy_stats['copied'] > 0 else 1.0
        }
    }
    
    with open(output_dataset / 'dataset_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)
    
    return copy_stats, aug_stats, augmentation_plan

def main():
    parser = argparse.ArgumentParser(description='Create balanced dataset through simple augmentation')
    parser.add_argument('--source_dataset', default='datasets/dataset_clean_2025.09.08',
                       help='Source clean dataset directory')
    parser.add_argument('--output_dataset', default='datasets/dataset_clean_balanced_2025.09.08',
                       help='Output balanced dataset directory')
    parser.add_argument('--manifests_dir', default='datasets/manifests',
                       help='Directory containing manifest files')
    
    args = parser.parse_args()
    
    logger.info("🚀 Starting simple balanced dataset creation...")
    
    source_dataset = Path(args.source_dataset)
    output_dataset = Path(args.output_dataset)
    manifests_dir = Path(args.manifests_dir)
    
    if not source_dataset.exists():
        logger.error(f"Source dataset not found: {source_dataset}")
        return 1
    
    # Create balanced dataset
    copy_stats, aug_stats, aug_plan = create_balanced_dataset(source_dataset, output_dataset, manifests_dir)
    
    # Generate summary
    total_original = copy_stats['copied']
    total_balanced = copy_stats['copied'] + aug_stats['created']
    
    logger.info("=" * 60)
    logger.info("✅ BALANCED DATASET CREATION COMPLETE")
    logger.info("=" * 60)
    logger.info(f"📁 Balanced dataset: {output_dataset}")
    logger.info(f"📊 Original videos: {total_original:,}")
    logger.info(f"📊 Augmented videos: {aug_stats['created']:,}")
    logger.info(f"📊 Total videos: {total_balanced:,}")
    logger.info(f"📊 Expansion factor: {total_balanced/total_original:.2f}x")
    logger.info("🚀 Ready for SageMaker training!")
    
    return 0

if __name__ == "__main__":
    exit(main())
