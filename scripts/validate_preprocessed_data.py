#!/usr/bin/env python3
"""
Validation Script for Preprocessed Classifier Data

Validates the preprocessed numpy arrays and demonstrates how to load them
for classifier training.

Usage:
    python scripts/validate_preprocessed_data.py --data_dir data/classifier_ready
"""

import argparse
import numpy as np
import pandas as pd
import json
import os
from pathlib import Path
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple
import cv2


def load_labels_mapping(labels_path: str) -> Dict[str, int]:
    """Load the phrase to class ID mapping."""
    with open(labels_path, 'r') as f:
        return json.load(f)


def load_training_manifest(manifest_path: str) -> pd.DataFrame:
    """Load the training manifest CSV."""
    return pd.read_csv(manifest_path)


def validate_numpy_arrays(data_dir: str) -> Dict[str, np.ndarray]:
    """Load and validate all preprocessed numpy arrays."""
    preprocessed_dir = Path(data_dir) / "preprocessed_videos"
    
    if not preprocessed_dir.exists():
        raise FileNotFoundError(f"Preprocessed videos directory not found: {preprocessed_dir}")
    
    arrays = {}
    
    # Load all .npy files
    for npy_file in preprocessed_dir.glob("*.npy"):
        phrase = npy_file.stem.replace("_preprocessed", "")
        
        try:
            array = np.load(str(npy_file))
            arrays[phrase] = array
            print(f"✅ Loaded {phrase}: shape={array.shape}, dtype={array.dtype}")
            
            # Validate array properties
            if len(array.shape) != 4:
                print(f"⚠️  Unexpected shape for {phrase}: {array.shape} (expected 4D)")
            
            if array.dtype != np.float32:
                print(f"⚠️  Unexpected dtype for {phrase}: {array.dtype} (expected float32)")
            
            if array.min() < 0 or array.max() > 1:
                print(f"⚠️  Values out of [0,1] range for {phrase}: [{array.min():.3f}, {array.max():.3f}]")
            
        except Exception as e:
            print(f"❌ Failed to load {phrase}: {e}")
    
    return arrays


def analyze_data_statistics(arrays: Dict[str, np.ndarray], manifest: pd.DataFrame):
    """Analyze and display data statistics."""
    print("\n📊 Data Statistics:")
    print("=" * 60)
    
    # Overall statistics
    total_frames = sum(array.shape[0] for array in arrays.values())
    total_size_mb = sum(array.nbytes for array in arrays.values()) / (1024 * 1024)
    
    print(f"📹 Total videos: {len(arrays)}")
    print(f"🎬 Total frames: {total_frames}")
    print(f"💾 Total size: {total_size_mb:.1f} MB")
    
    # Per-phrase statistics
    print(f"\n📋 Per-phrase breakdown:")
    for phrase, array in arrays.items():
        manifest_row = manifest[manifest['phrase'] == phrase].iloc[0]
        
        print(f"  {phrase}:")
        print(f"    Shape: {array.shape}")
        print(f"    Size: {array.nbytes / (1024 * 1024):.2f} MB")
        print(f"    Range: [{array.min():.3f}, {array.max():.3f}]")
        print(f"    Mean: {array.mean():.3f}")
        print(f"    Std: {array.std():.3f}")
        print(f"    Manifest mean: {manifest_row['mean_value']:.3f}")
        print(f"    Manifest std: {manifest_row['std_value']:.3f}")
    
    # Consistency checks
    print(f"\n🔍 Consistency Checks:")
    shapes = [array.shape for array in arrays.values()]
    if len(set(shapes)) == 1:
        print(f"✅ All arrays have consistent shape: {shapes[0]}")
    else:
        print(f"⚠️  Inconsistent shapes found: {set(shapes)}")
    
    dtypes = [array.dtype for array in arrays.values()]
    if len(set(dtypes)) == 1:
        print(f"✅ All arrays have consistent dtype: {dtypes[0]}")
    else:
        print(f"⚠️  Inconsistent dtypes found: {set(dtypes)}")


def visualize_sample_frames(arrays: Dict[str, np.ndarray], output_dir: str):
    """Create visualization of sample frames from each phrase."""
    print(f"\n🎨 Creating sample frame visualizations...")
    
    # Create visualization directory
    viz_dir = Path(output_dir) / "visualizations"
    viz_dir.mkdir(exist_ok=True)
    
    # Create a grid visualization
    n_phrases = len(arrays)
    n_frames_per_phrase = 3  # Show first, middle, last frame
    
    fig, axes = plt.subplots(n_phrases, n_frames_per_phrase, 
                            figsize=(12, 2 * n_phrases))
    
    if n_phrases == 1:
        axes = axes.reshape(1, -1)
    
    for i, (phrase, array) in enumerate(sorted(arrays.items())):
        # Select frames to show
        total_frames = array.shape[0]
        frame_indices = [0, total_frames // 2, total_frames - 1]
        
        for j, frame_idx in enumerate(frame_indices):
            frame = array[frame_idx]
            
            # Display frame
            axes[i, j].imshow(frame)
            axes[i, j].set_title(f"{phrase}\nFrame {frame_idx}")
            axes[i, j].axis('off')
    
    plt.tight_layout()
    
    # Save visualization
    viz_path = viz_dir / "sample_frames.png"
    plt.savefig(str(viz_path), dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"💾 Sample frames saved: {viz_path}")
    
    # Create individual phrase visualizations
    for phrase, array in arrays.items():
        # Create a montage of frames
        n_frames = min(array.shape[0], 12)  # Show up to 12 frames
        cols = 4
        rows = (n_frames + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(12, 3 * rows))
        if rows == 1:
            axes = axes.reshape(1, -1)
        
        for idx in range(n_frames):
            row = idx // cols
            col = idx % cols
            
            frame = array[idx]
            axes[row, col].imshow(frame)
            axes[row, col].set_title(f"Frame {idx}")
            axes[row, col].axis('off')
        
        # Hide unused subplots
        for idx in range(n_frames, rows * cols):
            row = idx // cols
            col = idx % cols
            axes[row, col].axis('off')
        
        plt.suptitle(f"{phrase.replace('_', ' ').title()} - Frame Sequence")
        plt.tight_layout()
        
        # Save phrase visualization
        phrase_viz_path = viz_dir / f"{phrase}_frames.png"
        plt.savefig(str(phrase_viz_path), dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"💾 {phrase} frames saved: {phrase_viz_path}")


def demonstrate_data_loading(data_dir: str):
    """Demonstrate how to load the data for classifier training."""
    print(f"\n🚀 Data Loading Demonstration:")
    print("=" * 60)
    
    # Load labels and manifest
    labels_path = Path(data_dir) / "labels.json"
    manifest_path = Path(data_dir) / "training_manifest.csv"
    
    labels_mapping = load_labels_mapping(str(labels_path))
    manifest = load_training_manifest(str(manifest_path))
    
    print(f"📋 Labels mapping: {labels_mapping}")
    print(f"📊 Manifest shape: {manifest.shape}")
    
    # Demonstrate loading for training
    print(f"\n💡 Example training data loading:")
    
    X_data = []  # Features
    y_data = []  # Labels
    
    for _, row in manifest.iterrows():
        # Load numpy array
        array = np.load(row['file_path'])
        
        # Add to training data
        X_data.append(array)
        y_data.append(row['class_id'])
        
        print(f"  Loaded {row['phrase']}: shape={array.shape}, label={row['class_id']}")
    
    # Convert to numpy arrays
    X = np.array(X_data)  # Shape: (n_samples, frames, height, width, channels)
    y = np.array(y_data)  # Shape: (n_samples,)
    
    print(f"\n📊 Training data ready:")
    print(f"  X shape: {X.shape}")
    print(f"  y shape: {y.shape}")
    print(f"  X dtype: {X.dtype}")
    print(f"  y dtype: {y.dtype}")
    print(f"  X range: [{X.min():.3f}, {X.max():.3f}]")
    print(f"  y range: [{y.min()}, {y.max()}]")
    
    # Show class distribution
    unique, counts = np.unique(y, return_counts=True)
    print(f"\n📈 Class distribution:")
    for class_id, count in zip(unique, counts):
        phrase = [k for k, v in labels_mapping.items() if v == class_id][0]
        print(f"  Class {class_id} ({phrase}): {count} samples")
    
    return X, y, labels_mapping


def main():
    parser = argparse.ArgumentParser(description='Validate preprocessed classifier data')
    parser.add_argument('--data_dir', default='data/classifier_ready',
                       help='Directory containing preprocessed data')
    parser.add_argument('--create_visualizations', action='store_true', default=True,
                       help='Create sample frame visualizations')
    
    args = parser.parse_args()
    
    print("🔍 Validating Preprocessed Classifier Data")
    print(f"📁 Data directory: {args.data_dir}")
    
    # Check if data directory exists
    if not os.path.exists(args.data_dir):
        print(f"❌ Data directory not found: {args.data_dir}")
        print("💡 Please run the preprocessing script first")
        return 1
    
    try:
        # Load and validate numpy arrays
        print("\n📦 Loading numpy arrays...")
        arrays = validate_numpy_arrays(args.data_dir)
        
        if not arrays:
            print("❌ No valid arrays found!")
            return 1
        
        # Load manifest
        manifest_path = Path(args.data_dir) / "training_manifest.csv"
        manifest = load_training_manifest(str(manifest_path))
        
        # Analyze statistics
        analyze_data_statistics(arrays, manifest)
        
        # Create visualizations
        if args.create_visualizations:
            try:
                visualize_sample_frames(arrays, args.data_dir)
            except Exception as e:
                print(f"⚠️  Visualization failed: {e}")
        
        # Demonstrate data loading
        X, y, labels_mapping = demonstrate_data_loading(args.data_dir)
        
        print(f"\n✅ Validation complete!")
        print(f"🎯 Ready for classifier training with {len(arrays)} phrases")
        print(f"📊 Total training samples: {len(X)}")
        print(f"🎬 Frame dimensions: {X.shape[1:]} (frames, height, width, channels)")
        
        return 0
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return 1


if __name__ == '__main__':
    import sys
    sys.exit(main())
