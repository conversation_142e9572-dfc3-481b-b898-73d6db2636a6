#!/usr/bin/env python3
"""
Video Validation Script

Identifies and removes problematic videos from the cleaned motion-guided dataset
by detecting multiple phrase repetitions and false positive motion detection.

Primary Issues to Detect:
1. Multiple phrase repetitions: Videos where speaker repeated target phrase 2+ times
2. False positive motion detection: Videos with lip motion that doesn't correspond 
   to the correct target phrase

Usage:
    python scripts/validate_videos.py \
      --dataset_dir "data/stage1M_motion_refined_full" \
      --summary_csv "reports/stage1M_full_summary.csv" \
      --output_flagged "reports/validation_flagged_videos.csv" \
      --output_summary "reports/validation_summary.txt" \
      --flagged_dir "data/validation_flagged" \
      --gallery "reports/validation_gallery.html" \
      --workers 4
"""

import argparse
import csv
import pandas as pd
import numpy as np
import cv2
import os
import shutil
from pathlib import Path
from collections import defaultdict, Counter
from concurrent.futures import ProcessPoolExecutor, as_completed
from tqdm import tqdm
import json
from typing import Dict, List, Tuple, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Expected phrase durations (in frames at 30fps) - baseline estimates
PHRASE_EXPECTED_DURATIONS = {
    'pillow': 45,           # ~1.5 seconds
    'phone': 40,            # ~1.3 seconds  
    'doctor': 50,           # ~1.7 seconds
    'glasses': 55,          # ~1.8 seconds
    'help': 35,             # ~1.2 seconds
    'i_need_to_move': 90,   # ~3.0 seconds
    'my_mouth_is_dry': 100  # ~3.3 seconds
}

def extract_phrase_from_path(video_path: str) -> str:
    """Extract phrase name from video path."""
    path_parts = Path(video_path).parts
    for phrase in PHRASE_EXPECTED_DURATIONS.keys():
        if phrase in path_parts:
            return phrase
    return 'unknown'

def calculate_duration_ratio(frames: int, phrase: str) -> float:
    """Calculate ratio of actual duration to expected duration."""
    expected = PHRASE_EXPECTED_DURATIONS.get(phrase, 60)
    return frames / expected

def analyze_motion_bursts(video_path: str, motion_share: float, heatmap_max: float) -> Dict:
    """
    Analyze motion patterns to detect multiple discrete bursts (repetitions)
    and sustained irregular motion (false positives).
    """
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return {'error': f'Could not open video: {video_path}'}
        
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            frames.append(frame)
        cap.release()
        
        if len(frames) < 10:
            return {'motion_bursts': 0, 'burst_pattern_score': 0.0, 'irregularity_score': 0.0}
        
        # Convert to grayscale and compute frame-by-frame motion
        motion_timeline = []
        prev_gray = None
        
        for frame in frames:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            if prev_gray is not None:
                # Compute optical flow magnitude
                flow = cv2.calcOpticalFlowPyrLK(prev_gray, gray, 
                                               np.array([[gray.shape[1]//2, gray.shape[0]//2]], dtype=np.float32),
                                               None)
                if flow[0] is not None:
                    motion_mag = np.linalg.norm(flow[0] - np.array([[gray.shape[1]//2, gray.shape[0]//2]]))
                else:
                    motion_mag = 0.0
                motion_timeline.append(motion_mag)
            
            prev_gray = gray
        
        if not motion_timeline:
            return {'motion_bursts': 0, 'burst_pattern_score': 0.0, 'irregularity_score': 0.0}
        
        # Smooth motion timeline
        motion_array = np.array(motion_timeline)
        if len(motion_array) > 5:
            motion_smooth = np.convolve(motion_array, np.ones(5)/5, mode='same')
        else:
            motion_smooth = motion_array
        
        # Detect motion bursts (peaks above threshold)
        if np.max(motion_smooth) > 0:
            threshold = np.percentile(motion_smooth, 75)
            above_threshold = motion_smooth > threshold
            
            # Count discrete bursts (connected components)
            burst_count = 0
            in_burst = False
            burst_lengths = []
            current_burst_length = 0
            
            for active in above_threshold:
                if active and not in_burst:
                    burst_count += 1
                    in_burst = True
                    current_burst_length = 1
                elif active and in_burst:
                    current_burst_length += 1
                elif not active and in_burst:
                    burst_lengths.append(current_burst_length)
                    in_burst = False
                    current_burst_length = 0
            
            if in_burst:
                burst_lengths.append(current_burst_length)
        else:
            burst_count = 0
            burst_lengths = []
        
        # Calculate burst pattern score (higher = more likely repetition)
        # More conservative: require significant separation between bursts
        if burst_count >= 3 and len(burst_lengths) >= 2:
            # Multiple distinct bursts detected with good separation
            avg_burst_length = np.mean(burst_lengths)
            burst_separation_score = 1.0 if burst_count >= 4 else 0.7
            burst_pattern_score = min(burst_separation_score * (avg_burst_length / len(motion_smooth)), 1.0)
        else:
            burst_pattern_score = 0.0
        
        # Calculate irregularity score (higher = more likely false positive)
        motion_std = np.std(motion_smooth)
        motion_mean = np.mean(motion_smooth)
        if motion_mean > 0:
            irregularity_score = min(motion_std / motion_mean, 2.0) / 2.0
        else:
            irregularity_score = 0.0
        
        return {
            'motion_bursts': burst_count,
            'burst_pattern_score': burst_pattern_score,
            'irregularity_score': irregularity_score
        }
        
    except Exception as e:
        logger.error(f"Error analyzing motion bursts for {video_path}: {e}")
        return {'motion_bursts': 0, 'burst_pattern_score': 0.0, 'irregularity_score': 0.0}

def validate_single_video(args_tuple) -> Dict:
    """Validate a single video for problematic patterns."""
    video_info, dataset_dir = args_tuple
    
    # Extract video information
    video_path = video_info['video_path']
    frames = video_info['frames']
    motion_share = video_info['motion_share']
    heatmap_max = video_info['heatmap_max']
    chose_fallback = video_info['chose_fallback']
    
    # Convert path to actual dataset path
    actual_path = video_path.replace('data/stage1_cropped_top7', dataset_dir)
    
    # Extract phrase
    phrase = extract_phrase_from_path(video_path)
    
    # Duration analysis
    duration_ratio = calculate_duration_ratio(frames, phrase)
    
    # Motion pattern analysis
    motion_analysis = analyze_motion_bursts(actual_path, motion_share, heatmap_max)
    
    # Issue detection with more conservative criteria
    issues = []
    recommendation = "keep"

    # Check for multiple repetitions (duration-based) - more conservative
    if duration_ratio > 2.5:
        issues.append("potential_repetition")
        recommendation = "review"

    # Check for false positive motion detection - more conservative
    if motion_share > 0.9 and motion_analysis.get('irregularity_score', 0) > 0.7:
        issues.append("false_positive_motion")
        recommendation = "review"

    # Check for multiple motion bursts (repetition pattern) - much more conservative
    if motion_analysis.get('motion_bursts', 0) >= 4 and motion_analysis.get('burst_pattern_score', 0) > 0.3:
        issues.append("multiple_motion_bursts")
        recommendation = "review"

    # Strong removal candidates - very conservative
    if (duration_ratio > 4.0 or
        (motion_share > 0.95 and motion_analysis.get('irregularity_score', 0) > 0.9) or
        (motion_analysis.get('motion_bursts', 0) >= 6 and motion_analysis.get('burst_pattern_score', 0) > 0.5)):
        recommendation = "remove"
    
    return {
        'video_path': actual_path,
        'phrase': phrase,
        'frames': frames,
        'duration_ratio': duration_ratio,
        'motion_share': motion_share,
        'heatmap_max': heatmap_max,
        'chose_fallback': chose_fallback,
        'motion_bursts': motion_analysis.get('motion_bursts', 0),
        'burst_pattern_score': motion_analysis.get('burst_pattern_score', 0.0),
        'irregularity_score': motion_analysis.get('irregularity_score', 0.0),
        'issues': ','.join(issues) if issues else 'none',
        'recommendation': recommendation
    }

def calculate_phrase_statistics(df: pd.DataFrame) -> Dict:
    """Calculate duration statistics by phrase."""
    stats = {}

    for phrase in PHRASE_EXPECTED_DURATIONS.keys():
        phrase_data = df[df['phrase'] == phrase]
        if len(phrase_data) > 0:
            stats[phrase] = {
                'count': len(phrase_data),
                'median_duration': phrase_data['frames'].median(),
                'mean_duration': phrase_data['frames'].mean(),
                'std_duration': phrase_data['frames'].std(),
                'median_ratio': phrase_data['duration_ratio'].median()
            }

    return stats

def generate_summary_report(results_df: pd.DataFrame, phrase_stats: Dict, output_path: str):
    """Generate comprehensive validation summary report."""
    total_videos = len(results_df)
    flagged_videos = len(results_df[results_df['issues'] != 'none'])

    # Count by recommendation
    recommendations = results_df['recommendation'].value_counts()

    # Count by issue type
    issue_counts = defaultdict(int)
    for issues_str in results_df['issues']:
        if issues_str != 'none':
            for issue in issues_str.split(','):
                issue_counts[issue.strip()] += 1

    with open(output_path, 'w') as f:
        f.write("VIDEO VALIDATION ANALYSIS REPORT\n")
        f.write("=" * 50 + "\n\n")

        # Overall Statistics
        f.write("OVERALL STATISTICS\n")
        f.write("-" * 20 + "\n")
        f.write(f"Total videos processed: {total_videos:,}\n")
        f.write(f"Videos flagged with issues: {flagged_videos:,} ({flagged_videos/total_videos*100:.1f}%)\n")
        f.write(f"Videos recommended to keep: {recommendations.get('keep', 0):,} ({recommendations.get('keep', 0)/total_videos*100:.1f}%)\n")
        f.write(f"Videos recommended for review: {recommendations.get('review', 0):,} ({recommendations.get('review', 0)/total_videos*100:.1f}%)\n")
        f.write(f"Videos recommended for removal: {recommendations.get('remove', 0):,} ({recommendations.get('remove', 0)/total_videos*100:.1f}%)\n\n")

        # Issue Type Breakdown
        f.write("ISSUE TYPE BREAKDOWN\n")
        f.write("-" * 20 + "\n")
        for issue, count in sorted(issue_counts.items()):
            f.write(f"{issue.replace('_', ' ').title()}: {count:,} videos ({count/total_videos*100:.1f}%)\n")
        f.write("\n")

        # Phrase-by-Phrase Analysis
        f.write("PHRASE-BY-PHRASE ANALYSIS\n")
        f.write("-" * 25 + "\n")
        for phrase, stats in phrase_stats.items():
            phrase_results = results_df[results_df['phrase'] == phrase]
            phrase_flagged = len(phrase_results[phrase_results['issues'] != 'none'])

            f.write(f"\n{phrase.upper().replace('_', ' ')}:\n")
            f.write(f"  Total videos: {stats['count']:,}\n")
            f.write(f"  Flagged videos: {phrase_flagged:,} ({phrase_flagged/stats['count']*100:.1f}%)\n")
            f.write(f"  Median duration: {stats['median_duration']:.1f} frames\n")
            f.write(f"  Mean duration: {stats['mean_duration']:.1f} frames\n")
            f.write(f"  Duration std: {stats['std_duration']:.1f} frames\n")
            f.write(f"  Median duration ratio: {stats['median_ratio']:.2f}\n")

            # Top issues for this phrase
            phrase_issues = defaultdict(int)
            for issues_str in phrase_results['issues']:
                if issues_str != 'none':
                    for issue in issues_str.split(','):
                        phrase_issues[issue.strip()] += 1

            if phrase_issues:
                f.write(f"  Top issues:\n")
                for issue, count in sorted(phrase_issues.items(), key=lambda x: x[1], reverse=True)[:3]:
                    f.write(f"    - {issue.replace('_', ' ').title()}: {count} videos\n")

def move_flagged_videos(results_df: pd.DataFrame, flagged_dir: str):
    """Move flagged videos to validation directory while preserving structure."""
    flagged_path = Path(flagged_dir)
    flagged_path.mkdir(parents=True, exist_ok=True)

    moved_count = 0
    flagged_videos = results_df[results_df['recommendation'].isin(['review', 'remove'])]

    for _, row in flagged_videos.iterrows():
        src_path = Path(row['video_path'])

        # Preserve directory structure
        rel_path = src_path.relative_to(src_path.parts[0])  # Remove 'data' prefix
        dst_path = flagged_path / rel_path

        # Create destination directory
        dst_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            if src_path.exists():
                shutil.move(str(src_path), str(dst_path))
                moved_count += 1
                logger.info(f"Moved {src_path} -> {dst_path}")
        except Exception as e:
            logger.error(f"Failed to move {src_path}: {e}")

    logger.info(f"Moved {moved_count} flagged videos to {flagged_dir}")
    return moved_count

def generate_validation_gallery(results_df: pd.DataFrame, output_path: str, top_n: int = 30):
    """Generate HTML gallery of worst flagged videos for manual inspection."""
    # Select worst videos based on multiple criteria
    flagged_videos = results_df[results_df['issues'] != 'none'].copy()

    if len(flagged_videos) == 0:
        logger.info("No flagged videos found for gallery generation")
        return

    # Calculate composite problem score
    flagged_videos['problem_score'] = (
        flagged_videos['duration_ratio'] * 0.3 +
        flagged_videos['burst_pattern_score'] * 0.4 +
        flagged_videos['irregularity_score'] * 0.3
    )

    # Sort by problem score and take top N
    worst_videos = flagged_videos.nlargest(top_n, 'problem_score')

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Video Validation Gallery - Top {top_n} Flagged Videos</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .video-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }}
            .video-card {{ border: 1px solid #ddd; padding: 15px; border-radius: 8px; }}
            .video-card h3 {{ margin-top: 0; color: #333; }}
            .metrics {{ background: #f5f5f5; padding: 10px; border-radius: 4px; margin: 10px 0; }}
            .issues {{ background: #ffe6e6; padding: 8px; border-radius: 4px; color: #d00; }}
            .recommendation {{ padding: 5px 10px; border-radius: 4px; color: white; font-weight: bold; }}
            .remove {{ background: #d00; }}
            .review {{ background: #f80; }}
            .keep {{ background: #080; }}
        </style>
    </head>
    <body>
        <h1>Video Validation Gallery - Top {len(worst_videos)} Flagged Videos</h1>
        <p>Generated on {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

        <div class="video-grid">
    """

    for idx, (_, video) in enumerate(worst_videos.iterrows(), 1):
        video_name = Path(video['video_path']).name

        html_content += f"""
            <div class="video-card">
                <h3>#{idx}: {video_name}</h3>
                <p><strong>Phrase:</strong> {video['phrase'].replace('_', ' ').title()}</p>
                <p><strong>Path:</strong> {video['video_path']}</p>

                <div class="metrics">
                    <strong>Metrics:</strong><br>
                    Duration: {video['frames']} frames (ratio: {video['duration_ratio']:.2f})<br>
                    Motion Share: {video['motion_share']:.3f}<br>
                    Motion Bursts: {video['motion_bursts']}<br>
                    Burst Pattern Score: {video['burst_pattern_score']:.3f}<br>
                    Irregularity Score: {video['irregularity_score']:.3f}<br>
                    Problem Score: {video['problem_score']:.3f}
                </div>

                <div class="issues">
                    <strong>Issues:</strong> {video['issues'].replace('_', ' ').replace(',', ', ').title()}
                </div>

                <div class="recommendation {video['recommendation']}">
                    Recommendation: {video['recommendation'].upper()}
                </div>
            </div>
        """

    html_content += """
        </div>
    </body>
    </html>
    """

    with open(output_path, 'w') as f:
        f.write(html_content)

    logger.info(f"Generated validation gallery with {len(worst_videos)} videos: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Video Validation for Problematic Pattern Detection')
    parser.add_argument('--dataset_dir', required=True, help='Directory containing cleaned dataset')
    parser.add_argument('--summary_csv', required=True, help='Motion analysis summary CSV file')
    parser.add_argument('--output_flagged', required=True, help='Output CSV for flagged videos')
    parser.add_argument('--output_summary', required=True, help='Output summary report')
    parser.add_argument('--flagged_dir', required=True, help='Directory to move flagged videos')
    parser.add_argument('--gallery', required=True, help='HTML gallery output path')
    parser.add_argument('--workers', type=int, default=4, help='Number of worker processes')
    parser.add_argument('--top_gallery', type=int, default=30, help='Number of videos in gallery')

    args = parser.parse_args()

    logger.info("Starting video validation analysis...")

    # Load motion analysis summary
    logger.info(f"Loading summary data from {args.summary_csv}")
    df = pd.read_csv(args.summary_csv)
    logger.info(f"Loaded {len(df)} video records")

    # Prepare validation tasks
    validation_tasks = [(row.to_dict(), args.dataset_dir) for _, row in df.iterrows()]

    # Process videos in parallel
    logger.info(f"Processing {len(validation_tasks)} videos with {args.workers} workers...")
    results = []

    with ProcessPoolExecutor(max_workers=args.workers) as executor:
        future_to_video = {executor.submit(validate_single_video, task): task for task in validation_tasks}

        for future in tqdm(as_completed(future_to_video), total=len(validation_tasks), desc="Validating videos"):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                logger.error(f"Error processing video: {e}")

    # Convert results to DataFrame
    results_df = pd.DataFrame(results)

    # Calculate phrase statistics
    phrase_stats = calculate_phrase_statistics(results_df)

    # Save flagged videos CSV
    logger.info(f"Saving flagged videos to {args.output_flagged}")
    results_df.to_csv(args.output_flagged, index=False)

    # Generate summary report
    logger.info(f"Generating summary report: {args.output_summary}")
    generate_summary_report(results_df, phrase_stats, args.output_summary)

    # Move flagged videos
    logger.info(f"Moving flagged videos to {args.flagged_dir}")
    moved_count = move_flagged_videos(results_df, args.flagged_dir)

    # Generate validation gallery
    logger.info(f"Generating validation gallery: {args.gallery}")
    generate_validation_gallery(results_df, args.gallery, args.top_gallery)

    # Final summary
    total_videos = len(results_df)
    flagged_count = len(results_df[results_df['issues'] != 'none'])

    logger.info("=" * 50)
    logger.info("VALIDATION COMPLETE")
    logger.info("=" * 50)
    logger.info(f"Total videos processed: {total_videos:,}")
    logger.info(f"Videos flagged: {flagged_count:,} ({flagged_count/total_videos*100:.1f}%)")
    logger.info(f"Videos moved: {moved_count:,}")
    logger.info(f"Reports generated:")
    logger.info(f"  - Flagged videos CSV: {args.output_flagged}")
    logger.info(f"  - Summary report: {args.output_summary}")
    logger.info(f"  - Validation gallery: {args.gallery}")

if __name__ == "__main__":
    main()
