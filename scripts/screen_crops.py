#!/usr/bin/env python3
"""
Screen Crops - Detect Wrong Lip Crops

After Stage-1 cropping, detect wrong crops (necks/hair/etc.) using MediaPipe face detection,
motion analysis, and mouth area variability. Split videos into keep/reject based on heuristics.

Usage:
    python -u scripts/screen_crops.py \
      --input_dir "data/stage1_cropped_top7" \
      --output_dir "data/stage1_cropped_top7_screened" \
      --gallery "reports/screen_gallery.html" \
      --summary "reports/screen_summary.csv" \
      --min_det_rate 0.85 \
      --min_mouth_motion_share 0.25 \
      --min_mouth_area_std 4.0 \
      --frames_for_gallery 3 \
      --upsample 2.0 \
      --workers 4
"""

import argparse
import cv2
import numpy as np
import os
import csv
import shutil
from pathlib import Path
from tqdm import tqdm
import multiprocessing as mp
from typing import List, Tuple, Optional, Dict, Any
import mediapipe as mp_face
from jinja2 import Template
import base64


def find_videos(input_dir: str) -> List[Path]:
    """Find all video files recursively."""
    input_path = Path(input_dir)
    extensions = {'.mp4', '.mov', '.mkv', '.avi', '.webm'}
    
    videos = []
    for ext in extensions:
        videos.extend(input_path.rglob(f'*{ext}'))
        videos.extend(input_path.rglob(f'*{ext.upper()}'))
    
    return sorted(videos)


def get_mouth_landmarks(landmarks, image_shape):
    """Extract mouth landmark coordinates from MediaPipe face mesh."""
    h, w = image_shape[:2]
    
    # MediaPipe mouth landmark indices (inner and outer lips)
    MOUTH_INDICES = [
        # Outer lip
        61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
        # Inner lip  
        78, 95, 88, 178, 87, 14, 317, 402, 318, 324
    ]
    
    mouth_points = []
    for idx in MOUTH_INDICES:
        if idx < len(landmarks.landmark):
            x = int(landmarks.landmark[idx].x * w)
            y = int(landmarks.landmark[idx].y * h)
            mouth_points.append((x, y))
    
    return mouth_points


def compute_mouth_area(mouth_points):
    """Compute area of mouth polygon."""
    if len(mouth_points) < 3:
        return 0.0
    
    # Convert to numpy array for cv2.contourArea
    contour = np.array(mouth_points, dtype=np.int32)
    return cv2.contourArea(contour)


def analyze_video(args) -> Dict[str, Any]:
    """Analyze a single video for screening metrics."""
    video_path, upsample_factor, min_det_rate, min_mouth_motion_share, min_mouth_area_std = args
    
    try:
        # Initialize MediaPipe Face Mesh
        mp_face_mesh = mp_face.solutions.face_mesh
        face_mesh = mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            return {'error': f'Failed to open {video_path}'}
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames <= 0:
            total_frames = 1000  # Fallback
        
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Metrics tracking
        detected_frames = 0
        mouth_areas = []
        prev_frame = None
        total_motion = 0.0
        mouth_motion = 0.0
        
        # Mouth region bounds (relative to crop)
        mouth_y_min = int(0.45 * height)
        mouth_y_max = int(0.85 * height)
        mouth_x_min = int(0.30 * width)
        mouth_x_max = int(0.70 * width)
        
        frame_idx = 0
        sample_frames = []  # For gallery
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Store sample frames for gallery (evenly spaced)
            if len(sample_frames) < 3:
                if frame_idx == 0 or frame_idx == total_frames // 2 or frame_idx == total_frames - 1:
                    sample_frames.append(frame.copy())
            
            # Upsample for better detection
            if upsample_factor > 1.0:
                new_w = int(width * upsample_factor)
                new_h = int(height * upsample_factor)
                upsampled = cv2.resize(frame, (new_w, new_h))
            else:
                upsampled = frame
            
            # MediaPipe face detection
            rgb_frame = cv2.cvtColor(upsampled, cv2.COLOR_BGR2RGB)
            results = face_mesh.process(rgb_frame)
            
            if results.multi_face_landmarks:
                detected_frames += 1
                
                # Get mouth landmarks and compute area
                landmarks = results.multi_face_landmarks[0]
                mouth_points = get_mouth_landmarks(landmarks, upsampled.shape)
                if mouth_points:
                    area = compute_mouth_area(mouth_points)
                    # Scale area back to original resolution
                    area = area / (upsample_factor ** 2)
                    mouth_areas.append(area)
            
            # Motion analysis
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            if prev_frame is not None:
                # Frame difference
                diff = cv2.absdiff(gray, prev_frame)
                
                # Total motion energy
                total_motion += np.sum(diff)
                
                # Motion in mouth region
                mouth_region = diff[mouth_y_min:mouth_y_max, mouth_x_min:mouth_x_max]
                mouth_motion += np.sum(mouth_region)
            
            prev_frame = gray
            frame_idx += 1
        
        cap.release()
        face_mesh.close()
        
        # Compute metrics
        detection_rate = detected_frames / max(frame_idx, 1)
        mouth_motion_share = mouth_motion / max(total_motion, 1e-6)
        mouth_area_std = np.std(mouth_areas) if mouth_areas else 0.0
        
        # Determine if suspect (≥2 of 3 criteria fail)
        failures = 0
        reasons = []
        
        if detection_rate < min_det_rate:
            failures += 1
            reasons.append(f"low_detection_rate({detection_rate:.3f})")
        
        if mouth_motion_share < min_mouth_motion_share:
            failures += 1
            reasons.append(f"low_mouth_motion({mouth_motion_share:.3f})")
        
        if mouth_area_std < min_mouth_area_std:
            failures += 1
            reasons.append(f"low_mouth_variability({mouth_area_std:.3f})")
        
        suspect = failures >= 2
        
        return {
            'video_path': str(video_path),
            'frames': frame_idx,
            'detection_rate': detection_rate,
            'mouth_motion_share': mouth_motion_share,
            'mouth_area_std': mouth_area_std,
            'suspect': suspect,
            'reasons': reasons,
            'sample_frames': sample_frames
        }
        
    except Exception as e:
        return {'error': f'Exception analyzing {video_path}: {e}'}


def copy_video_and_sidecars(src_path: Path, dst_path: Path):
    """Copy video and any small sidecar files."""
    dst_path.parent.mkdir(parents=True, exist_ok=True)
    shutil.copy2(src_path, dst_path)
    
    # Copy potential sidecar files (thumbnails, etc.)
    for sidecar in src_path.parent.glob(f"{src_path.stem}*"):
        if sidecar != src_path and sidecar.stat().st_size < 1024 * 1024:  # < 1MB
            sidecar_dst = dst_path.parent / sidecar.name
            shutil.copy2(sidecar, sidecar_dst)


def generate_html_gallery(results: List[Dict], output_path: str):
    """Generate HTML gallery with suspects first."""
    # Sort: suspects first, then by detection rate
    sorted_results = sorted(results, key=lambda x: (not x.get('suspect', False), -x.get('detection_rate', 0)))
    
    html_template = """
<!DOCTYPE html>
<html>
<head>
    <title>Crop Screening Results</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .header { text-align: center; margin-bottom: 30px; }
        .video-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(500px, 1fr)); gap: 20px; }
        .video-card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .suspect { border-left: 5px solid #ff4444; }
        .good { border-left: 5px solid #44ff44; }
        .video-title { font-weight: bold; margin-bottom: 10px; word-break: break-all; }
        .thumbnails { display: flex; gap: 10px; margin: 15px 0; }
        .thumbnail { max-width: 96px; max-height: 96px; border: 1px solid #ddd; }
        .metrics { display: flex; gap: 10px; margin: 10px 0; }
        .metric { padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .metric.good { background: #e8f5e8; color: #2d5a2d; }
        .metric.bad { background: #ffe8e8; color: #5a2d2d; }
        .reasons { font-size: 12px; color: #666; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Crop Screening Results</h1>
        <p>Suspects shown first - videos likely missing proper lip regions</p>
    </div>
    <div class="video-grid">
    {% for result in results %}
        <div class="video-card {{ 'suspect' if result.suspect else 'good' }}">
            <div class="video-title">{{ result.video_path }}</div>
            <div class="thumbnails">
                {% for frame in result.sample_frames %}
                <img src="data:image/png;base64,{{ frame }}" class="thumbnail" alt="Sample frame">
                {% endfor %}
            </div>
            <div class="metrics">
                <span class="metric {{ 'bad' if result.detection_rate < 0.85 else 'good' }}">
                    Detection: {{ "%.3f"|format(result.detection_rate) }}
                </span>
                <span class="metric {{ 'bad' if result.mouth_motion_share < 0.25 else 'good' }}">
                    Motion: {{ "%.3f"|format(result.mouth_motion_share) }}
                </span>
                <span class="metric {{ 'bad' if result.mouth_area_std < 4.0 else 'good' }}">
                    Variability: {{ "%.3f"|format(result.mouth_area_std) }}
                </span>
            </div>
            <div class="reasons">
                <strong>Frames:</strong> {{ result.frames }} | 
                <strong>Status:</strong> {{ 'SUSPECT' if result.suspect else 'GOOD' }}
                {% if result.reasons %}| <strong>Issues:</strong> {{ result.reasons|join(', ') }}{% endif %}
            </div>
        </div>
    {% endfor %}
    </div>
</body>
</html>
"""
    
    # Convert sample frames to base64
    for result in sorted_results:
        if 'sample_frames' in result:
            b64_frames = []
            for frame in result['sample_frames']:
                _, buffer = cv2.imencode('.png', frame)
                b64_frames.append(base64.b64encode(buffer).decode('utf-8'))
            result['sample_frames'] = b64_frames
    
    template = Template(html_template)
    html_content = template.render(results=sorted_results)
    
    with open(output_path, 'w') as f:
        f.write(html_content)


def main():
    parser = argparse.ArgumentParser(description='Screen Crops for Wrong Lip Regions')
    parser.add_argument('--input_dir', required=True, help='Input directory with cropped videos')
    parser.add_argument('--output_dir', required=True, help='Output directory for keep/reject split')
    parser.add_argument('--gallery', required=True, help='HTML gallery output path')
    parser.add_argument('--summary', required=True, help='CSV summary output path')
    parser.add_argument('--min_det_rate', type=float, default=0.85, help='Minimum detection rate')
    parser.add_argument('--min_mouth_motion_share', type=float, default=0.25, help='Minimum mouth motion share')
    parser.add_argument('--min_mouth_area_std', type=float, default=4.0, help='Minimum mouth area std')
    parser.add_argument('--frames_for_gallery', type=int, default=3, help='Frames per video for gallery')
    parser.add_argument('--upsample', type=float, default=2.0, help='Upsample factor for detection')
    parser.add_argument('--workers', type=int, default=4, help='Number of worker processes')
    
    args = parser.parse_args()
    
    # Setup paths
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    keep_dir = output_dir / 'keep'
    reject_dir = output_dir / 'reject'
    
    keep_dir.mkdir(parents=True, exist_ok=True)
    reject_dir.mkdir(parents=True, exist_ok=True)
    
    reports_dir = Path('reports')
    reports_dir.mkdir(exist_ok=True)
    
    # Find videos
    print("Scanning for cropped videos...")
    videos = find_videos(str(input_dir))
    print(f"Found {len(videos)} videos to screen")
    
    if not videos:
        print("No videos found!")
        return
    
    # Prepare arguments for multiprocessing
    process_args = [(video, args.upsample, args.min_det_rate, 
                    args.min_mouth_motion_share, args.min_mouth_area_std) 
                   for video in videos]
    
    # Analyze videos
    print(f"Analyzing {len(videos)} videos with {args.workers} workers...")
    
    with mp.Pool(args.workers) as pool:
        results = list(tqdm(
            pool.imap(analyze_video, process_args),
            total=len(videos),
            desc="Screening videos"
        ))
    
    # Filter out errors and collect valid results
    valid_results = []
    errors = []
    
    for result in results:
        if 'error' in result:
            errors.append(result['error'])
        else:
            valid_results.append(result)
    
    # Split videos into keep/reject
    keep_count = 0
    reject_count = 0
    
    for result in valid_results:
        src_path = Path(result['video_path'])
        rel_path = src_path.relative_to(input_dir)
        
        if result['suspect']:
            dst_path = reject_dir / rel_path
            reject_count += 1
        else:
            dst_path = keep_dir / rel_path
            keep_count += 1
        
        copy_video_and_sidecars(src_path, dst_path)
    
    # Write CSV summary
    with open(args.summary, 'w', newline='') as csvfile:
        fieldnames = ['video_path', 'frames', 'detection_rate', 'mouth_motion_share', 
                     'mouth_area_std', 'suspect', 'reasons']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for result in valid_results:
            writer.writerow({
                'video_path': result['video_path'],
                'frames': result['frames'],
                'detection_rate': result['detection_rate'],
                'mouth_motion_share': result['mouth_motion_share'],
                'mouth_area_std': result['mouth_area_std'],
                'suspect': result['suspect'],
                'reasons': ';'.join(result['reasons'])
            })
    
    # Generate HTML gallery
    generate_html_gallery(valid_results, args.gallery)
    
    # Write error log
    if errors:
        with open(reports_dir / 'screen_skips.txt', 'w') as f:
            f.write("Errors during screening:\n")
            for error in errors:
                f.write(f"{error}\n")
    
    print(f"\nScreening complete!")
    print(f"Total videos analyzed: {len(valid_results)}")
    print(f"Keep (good crops): {keep_count}")
    print(f"Reject (suspect crops): {reject_count}")
    print(f"Errors: {len(errors)}")
    print(f"Gallery: {args.gallery}")
    print(f"Summary: {args.summary}")
    if errors:
        print(f"Error log: reports/screen_skips.txt")


if __name__ == '__main__':
    main()
