# ICU Lipreading Training Configuration
# Target: >80% validation accuracy on speaker-held-out 10-class problem

# Data Configuration
data:
  s3_bucket: "icudatasetphrasesfortesting"
  s3_prefix: "icu-videos/"
  target_phrases:
    - "pillow"
    - "phone"
    - "doctor"
    - "glasses"
    - "i_need_to_move"
    - "help"
    - "my_mouth_is_dry"
    - "my_chest_hurts"
    - "my_back_hurts"
    - "i_m_hot"

  # Preprocessing
  img_size: 96  # Final ROI size (96x96)
  frames_per_clip: 16  # Fixed frames per clip (16 or 32)
  fps_target: 25  # Target FPS for standardization
  use_mediapipe: true  # Now using Python 3.11 venv with MediaPipe
  drop_low_motion: true  # keep this if implemented via alt heuristics
  min_valid_roi_pct: 0.15  # reject tiny lips (adjust if needed)

  # Landmark-anchored preprocessing parameters (tightened for lips filling frame)
  target_mouth_width_ratio: 0.90  # mouth ≈ 90% of crop width
  aspect_ratio_y: 0.75             # slightly shorter height around lips
  pad_pct: 0.02                    # very small padding
  coverage_accept_min: 0.35        # accept 0.35–0.70
  coverage_accept_max: 0.70
  target_coverage: 0.45            # target coverage for auto-adjust
  smooth_window: 5
  min_frames_with_landmarks_pct: 90
  max_adjust_iterations: 3         # up to 3 iterations for auto-adjust

  # Top-middle supercrop v2 configuration
  supercrop_v2:
    upscale_factor: 2.0
    anchor_y_ratio: 0.42
    search_vert_top: 0.20
    search_vert_bottom: 0.70
    search_horiz_offset: 0.05
    widths_frac: [0.30, 0.34, 0.38]
    edge_band_px: 8
    dark_band_px: 6
    motion_thresh: 0.0098     # 2.5/255
    edge_var_thresh: 0.002
    weights:
      edge: 0.55
      redness: 0.30
      dark: 0.15

  # YOLO+SAM configuration
  yolo_sam:
    yolo_model: "./yolov8n.pt"      # Use standard YOLO for face detection (class 0)
    conf: 0.25
    iou: 0.5
    sam_checkpoint: "./checkpoints/sam_vit_b_01ec64.pth"
    face_lower_frac_for_box: 0.60
    mouth_seed_bias_up: 0.15
    pad_frac: 0.12
    target_mask_width_ratio: 0.80
    min_mask_width_ratio: 0.70
    max_mask_width_ratio: 0.90
    anchor_y_ratio: 0.42
    smooth_window: 5
    motion_thresh: 0.0098
    min_frames_with_mask_pct: 80

  # Lower-face ROI with RetinaFace+SAM configuration
  lowerface_roi_retinaface_sam:
    roi_x: [0.28, 0.72]
    roi_y: [0.05, 0.58]
    clahe:
      clip: 2.0
      tiles: 8
    yolo_conf: 0.15
    yolo_iou: 0.5
    face_lower_frac: 0.60
    pad_frac: 0.12
    target_mask_width_ratio: 0.84
    min_mask_width_ratio: 0.75
    max_mask_width_ratio: 0.92
    centroid_y_min: 0.60
    centroid_y_max: 0.85
    smooth_window: 5
    motion_thresh: 0.0098
    min_frames_with_mask_pct: 85
    max_fallback_frac: 0.20
    sam_checkpoint: "./checkpoints/sam_vit_b_01ec64.pth"

  # Splits (speaker-disjoint)
  split_seed: 42
  train_pct: 0.70
  val_pct: 0.15
  test_pct: 0.15
  speaker_disjoint: true

  # Paths
  data_processed_dir: "./data_processed"
  manifests_dir: "./manifests"
  checkpoints_dir: "./checkpoints"

# Model Configuration
model:
  name: "Mobile3DTiny_BiGRU"
  num_classes: 10
  input_channels: 3
  dropout: 0.3
  
  # Mobile3DTiny params
  mobile3d:
    width_mult: 0.5
    depth_mult: 0.5
  
  # BiGRU params  
  bigru:
    hidden_size: 128
    num_layers: 2
    dropout: 0.2

# Training Configuration
training:
  batch_size: 32
  lr: 0.003
  epochs: 25
  early_stopping_patience: 5
  class_weighting: auto  # or false
  device: auto  # 'cuda' if you'll run on GPU, else 'cpu'
  num_workers: 4
  weight_decay: 1e-4

  # Scheduler
  scheduler:
    type: "OneCycleLR"
    max_lr: 0.003
    pct_start: 0.3

  # Early stopping
  early_stopping:
    patience: 5
    metric: "val_macro_f1"
    mode: "max"

  # Class balancing
  use_class_weights: true

  # Checkpointing
  save_best_only: true
  checkpoint_dir: "./checkpoints"

# Evaluation Configuration
evaluation:
  metrics:
    - "accuracy"
    - "macro_f1"
    - "per_class_precision"
    - "per_class_recall"
    - "confusion_matrix"
  
  min_class_recall: 0.70  # Minimum acceptable per-class recall

# Segmentation Configuration
segmentation:
  yolo:
    model: "yolov8n.pt"  # small & fast
    conf_threshold: 0.5
    classes: [0]  # Person class only

  sam:
    model_type: "vit_b"
    checkpoint_path: "./checkpoints/sam_vit_b_01ec64.pth"
  
  # Quality filters
  quality:
    min_face_area: 1000  # Minimum face bounding box area
    min_motion_threshold: 0.1  # Minimum frame-to-frame motion
    max_blur_threshold: 100  # Maximum blur (Laplacian variance)

# Inference Configuration
inference:
  device: "auto"  # auto, cuda, cpu
  max_batch_size: 8
  timeout_seconds: 5.0
  
  # API settings
  api:
    host: "0.0.0.0"
    port: 8000
    return_top_k: 5

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  log_dir: "./logs"
  
# Reproducibility
seed: 42
