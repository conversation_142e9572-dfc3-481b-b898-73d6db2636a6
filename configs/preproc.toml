# ICU Lipreading 4-Stage Gated Preprocessing Pipeline Configuration

[target_phrases]
# Focus only on these 7 phrases with highest video counts
phrases = ["pillow", "phone", "doctor", "glasses", "i_need_to_move", "help", "my_mouth_is_dry"]

[paths]
input_dir = "data/RAW_VIDEOS"
stage1_dir = "data/STAGE1_cropped"
stage2_dir = "data/STAGE2_landmarks"
stage3_dir = "data/STAGE3_segmented"
stage4_dir = "data/STAGE4_filtered"
manifests_dir = "data/manifests"
reports_dir = "reports"
logs_dir = "reports/logs"

[crop]
# Top-middle third of 3x3 grid (row=0, col=1)
resize_to = 96
keep_aspect = true
pad_mode = "edge"
x0_frac = 0.3333  # 1/3
x1_frac = 0.6667  # 2/3
y0_frac = 0.0
y1_frac = 0.3333  # 1/3
shift_y_frac = 0.08  # Optional downward shift for better lip capture

[qc]
frames_per_video = 5
thumbs_per_row = 9
sample_videos = 200
acceptance_min_centerfrac = 0.85
mouth_center_tolerance_px = 10

[mediapipe]
static_image_mode = false
max_num_faces = 1
min_detection_confidence = 0.5
min_tracking_confidence = 0.5

[yolo]
model = "yolov8n-face.pt"

[sam]
model_type = "vit_b"

[filter]
min_frames = 12
max_frames = 160
allow_single_utterance = true
mouth_open_peak_prominence = 0.08
min_peak_separation_frames = 8
max_allowed_peaks = 1
max_optical_flow_mean = 6.0
drop_black_or_static = true

[processing]
# Multiprocessing settings
max_workers = 4
chunk_size = 10
video_extensions = [".mp4", ".avi", ".mov", ".mkv"]

[logging]
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
