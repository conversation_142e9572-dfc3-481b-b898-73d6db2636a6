#!/usr/bin/env python3
"""
Lower-face ROI RetinaFace+SAM Pipeline Visualization
Shows side-by-side comparison of pipeline stages and results
"""

import os
import sys
import pandas as pd
import numpy as np
import cv2
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import tempfile
from typing import List, Dict, Tuple, Optional
import argparse

# Add src to path
sys.path.append('src')
from preprocess import VideoPreprocessor
from utils import load_config, setup_logging, S3Manager


class LowerfaceROIVisualizer:
    """Visualize lower-face ROI pipeline results"""
    
    def __init__(self, config_path: str):
        self.config = load_config(config_path)
        self.s3_manager = S3Manager(self.config['data']['s3_bucket'])
        
        # Initialize preprocessor for pipeline components
        self.preprocessor = VideoPreprocessor(self.config, mode='lowerface_roi_retinaface_sam')
        
        # ROI config
        self.roi_config = self.config['data']['lowerface_roi_retinaface_sam']
        
    def download_and_extract_frame(self, s3_key: str, frame_idx: int = 8) -> Optional[np.ndarray]:
        """Download video and extract a specific frame"""
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        try:
            if not self.s3_manager.download_video(s3_key, temp_path):
                return None
            
            # Extract frames
            frames = self.preprocessor.extract_frames_from_video(temp_path)
            if frames is None or len(frames) <= frame_idx:
                return None
            
            return frames[frame_idx]
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    def process_frame_with_debug(self, frame: np.ndarray) -> Dict:
        """Process frame through pipeline with debug information"""
        h, w = frame.shape[:2]
        debug_info = {
            'original_frame': frame.copy(),
            'roi_coords': None,
            'roi_frame': None,
            'preprocessed_roi': None,
            'face_data': None,
            'sam_prompts': None,
            'mask': None,
            'final_crop': None,
            'success': False
        }
        
        try:
            # A) Extract ROI from spatial prior
            roi_coords, roi_frame = self.preprocessor._extract_roi_from_spatial_prior(frame, self.roi_config)
            if roi_frame is None:
                return debug_info
            
            debug_info['roi_coords'] = roi_coords
            debug_info['roi_frame'] = roi_frame.copy()
            
            # B) Preprocess ROI (YCrCb + CLAHE)
            preprocessed_roi = self.preprocessor._preprocess_roi(roi_frame, self.roi_config)
            debug_info['preprocessed_roi'] = preprocessed_roi.copy()
            
            # C) Face detection with landmarks
            face_data = self.preprocessor._detect_face_with_landmarks_roi(preprocessed_roi, self.roi_config)
            if face_data is None:
                return debug_info
            
            debug_info['face_data'] = face_data
            
            # D) Create SAM prompts
            sam_prompts = self.preprocessor._create_mouth_specific_sam_prompts(face_data, roi_coords, self.roi_config)
            debug_info['sam_prompts'] = sam_prompts
            
            # E) SAM segmentation
            mask = self.preprocessor._segment_lips_sam_roi(frame, sam_prompts, self.roi_config)
            if mask is not None:
                debug_info['mask'] = mask
                
                # F) Extract final crop
                final_crop = self.preprocessor._extract_crop_from_mask_roi(frame, mask, face_data, self.roi_config)
                if final_crop is not None:
                    debug_info['final_crop'] = final_crop
                    debug_info['success'] = True
            
        except Exception as e:
            print(f"Processing error: {e}")
        
        return debug_info
    
    def create_side_by_side_visualization(self, debug_info: Dict, title: str = "") -> np.ndarray:
        """Create side-by-side visualization of pipeline stages"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # Left panel: Original frame with overlays
        original = debug_info['original_frame']
        ax1.imshow(cv2.cvtColor(original, cv2.COLOR_BGR2RGB))
        ax1.set_title(f"Original Frame + Pipeline Overlays\n{title}")
        ax1.axis('off')
        
        # Draw ROI bounding box
        if debug_info['roi_coords'] is not None:
            roi_x1, roi_y1, roi_x2, roi_y2 = debug_info['roi_coords']
            roi_rect = patches.Rectangle((roi_x1, roi_y1), roi_x2-roi_x1, roi_y2-roi_y1,
                                       linewidth=3, edgecolor='cyan', facecolor='none', label='ROI')
            ax1.add_patch(roi_rect)
        
        # Draw face bounding box (converted to original coordinates)
        if debug_info['face_data'] is not None and debug_info['roi_coords'] is not None:
            face_bbox = debug_info['face_data']['face_bbox']
            roi_x1, roi_y1, _, _ = debug_info['roi_coords']
            
            face_x1 = roi_x1 + face_bbox[0]
            face_y1 = roi_y1 + face_bbox[1]
            face_x2 = roi_x1 + face_bbox[2]
            face_y2 = roi_y1 + face_bbox[3]
            
            face_rect = patches.Rectangle((face_x1, face_y1), face_x2-face_x1, face_y2-face_y1,
                                        linewidth=2, edgecolor='yellow', facecolor='none', label='Face')
            ax1.add_patch(face_rect)
            
            # Draw landmark points
            landmarks = debug_info['face_data']['landmarks']
            if landmarks:
                for name, (x, y) in landmarks.items():
                    orig_x = roi_x1 + x
                    orig_y = roi_y1 + y
                    color = {'mouth_left': 'red', 'mouth_right': 'red', 'nose_tip': 'blue'}.get(name, 'white')
                    ax1.plot(orig_x, orig_y, 'o', color=color, markersize=8, label=name if name == 'nose_tip' else '')
        
        # Draw SAM mask outline
        if debug_info['mask'] is not None:
            mask = debug_info['mask']
            contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            for i, contour in enumerate(contours):
                contour = contour.squeeze()
                if len(contour.shape) == 2 and contour.shape[0] > 2:
                    ax1.plot(contour[:, 0], contour[:, 1], 'lime', linewidth=2, label='SAM Mask' if i == 0 else '')
        
        ax1.legend(loc='upper right', fontsize=8)
        
        # Right panel: Final crop with mask contour
        if debug_info['final_crop'] is not None:
            final_crop = debug_info['final_crop']
            ax2.imshow(cv2.cvtColor(final_crop, cv2.COLOR_BGR2RGB))
            ax2.set_title(f"Final 96×96 Crop\nSuccess: {debug_info['success']}")
            
            # Overlay mask contour on crop (if available)
            if debug_info['mask'] is not None:
                # Need to transform mask to crop coordinates - simplified approach
                # This is approximate since we don't have the exact transformation
                ax2.text(5, 15, "Mask contour overlay\n(approximate)", 
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
                        fontsize=8)
        else:
            # Show failure case
            ax2.text(0.5, 0.5, f"Processing Failed\nROI: {'✓' if debug_info['roi_coords'] else '✗'}\n" +
                    f"Face: {'✓' if debug_info['face_data'] else '✗'}\n" +
                    f"Mask: {'✓' if debug_info['mask'] is not None else '✗'}",
                    ha='center', va='center', transform=ax2.transAxes,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="red", alpha=0.7),
                    fontsize=12, color='white')
            ax2.set_title("Processing Failed")
        
        ax2.axis('off')
        
        plt.tight_layout()
        
        # Convert to numpy array
        fig.canvas.draw()
        buf = fig.canvas.buffer_rgba()
        img_array = np.asarray(buf)
        img_array = img_array[:, :, :3]  # Remove alpha channel
        plt.close(fig)
        
        return img_array
    
    def visualize_sample_results(self, manifest_path: str, output_path: str, max_samples: int = 15):
        """Create visualization of sample results"""
        print(f"🎨 Creating lower-face ROI pipeline visualization...")
        
        # Load manifest
        df = pd.read_csv(manifest_path)
        
        # Sample clips (mix of different phrases)
        sample_clips = []
        phrases = df['phrase'].unique()
        clips_per_phrase = max(1, max_samples // len(phrases))
        
        for phrase in phrases[:max_samples]:
            phrase_clips = df[df['phrase'] == phrase].sample(n=min(clips_per_phrase, len(df[df['phrase'] == phrase])), 
                                                           random_state=42)
            sample_clips.append(phrase_clips)
        
        sample_df = pd.concat(sample_clips).head(max_samples)
        
        print(f"📊 Processing {len(sample_df)} sample clips...")
        
        # Process each clip and create visualizations
        visualizations = []
        
        for i, (_, row) in enumerate(sample_df.iterrows()):
            print(f"  Processing {i+1}/{len(sample_df)}: {row['phrase']}")
            
            # Download and extract frame
            frame = self.download_and_extract_frame(row['s3_key'])
            if frame is None:
                print(f"    ❌ Failed to download/extract frame")
                continue
            
            # Process frame through pipeline
            debug_info = self.process_frame_with_debug(frame)
            
            # Create visualization
            title = f"{row['phrase']} ({i+1}/{len(sample_df)})"
            viz = self.create_side_by_side_visualization(debug_info, title)
            visualizations.append(viz)
            
            if len(visualizations) >= max_samples:
                break
        
        if not visualizations:
            print("❌ No successful visualizations created")
            return
        
        # Combine all visualizations into a grid
        print(f"🖼️ Combining {len(visualizations)} visualizations...")
        
        # Arrange in grid (e.g., 3 columns)
        cols = 1  # Single column for better readability
        rows = len(visualizations)
        
        # Create combined image
        if visualizations:
            single_h, single_w = visualizations[0].shape[:2]
            combined_img = np.zeros((rows * single_h, cols * single_w, 3), dtype=np.uint8)
            
            for i, viz in enumerate(visualizations):
                row = i // cols
                col = i % cols
                y1, y2 = row * single_h, (row + 1) * single_h
                x1, x2 = col * single_w, (col + 1) * single_w
                combined_img[y1:y2, x1:x2] = viz
            
            # Save result
            cv2.imwrite(output_path, cv2.cvtColor(combined_img, cv2.COLOR_RGB2BGR))
            print(f"✅ Visualization saved: {output_path}")
            print(f"📏 Image size: {combined_img.shape[1]}×{combined_img.shape[0]}")
        else:
            print("❌ No visualizations to combine")


def main():
    parser = argparse.ArgumentParser(description='Visualize lower-face ROI pipeline results')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--manifest', default='tiny_test.csv', help='Manifest file to sample from')
    parser.add_argument('--output', default='lowerface_roi_sample_results.png', help='Output image path')
    parser.add_argument('--max-samples', type=int, default=12, help='Maximum number of samples to visualize')
    args = parser.parse_args()
    
    # Create visualizer
    visualizer = LowerfaceROIVisualizer(args.config)
    
    # Create visualization
    visualizer.visualize_sample_results(args.manifest, args.output, args.max_samples)
    
    print("🎉 Visualization completed!")


if __name__ == "__main__":
    main()
