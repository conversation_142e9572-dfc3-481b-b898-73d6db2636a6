# Large video datasets (too big for GitHub)
data/stage1_cropped_top7/
data/stage1M_motion_refined_full/
data/quick_stage1_cropped/
data/stage1_cropped_top7_screened/

# Original dataset (external)
~/Desktop/top*dataset*/

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.venv/
.venv311/
venv/
ENV/
env/
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Temporary files
*.tmp
*.temp
.cache/

# Model checkpoints (if any)
*.pth
*.pt
*.ckpt
*.h5
*.pkl

# Large reports (keep smaller ones)
reports/*_full_gallery.html
reports/stage1M_full_gallery.html

# Keep important files
!requirements.txt
!scripts/
!reports/*.csv
!reports/*.txt
!reports/cleaned_dataset_inspection.html
!reports/suspect_gallery.html
!reports/quick_crop_gallery.html
!README.md
!.gitignore
