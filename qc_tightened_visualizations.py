#!/usr/bin/env python3
"""
Generate QC visualizations for tightened MediaPipe landmark-anchored preprocessing.
Must be run in Python 3.11 environment.
"""

import os
import sys
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import glob
import random
from pathlib import Path

# Import MediaPipe preprocessor
from mediapipe_landmark_preprocessor import MediaPipeLandmarkPreprocessor


def create_tightened_analysis():
    """Create comprehensive analysis of tightened preprocessing"""
    
    # Load tightened results
    csv_path = 'data_processed_tightened/quality_logs/quality_scores.csv'
    if not os.path.exists(csv_path):
        print("❌ Tightened quality CSV not found!")
        return
    
    df = pd.read_csv(csv_path)
    
    # Create comprehensive analysis
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Tightened MediaPipe Landmark-Anchored Preprocessing Analysis', fontsize=16, fontweight='bold')
    
    # Coverage histogram
    ax1 = axes[0, 0]
    coverage_values = df['coverage_after'].values
    coverage_values = coverage_values[~np.isnan(coverage_values)]  # Remove NaN values
    
    if len(coverage_values) > 0:
        ax1.hist(coverage_values, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(0.35, color='red', linestyle='--', label='Min Accept (35%)')
        ax1.axvline(0.70, color='red', linestyle='--', label='Max Accept (70%)')
        ax1.axvline(0.45, color='green', linestyle='-', label='Target (45%)')
        ax1.set_xlabel('Lip Coverage After Adjustment')
        ax1.set_ylabel('Number of Clips')
        ax1.set_title('Distribution of Lip Coverage Values (Tightened)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
    
    # Landmark detection rate by phrase
    ax2 = axes[0, 1]
    phrase_landmarks = df.groupby('phrase')['frames_with_landmarks_pct'].mean().sort_values()
    phrase_landmarks.plot(kind='bar', ax=ax2, color='lightcoral')
    ax2.set_xlabel('Phrase')
    ax2.set_ylabel('Avg Landmark Detection %')
    ax2.set_title('Landmark Detection Rate by Phrase (Tightened)')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)
    
    # Mouth width distribution
    ax3 = axes[1, 0]
    mouth_widths = df['mouth_width_px'].values
    mouth_widths = mouth_widths[~np.isnan(mouth_widths) & (mouth_widths > 0)]
    
    if len(mouth_widths) > 0:
        ax3.hist(mouth_widths, bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
        ax3.set_xlabel('Mouth Width (pixels)')
        ax3.set_ylabel('Number of Clips')
        ax3.set_title('Distribution of Detected Mouth Widths (Tightened)')
        ax3.grid(True, alpha=0.3)
    
    # Summary statistics
    ax4 = axes[1, 1]
    ax4.axis('off')
    
    total_clips = len(df)
    valid_clips = df['valid'].sum()
    avg_landmarks_pct = df['frames_with_landmarks_pct'].mean()
    avg_coverage = df['coverage_after'].mean() if not df['coverage_after'].isna().all() else 0
    clips_with_landmarks = (df['frames_with_landmarks_pct'] > 0).sum()
    
    stats_text = f"""
    Tightened Processing Statistics:
    
    Total Clips: {total_clips}
    Valid Clips: {valid_clips} ({valid_clips/total_clips*100:.1f}%)
    Clips with ANY landmarks: {clips_with_landmarks}
    
    Landmark Detection:
    Avg Detection Rate: {avg_landmarks_pct:.1f}%
    Clips with >90% landmarks: {(df['frames_with_landmarks_pct'] >= 90).sum()}
    Clips with >50% landmarks: {(df['frames_with_landmarks_pct'] >= 50).sum()}
    
    Coverage Analysis:
    Avg Coverage: {avg_coverage:.3f}
    In range [0.35-0.70]: {((coverage_values >= 0.35) & (coverage_values <= 0.70)).sum() if len(coverage_values) > 0 else 0}
    Below 0.35: {(coverage_values < 0.35).sum() if len(coverage_values) > 0 else 0}
    Above 0.70: {(coverage_values > 0.70).sum() if len(coverage_values) > 0 else 0}
    
    Issues Identified:
    - Low landmark detection rate
    - Most clips have 0% landmarks
    - Possible video quality issues
    - Need to investigate face detection
    """
    
    ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, 
             fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig('qc_tightened_analysis.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 Tightened analysis saved to: qc_tightened_analysis.png")


def create_sample_preview():
    """Create preview of tightened crops with lip polygons"""
    
    # Load some processed clips
    clips_dir = 'data_processed_tightened/clips'
    if not os.path.exists(clips_dir):
        print("❌ No processed clips found!")
        return
    
    clip_files = glob.glob(os.path.join(clips_dir, '*.npy'))[:10]  # First 10 clips
    
    if not clip_files:
        print("❌ No clip files found!")
        return
    
    # Create preview grid: 10 clips × 4 frames
    fig, axes = plt.subplots(10, 4, figsize=(16, 40))
    fig.suptitle('Tightened MediaPipe Lip Crops Preview (10 clips × 4 frames)', 
                 fontsize=16, fontweight='bold')
    
    for clip_idx, clip_file in enumerate(clip_files):
        try:
            clip_array = np.load(clip_file)
            clip_name = os.path.basename(clip_file).replace('.npy', '')
            
            # Show 4 frames from the clip
            frame_indices = [0, len(clip_array)//3, 2*len(clip_array)//3, len(clip_array)-1]
            
            for frame_idx, frame_pos in enumerate(frame_indices):
                if frame_pos < len(clip_array):
                    frame = clip_array[frame_pos]
                    
                    ax = axes[clip_idx, frame_idx]
                    ax.imshow(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                    ax.set_title(f'{clip_name}\nFrame {frame_pos}', fontsize=8)
                    ax.axis('off')
                    
                    # Draw estimated lip region (since we don't have access to landmarks here)
                    h, w = frame.shape[:2]
                    center_x, center_y = w // 2, int(h * 0.6)
                    lip_width = int(w * 0.9)  # 90% width for tightened
                    lip_height = int(lip_width * 0.3)
                    
                    x1 = center_x - lip_width // 2
                    x2 = center_x + lip_width // 2
                    y1 = center_y - lip_height // 2
                    y2 = center_y + lip_height // 2
                    
                    # Draw rectangle overlay
                    rect = plt.Rectangle((x1, y1), lip_width, lip_height, 
                                       linewidth=2, edgecolor='lime', facecolor='none')
                    ax.add_patch(rect)
                    
                    # Draw center point
                    ax.plot(center_x, center_y, 'ro', markersize=3)
        
        except Exception as e:
            print(f"Error processing {clip_file}: {e}")
    
    plt.tight_layout()
    plt.savefig('qc_tightened_preview.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 Tightened preview saved to: qc_tightened_preview.png")


def investigate_landmark_detection():
    """Investigate why landmark detection is failing"""
    
    # Load manifest to get original video paths
    manifest_path = 'trial_manifest_tightened.csv'
    if not os.path.exists(manifest_path):
        print("❌ Manifest not found!")
        return
    
    df = pd.read_csv(manifest_path)
    
    # Test landmark detection on a few sample videos
    print("🔍 Investigating landmark detection on sample videos...")
    
    # Initialize preprocessor
    preprocessor = MediaPipeLandmarkPreprocessor()
    
    for idx, row in df.head(5).iterrows():
        video_path = row['s3_key']
        phrase = row['phrase']
        
        print(f"\n📹 Testing: {phrase} - {os.path.basename(video_path)}")
        
        try:
            # Load video
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                print("  ❌ Could not open video")
                continue
            
            # Test first few frames
            frame_count = 0
            landmarks_detected = 0
            
            for i in range(5):  # Test first 5 frames
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame_count += 1
                landmarks, mouth_width, lip_centroid = preprocessor.extract_lip_landmarks_and_metrics(frame)
                
                if landmarks is not None:
                    landmarks_detected += 1
                    print(f"  ✅ Frame {i}: Landmarks detected, mouth_width={mouth_width:.1f}")
                else:
                    print(f"  ❌ Frame {i}: No landmarks detected")
            
            cap.release()
            
            detection_rate = (landmarks_detected / frame_count * 100) if frame_count > 0 else 0
            print(f"  📊 Detection rate: {detection_rate:.1f}% ({landmarks_detected}/{frame_count})")
            
        except Exception as e:
            print(f"  ❌ Error: {e}")


def main():
    """Main QC visualization function"""
    print("🎯 QC Visualizations for Tightened MediaPipe Preprocessing")
    print("=" * 60)
    
    # Check MediaPipe availability
    try:
        import mediapipe as mp
        print("✅ MediaPipe available")
    except ImportError:
        print("❌ MediaPipe not available! Please run in Python 3.11 environment")
        return False
    
    # Create analysis
    create_tightened_analysis()
    
    # Create preview
    create_sample_preview()
    
    # Investigate landmark detection issues
    investigate_landmark_detection()
    
    print("\n✅ QC visualizations completed!")
    print("📁 Generated files:")
    print("  - qc_tightened_analysis.png")
    print("  - qc_tightened_preview.png")
    
    return True


if __name__ == "__main__":
    main()
