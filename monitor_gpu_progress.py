#!/usr/bin/env python3
"""
GPU Progress Monitor for Stage-3 Segmentation
Monitors processing progress, GPU utilization, and estimates completion time.
"""

import os
import time
import subprocess
import json
from pathlib import Path
from datetime import datetime, timedelta
import argparse

def get_gpu_info():
    """Get GPU utilization info using nvidia-smi."""
    try:
        result = subprocess.run([
            'nvidia-smi', '--query-gpu=name,memory.used,memory.total,utilization.gpu,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, check=True)
        
        lines = result.stdout.strip().split('\n')
        gpus = []
        for line in lines:
            parts = [p.strip() for p in line.split(',')]
            if len(parts) >= 5:
                gpus.append({
                    'name': parts[0],
                    'memory_used': int(parts[1]),
                    'memory_total': int(parts[2]),
                    'utilization': int(parts[3]),
                    'temperature': int(parts[4])
                })
        return gpus
    except:
        return []

def count_processed_videos(output_dir):
    """Count how many videos have been processed."""
    if not os.path.exists(output_dir):
        return 0
    
    # Count directories with _masked.mp4 files
    processed = 0
    for root, dirs, files in os.walk(output_dir):
        for file in files:
            if file.endswith('_masked.mp4'):
                processed += 1
    
    return processed

def count_total_videos(input_dir):
    """Count total input videos."""
    if not os.path.exists(input_dir):
        return 0
    
    extensions = ['.mp4', '.mov', '.mkv', '.avi', '.webm']
    total = 0
    for root, dirs, files in os.walk(input_dir):
        for file in files:
            if any(file.lower().endswith(ext) for ext in extensions):
                total += 1
    
    return total

def format_time(seconds):
    """Format seconds into human readable time."""
    if seconds < 60:
        return f"{seconds:.0f}s"
    elif seconds < 3600:
        return f"{seconds/60:.1f}m"
    else:
        return f"{seconds/3600:.1f}h"

def main():
    parser = argparse.ArgumentParser(description='Monitor GPU segmentation progress')
    parser.add_argument('--input_dir', default='data/stage1M_motion_refined_full', help='Input directory')
    parser.add_argument('--output_dir', default='data/stage3_segmented', help='Output directory')
    parser.add_argument('--interval', type=int, default=30, help='Update interval in seconds')
    
    args = parser.parse_args()
    
    print("🎮 GPU Segmentation Progress Monitor")
    print("=" * 50)
    
    # Get total video count
    total_videos = count_total_videos(args.input_dir)
    print(f"📹 Total videos to process: {total_videos}")
    
    if total_videos == 0:
        print(f"❌ No videos found in {args.input_dir}")
        return
    
    start_time = time.time()
    last_processed = 0
    last_time = start_time
    
    try:
        while True:
            current_time = time.time()
            processed = count_processed_videos(args.output_dir)
            
            # Calculate progress
            progress_pct = (processed / total_videos) * 100 if total_videos > 0 else 0
            remaining = total_videos - processed
            
            # Calculate processing rate
            time_elapsed = current_time - start_time
            if time_elapsed > 0:
                overall_rate = processed / time_elapsed * 60  # videos per minute
            else:
                overall_rate = 0
            
            # Calculate recent rate
            time_since_last = current_time - last_time
            if time_since_last > 0:
                recent_rate = (processed - last_processed) / time_since_last * 60
            else:
                recent_rate = 0
            
            # Estimate completion time
            if recent_rate > 0:
                eta_seconds = remaining / (recent_rate / 60)
                eta_str = format_time(eta_seconds)
            else:
                eta_str = "Unknown"
            
            # Get GPU info
            gpus = get_gpu_info()
            
            # Clear screen and display status
            os.system('clear' if os.name == 'posix' else 'cls')
            
            print("🎮 GPU Segmentation Progress Monitor")
            print("=" * 50)
            print(f"📊 Progress: {processed}/{total_videos} ({progress_pct:.1f}%)")
            print(f"⏱️  Elapsed: {format_time(time_elapsed)}")
            print(f"🚀 Overall rate: {overall_rate:.1f} videos/min")
            print(f"📈 Recent rate: {recent_rate:.1f} videos/min")
            print(f"⏰ ETA: {eta_str}")
            print(f"📅 Started: {datetime.fromtimestamp(start_time).strftime('%Y-%m-%d %H:%M:%S')}")
            
            if eta_str != "Unknown":
                completion_time = datetime.now() + timedelta(seconds=eta_seconds)
                print(f"🏁 Est. completion: {completion_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            print("\n" + "=" * 50)
            
            # Display GPU info
            if gpus:
                print("🎮 GPU Status:")
                for i, gpu in enumerate(gpus):
                    memory_pct = (gpu['memory_used'] / gpu['memory_total']) * 100
                    print(f"  GPU {i}: {gpu['name']}")
                    print(f"    Memory: {gpu['memory_used']}/{gpu['memory_total']}MB ({memory_pct:.1f}%)")
                    print(f"    Utilization: {gpu['utilization']}%")
                    print(f"    Temperature: {gpu['temperature']}°C")
            else:
                print("⚠️  GPU info not available")
            
            print("\n" + "=" * 50)
            print(f"🔄 Next update in {args.interval}s... (Ctrl+C to exit)")
            
            # Update for next iteration
            last_processed = processed
            last_time = current_time
            
            # Check if completed
            if processed >= total_videos:
                print("\n🎉 Processing completed!")
                break
            
            time.sleep(args.interval)
            
    except KeyboardInterrupt:
        print("\n👋 Monitor stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == '__main__':
    main()
