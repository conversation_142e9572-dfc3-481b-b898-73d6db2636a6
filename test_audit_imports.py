#!/usr/bin/env python3
"""Test imports for audit script"""

print("🔍 Testing audit script imports...")

try:
    import mediapipe as mp
    print("✅ MediaPipe imported")
    
    from scipy.spatial.distance import euclidean
    from scipy.stats import zscore
    print("✅ SciPy imported")
    
    from sklearn.linear_model import LogisticRegression
    from sklearn.model_selection import cross_val_predict, StratifiedKFold
    from sklearn.preprocessing import StandardScaler
    print("✅ Scikit-learn imported")
    
    from jinja2 import Template
    print("✅ Jinja2 imported")
    
    print("🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
    exit(1)
