#!/usr/bin/env python3
"""
Complete Pipeline Runner for ICU Lipreading
Orchestrates the entire pipeline from manifest building to training
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(command, description, check=True):
    """Run a shell command with logging"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"Command: {command}")
    print()
    
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=False)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully!")
        else:
            print(f"❌ {description} failed with return code {result.returncode}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        return False
    except KeyboardInterrupt:
        print(f"\n⚠️ {description} interrupted by user")
        return False
    
    return True

def check_prerequisites():
    """Check if all prerequisites are met"""
    print("🔍 Checking prerequisites...")
    
    # Check if config exists
    if not os.path.exists('configs/training.yaml'):
        print("❌ configs/training.yaml not found")
        return False
    
    # Check if SAM checkpoint exists
    if not os.path.exists('checkpoints/sam_vit_b.pth'):
        print("❌ SAM checkpoint not found at checkpoints/sam_vit_b.pth")
        return False
    
    # Check if AWS credentials are configured
    try:
        result = subprocess.run(['aws', 'sts', 'get-caller-identity'], 
                              capture_output=True, text=True, check=True)
        print("✅ AWS credentials configured")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ AWS credentials not configured. Run 'aws configure'")
        return False
    
    # Check Python environment
    try:
        import torch
        import ultralytics
        import segment_anything
        print("✅ Core dependencies available")
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False
    
    print("✅ All prerequisites met!")
    return True

def main():
    parser = argparse.ArgumentParser(description='Run complete ICU Lipreading pipeline')
    parser.add_argument('--step', choices=[
        'all', 'manifests', 'preprocess', 'train', 'evaluate', 'serve'
    ], default='all', help='Pipeline step to run')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--skip-checks', action='store_true', help='Skip prerequisite checks')
    
    args = parser.parse_args()
    
    print("🎯 ICU Lipreading Pipeline Runner")
    print("=" * 60)
    
    # Check prerequisites
    if not args.skip_checks and not check_prerequisites():
        print("\n❌ Prerequisites not met. Fix issues above or use --skip-checks")
        sys.exit(1)
    
    # Define pipeline steps
    steps = {
        'manifests': {
            'command': f'python src/manifest_builder.py --config {args.config}',
            'description': 'Building speaker-disjoint manifests'
        },
        'preprocess_train': {
            'command': f'python src/preprocess.py --config {args.config} --manifest manifests/train.csv',
            'description': 'Preprocessing training videos'
        },
        'preprocess_val': {
            'command': f'python src/preprocess.py --config {args.config} --manifest manifests/val.csv',
            'description': 'Preprocessing validation videos'
        },
        'preprocess_test': {
            'command': f'python src/preprocess.py --config {args.config} --manifest manifests/test.csv',
            'description': 'Preprocessing test videos'
        },
        'train': {
            'command': f'python src/train.py --config {args.config}',
            'description': 'Training Mobile3DTiny + BiGRU model'
        },
        'evaluate': {
            'command': f'python src/evaluate.py --config {args.config} --checkpoint checkpoints/best_model.pth',
            'description': 'Evaluating trained model'
        },
        'serve': {
            'command': f'python src/infer_server.py',
            'description': 'Starting inference server'
        }
    }
    
    # Determine which steps to run
    if args.step == 'all':
        step_order = ['manifests', 'preprocess_train', 'preprocess_val', 'preprocess_test', 'train', 'evaluate']
    elif args.step == 'preprocess':
        step_order = ['preprocess_train', 'preprocess_val', 'preprocess_test']
    else:
        step_order = [args.step]
    
    # Run selected steps
    success_count = 0
    total_steps = len(step_order)
    
    for step_name in step_order:
        if step_name not in steps:
            print(f"❌ Unknown step: {step_name}")
            continue
        
        step = steps[step_name]
        success = run_command(step['command'], step['description'])
        
        if success:
            success_count += 1
        else:
            print(f"\n💥 Pipeline failed at step: {step_name}")
            print("Check the error messages above and fix any issues.")
            sys.exit(1)
    
    # Final summary
    print(f"\n{'='*60}")
    print(f"🎉 Pipeline Summary")
    print(f"{'='*60}")
    print(f"Completed: {success_count}/{total_steps} steps")
    
    if success_count == total_steps:
        print("✅ All steps completed successfully!")
        
        if args.step == 'all' or 'train' in step_order:
            print("\n📊 Next steps:")
            print("1. Check training results in checkpoints/training_history.csv")
            print("2. Review evaluation metrics in manifests/evaluation_results/")
            print("3. Start inference server: python src/infer_server.py")
            print("4. Test API: curl -X POST http://localhost:8000/predict -F 'video=@test.mp4'")
    else:
        print("❌ Some steps failed. Check error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
