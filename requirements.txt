# Core ML/DL
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.21.0
scikit-learn>=1.0.0

# Computer Vision - Pinned to Stable Versions
opencv-python>=4.8.0,<5.0.0
ultralytics>=8.0.0,<9.0.0
segment-anything>=1.0,<2.0.0
insightface>=0.7.0,<1.0.0
mediapipe>=0.10.0,<1.0.0
scikit-image>=0.21.0,<1.0.0
onnxruntime-gpu>=1.15.0,<2.0.0

# Data Processing
pandas>=1.3.0
pillow>=8.0.0
imageio>=2.9.0
imageio-ffmpeg>=0.4.0

# AWS
boto3>=1.26.0
s3fs>=2023.1.0

# API
fastapi>=0.68.0
uvicorn>=0.15.0
python-multipart>=0.0.5

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Utilities
tqdm>=4.65.0,<5.0.0
pyyaml>=6.0
jinja2>=3.1.0,<4.0.0
toml>=0.10.0,<1.0.0
python-multipart>=0.0.5

# Optional: MediaPipe (requires Python 3.11)
# mediapipe>=0.10.0
