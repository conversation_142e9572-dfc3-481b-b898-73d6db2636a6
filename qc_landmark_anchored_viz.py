#!/usr/bin/env python3
"""
QC visualizations for landmark-anchored lip cropping.
Generate preview images and coverage histogram as required.
"""

import os
import sys
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import glob
import random
from pathlib import Path

# Add src to path
sys.path.append('src')


def load_processed_clips_and_metadata():
    """Load processed clips and quality metadata"""
    clips_dir = 'data_processed_trial_anchored/clips'
    csv_path = 'data_processed_trial_anchored/quality_logs/quality_scores.csv'
    
    if not os.path.exists(csv_path):
        print("❌ Quality CSV not found!")
        return None, None
    
    # Load quality data
    df = pd.read_csv(csv_path)
    print(f"📊 Loaded quality data for {len(df)} clips")
    
    # Load clip data
    clip_data = {}
    for _, row in df.iterrows():
        clip_id = row['clip_id']
        clip_path = os.path.join(clips_dir, f'{clip_id}.npy')
        
        if os.path.exists(clip_path):
            try:
                clip_array = np.load(clip_path)
                clip_data[clip_id] = {
                    'frames': clip_array,
                    'phrase': row['phrase'],
                    'valid': row['valid'],
                    'coverage': row['avg_coverage_after'],
                    'landmarks_pct': row['frames_with_landmarks_pct']
                }
            except Exception as e:
                print(f"⚠️ Error loading {clip_path}: {e}")
    
    print(f"✅ Loaded {len(clip_data)} clip arrays")
    return df, clip_data


def create_lip_coverage_histogram(df, output_file="qc_lip_coverage_hist.png"):
    """Create histogram of lip coverage values"""
    coverage_values = df['avg_coverage_after'].values
    
    plt.figure(figsize=(12, 8))
    
    # Main histogram
    plt.subplot(2, 2, 1)

    # Check if all values are the same
    unique_values = np.unique(coverage_values)
    if len(unique_values) == 1:
        # All values are the same - create a bar chart instead
        plt.bar([unique_values[0]], [len(coverage_values)], width=0.01, alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(0.30, color='red', linestyle='--', label='Min Accept (30%)')
        plt.axvline(0.75, color='red', linestyle='--', label='Max Accept (75%)')
        plt.xlim(0, 1.0)
        plt.text(unique_values[0], len(coverage_values)/2, f'All clips: {unique_values[0]:.3f}',
                ha='center', va='center', fontweight='bold')
    else:
        # Normal histogram
        plt.hist(coverage_values, bins=min(30, len(unique_values)), alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(0.30, color='red', linestyle='--', label='Min Accept (30%)')
        plt.axvline(0.75, color='red', linestyle='--', label='Max Accept (75%)')

    plt.xlabel('Lip Coverage After Adjustment')
    plt.ylabel('Number of Clips')
    plt.title('Distribution of Lip Coverage Values')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Coverage by phrase
    plt.subplot(2, 2, 2)
    phrase_coverage = df.groupby('phrase')['avg_coverage_after'].mean().sort_values()
    phrase_coverage.plot(kind='bar', color='lightcoral')
    plt.xlabel('Phrase')
    plt.ylabel('Average Coverage')
    plt.title('Average Coverage by Phrase')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # Acceptance rate by phrase
    plt.subplot(2, 2, 3)
    acceptance_rate = df.groupby('phrase')['valid'].mean() * 100
    acceptance_rate.plot(kind='bar', color='lightgreen')
    plt.xlabel('Phrase')
    plt.ylabel('Acceptance Rate (%)')
    plt.title('Acceptance Rate by Phrase')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # Summary statistics
    plt.subplot(2, 2, 4)
    plt.axis('off')
    
    stats_text = f"""
    Coverage Statistics:
    
    Total Clips: {len(coverage_values)}
    Mean Coverage: {np.mean(coverage_values):.3f}
    Std Coverage: {np.std(coverage_values):.3f}
    Min Coverage: {np.min(coverage_values):.3f}
    Max Coverage: {np.max(coverage_values):.3f}
    
    Acceptance Rates:
    Within Range [0.30-0.75]: {np.sum((coverage_values >= 0.30) & (coverage_values <= 0.75))} ({np.sum((coverage_values >= 0.30) & (coverage_values <= 0.75))/len(coverage_values)*100:.1f}%)
    Below 0.30: {np.sum(coverage_values < 0.30)} ({np.sum(coverage_values < 0.30)/len(coverage_values)*100:.1f}%)
    Above 0.75: {np.sum(coverage_values > 0.75)} ({np.sum(coverage_values > 0.75)/len(coverage_values)*100:.1f}%)
    
    Valid Clips (≥90% landmarks): {df['valid'].sum()} ({df['valid'].mean()*100:.1f}%)
    """
    
    plt.text(0.1, 0.9, stats_text, transform=plt.gca().transAxes, 
             fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"💾 Coverage histogram saved to: {output_file}")


def create_qc_preview(clip_data, num_clips=10, frames_per_clip=4, output_file="qc_lip_tight_preview.png"):
    """Create QC preview showing 10 clips × 4 frames with lip regions highlighted"""
    
    # Select random clips for preview
    clip_ids = list(clip_data.keys())
    selected_clips = random.sample(clip_ids, min(num_clips, len(clip_ids)))
    
    # Create figure
    fig, axes = plt.subplots(num_clips, frames_per_clip, figsize=(16, 4*num_clips))
    fig.suptitle('QC: Landmark-Anchored Lip Cropping Preview\n'
                 'Green rectangles indicate estimated lip regions in 96×96 crops', 
                 fontsize=16, fontweight='bold')
    
    # Handle single clip case
    if num_clips == 1:
        axes = axes.reshape(1, frames_per_clip)
    
    for clip_idx, clip_id in enumerate(selected_clips):
        clip_info = clip_data[clip_id]
        frames = clip_info['frames']
        phrase = clip_info['phrase']
        coverage = clip_info['coverage']
        valid = clip_info['valid']
        
        # Select frames to display
        frame_indices = np.linspace(0, len(frames)-1, frames_per_clip, dtype=int)
        
        for frame_idx, frame_array_idx in enumerate(frame_indices):
            if frame_array_idx < len(frames):
                frame = frames[frame_array_idx]
                
                # Draw estimated lip region (simplified - green rectangle in center)
                frame_with_overlay = frame.copy()
                h, w = frame.shape[:2]
                
                # Estimate lip region based on target coverage
                # Assume lips are centered and occupy target percentage
                target_ratio = 0.70  # 70% of crop width
                lip_width = int(w * target_ratio)
                lip_height = int(lip_width * 0.3)  # Rough aspect ratio
                
                center_x, center_y = w // 2, int(h * 0.6)  # Slightly below center
                
                x1 = center_x - lip_width // 2
                x2 = center_x + lip_width // 2
                y1 = center_y - lip_height // 2
                y2 = center_y + lip_height // 2
                
                # Draw green rectangle
                cv2.rectangle(frame_with_overlay, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # Draw center point
                cv2.circle(frame_with_overlay, (center_x, center_y), 3, (0, 255, 0), -1)
                
                ax = axes[clip_idx, frame_idx]
                ax.imshow(cv2.cvtColor(frame_with_overlay, cv2.COLOR_BGR2RGB))
                
                status = "✓ VALID" if valid else "✗ INVALID"
                ax.set_title(f'{clip_id}\nFrame {frame_idx+1} | {phrase}\nCov: {coverage:.3f} | {status}', fontsize=8)
                ax.axis('off')
            else:
                # Empty subplot
                axes[clip_idx, frame_idx].axis('off')
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"💾 QC preview saved to: {output_file}")


def create_failure_analysis(df, clip_data, output_file="qc_lip_failures.png"):
    """Create visualization of worst failure cases"""
    
    # Find clips with lowest coverage or invalid status
    failure_candidates = df[
        (df['avg_coverage_after'] < 0.30) | 
        (df['avg_coverage_after'] > 0.75) | 
        (df['valid'] == False)
    ].copy()
    
    if len(failure_candidates) == 0:
        # If no failures, show clips with lowest coverage
        failure_candidates = df.nsmallest(12, 'avg_coverage_after')
    
    failure_candidates = failure_candidates.head(12)  # Top 12 failures
    
    if len(failure_candidates) == 0:
        print("⚠️ No failure cases found to analyze")
        return
    
    # Create visualization
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    fig.suptitle('QC: Failure Analysis - Worst 12 Cases\n'
                 'Red rectangles indicate problematic lip regions', 
                 fontsize=16, fontweight='bold')
    
    axes = axes.flatten()
    
    for idx, (_, row) in enumerate(failure_candidates.iterrows()):
        if idx >= 12:
            break
            
        clip_id = row['clip_id']
        
        if clip_id in clip_data:
            clip_info = clip_data[clip_id]
            frames = clip_info['frames']
            phrase = clip_info['phrase']
            coverage = clip_info['coverage']
            
            # Show first frame with red overlay
            frame = frames[0]
            frame_with_overlay = frame.copy()
            h, w = frame.shape[:2]
            
            # Draw red rectangle to indicate problem area
            center_x, center_y = w // 2, int(h * 0.6)
            lip_width = int(w * 0.70)
            lip_height = int(lip_width * 0.3)
            
            x1 = center_x - lip_width // 2
            x2 = center_x + lip_width // 2
            y1 = center_y - lip_height // 2
            y2 = center_y + lip_height // 2
            
            cv2.rectangle(frame_with_overlay, (x1, y1), (x2, y2), (0, 0, 255), 2)
            
            ax = axes[idx]
            ax.imshow(cv2.cvtColor(frame_with_overlay, cv2.COLOR_BGR2RGB))
            
            failure_reason = "Low Coverage" if coverage < 0.30 else "High Coverage" if coverage > 0.75 else "Invalid"
            ax.set_title(f'{clip_id}\n{phrase}\nCov: {coverage:.3f}\n{failure_reason}', fontsize=8)
            ax.axis('off')
        else:
            axes[idx].axis('off')
    
    # Hide unused subplots
    for idx in range(len(failure_candidates), 12):
        axes[idx].axis('off')
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"💾 Failure analysis saved to: {output_file}")


def main():
    """Main QC visualization function"""
    print("🎯 QC Visualizations for Landmark-Anchored Lip Cropping")
    print("=" * 60)
    
    # Load data
    df, clip_data = load_processed_clips_and_metadata()
    
    if df is None or clip_data is None:
        print("❌ Failed to load data!")
        return False
    
    # Generate visualizations
    print("\n📊 Generating coverage histogram...")
    create_lip_coverage_histogram(df)
    
    print("\n🔍 Generating QC preview...")
    create_qc_preview(clip_data)
    
    print("\n⚠️ Generating failure analysis...")
    create_failure_analysis(df, clip_data)
    
    # Print summary
    print(f"\n📋 QC Summary:")
    print("=" * 50)
    print(f"Total clips processed: {len(df)}")
    print(f"Valid clips (≥90% landmarks): {df['valid'].sum()} ({df['valid'].mean()*100:.1f}%)")
    print(f"Average coverage: {df['avg_coverage_after'].mean():.3f}")
    print(f"Coverage std dev: {df['avg_coverage_after'].std():.3f}")
    
    coverage_in_range = np.sum((df['avg_coverage_after'] >= 0.30) & (df['avg_coverage_after'] <= 0.75))
    print(f"Coverage in target range [0.30-0.75]: {coverage_in_range} ({coverage_in_range/len(df)*100:.1f}%)")
    
    print("\n✅ QC visualizations completed!")
    print("📁 Generated files:")
    print("  - qc_lip_coverage_hist.png")
    print("  - qc_lip_tight_preview.png") 
    print("  - qc_lip_failures.png")
    print("\n🔍 Please inspect these files before proceeding with full preprocessing!")
    
    return True


if __name__ == "__main__":
    main()
