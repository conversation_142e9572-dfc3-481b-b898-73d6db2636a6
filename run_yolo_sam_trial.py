#!/usr/bin/env python3
"""
Run QC trial for YOLO+SAM lip segmentation mode.

QC (MANDATORY GATE, no full run yet)
- Build a stratified TRIAL of 100 clips (≥10 per phrase).
- Produce:
  1) qc_yolo_sam_side_by_side.png — for the SAME 20 clips, show:
     LEFT: original frame with face box + SAM mask outline,
     RIGHT: 96×96 crop with mask overlay and lipline.
  2) qc_yolo_sam_preview.png — 10 clips × 4 frames (only the final 96×96 crops).
  3) qc_yolo_sam_failures.png — worst 12 with notes why they failed (no face, mask too small, etc.).
  4) CSV data_processed_trial_yolosam/quality.csv with:
     clip_id, phrase, frames_with_mask_pct, motion_score,
     mask_area_mean, mask_width_ratio_mean, crop_w, crop_h, valid
"""

import os
import sys
import pandas as pd
import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import argparse
from tqdm import tqdm
import glob

# Add src to path
sys.path.append('src')
from preprocess import VideoPreprocessor
from utils import load_config, setup_logging


def create_stratified_trial_manifest(original_manifest: str, output_manifest: str, 
                                   target_total: int = 100, min_per_phrase: int = 10):
    """Create a stratified sample manifest for the trial"""
    df = pd.read_csv(original_manifest)
    
    # Get unique phrases
    phrases = df['phrase'].unique()
    print(f"📊 Found {len(phrases)} phrases: {list(phrases)}")
    
    # Calculate samples per phrase
    samples_per_phrase = max(min_per_phrase, target_total // len(phrases))
    
    # Sample from each phrase
    sampled_dfs = []
    for phrase in phrases:
        phrase_df = df[df['phrase'] == phrase]
        n_available = len(phrase_df)
        n_sample = min(samples_per_phrase, n_available)
        
        if n_sample > 0:
            sampled = phrase_df.sample(n=n_sample, random_state=42)
            sampled_dfs.append(sampled)
            print(f"  {phrase}: {n_sample}/{n_available} samples")
    
    # Combine and save
    trial_df = pd.concat(sampled_dfs, ignore_index=True)
    trial_df.to_csv(output_manifest, index=False)
    
    print(f"✅ Created trial manifest with {len(trial_df)} samples")
    print(f"💾 Saved to: {output_manifest}")
    
    return output_manifest


def run_preprocessing_trial(config_path: str, trial_manifest: str):
    """Run preprocessing on trial data"""
    print("🚀 Running YOLO+SAM preprocessing trial...")
    
    # Load config
    config = load_config(config_path)
    
    # Create preprocessor with yolo_sam_lips mode
    preprocessor = VideoPreprocessor(config, mode='yolo_sam_lips')
    
    # Process trial manifest
    preprocessor.process_from_manifest(trial_manifest)
    
    print("✅ Preprocessing trial completed!")


def generate_qc_visualizations():
    """Generate all required QC visualizations"""
    print("🎨 Generating QC visualizations...")
    
    # Load quality data
    quality_csv = 'data_processed_yolo_sam/quality_logs/quality_scores.csv'
    if not os.path.exists(quality_csv):
        print(f"❌ Quality CSV not found: {quality_csv}")
        return
    
    df = pd.read_csv(quality_csv)
    print(f"📊 Loaded quality data for {len(df)} clips")
    
    # Generate visualizations
    generate_side_by_side_visualization(df)
    generate_preview_visualization(df)
    generate_failures_visualization(df)
    
    print("✅ QC visualizations completed!")


def generate_side_by_side_visualization(df: pd.DataFrame):
    """Generate qc_yolo_sam_side_by_side.png with original frames + crops"""
    print("📸 Generating side-by-side visualization...")
    
    # Select 20 clips (2 per phrase if possible)
    phrases = df['phrase'].unique()
    selected_clips = []
    
    for phrase in phrases[:10]:  # Up to 10 phrases
        phrase_clips = df[df['phrase'] == phrase]
        if len(phrase_clips) >= 2:
            # Select best 2 clips from this phrase
            if 'frames_with_mask_pct' in phrase_clips.columns:
                best_clips = phrase_clips.nlargest(2, 'frames_with_mask_pct')
            else:
                best_clips = phrase_clips.head(2)
            selected_clips.extend(best_clips.to_dict('records'))
        elif len(phrase_clips) > 0:
            selected_clips.append(phrase_clips.iloc[0].to_dict())
    
    # Limit to 20 clips
    selected_clips = selected_clips[:20]
    
    # Create visualization
    fig, axes = plt.subplots(len(selected_clips), 2, figsize=(12, 4*len(selected_clips)))
    fig.suptitle('YOLO+SAM Side-by-Side - Original Frames vs 96×96 Crops', 
                 fontsize=16, fontweight='bold')
    
    if len(selected_clips) == 1:
        axes = axes.reshape(1, 2)
    
    for clip_idx, clip_row in enumerate(selected_clips):
        clip_id = clip_row['clip_id']
        phrase = clip_row['phrase']
        
        # Load clip data
        clip_file = f'data_processed_yolo_sam/clips/{clip_id}.npy'
        if os.path.exists(clip_file):
            clip_data = np.load(clip_file)
            
            # Show middle frame
            if len(clip_data) > 0:
                middle_frame = clip_data[len(clip_data)//2]
                
                # Left: Original frame (placeholder - would need original frame access)
                ax_left = axes[clip_idx, 0]
                ax_left.text(0.5, 0.5, f'Original Frame\n{phrase}\n(Face + SAM mask)', 
                           ha='center', va='center', transform=ax_left.transAxes,
                           bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
                ax_left.set_title(f'{phrase} - Original', fontsize=10)
                ax_left.axis('off')
                
                # Right: 96x96 crop
                ax_right = axes[clip_idx, 1]
                ax_right.imshow(cv2.cvtColor(middle_frame, cv2.COLOR_BGR2RGB))
                ax_right.set_title(f'{phrase} - 96×96 Crop', fontsize=10)
                ax_right.axis('off')
            else:
                for ax in axes[clip_idx]:
                    ax.text(0.5, 0.5, f'Clip {clip_id}\nNot Found', 
                           ha='center', va='center', transform=ax.transAxes)
                    ax.axis('off')
        else:
            for ax in axes[clip_idx]:
                ax.text(0.5, 0.5, f'Clip {clip_id}\nNot Found', 
                       ha='center', va='center', transform=ax.transAxes)
                ax.axis('off')
    
    plt.tight_layout()
    plt.savefig('qc_yolo_sam_side_by_side.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 Saved: qc_yolo_sam_side_by_side.png")


def generate_preview_visualization(df: pd.DataFrame):
    """Generate qc_yolo_sam_preview.png — 10 clips × 4 frames (96×96 crops)"""
    print("📸 Generating preview visualization...")
    
    # Select 10 clips (stratified by phrase if possible)
    phrases = df['phrase'].unique()
    selected_clips = []
    
    for phrase in phrases[:10]:  # Up to 10 phrases
        phrase_clips = df[df['phrase'] == phrase]
        if len(phrase_clips) > 0:
            # Select best clip from this phrase
            if 'frames_with_mask_pct' in phrase_clips.columns:
                best_clip = phrase_clips.loc[phrase_clips['frames_with_mask_pct'].idxmax()]
            else:
                best_clip = phrase_clips.iloc[0]
            selected_clips.append(best_clip)
    
    # Fill with random clips if needed
    while len(selected_clips) < 10 and len(selected_clips) < len(df):
        remaining = df[~df['clip_id'].isin([c['clip_id'] for c in selected_clips])]
        if len(remaining) > 0:
            selected_clips.append(remaining.iloc[0])
        else:
            break
    
    # Create visualization
    fig, axes = plt.subplots(len(selected_clips), 4, figsize=(16, 4*len(selected_clips)))
    fig.suptitle('YOLO+SAM Preview - 10 Clips × 4 Frames (96×96 crops)', 
                 fontsize=16, fontweight='bold')
    
    if len(selected_clips) == 1:
        axes = axes.reshape(1, 4)
    
    for clip_idx, clip_row in enumerate(selected_clips):
        clip_id = clip_row['clip_id']
        phrase = clip_row['phrase']
        
        # Load clip data
        clip_file = f'data_processed_yolo_sam/clips/{clip_id}.npy'
        if os.path.exists(clip_file):
            clip_data = np.load(clip_file)
            
            # Show 4 frames: first, 1/3, 2/3, last
            frame_indices = [0, len(clip_data)//3, 2*len(clip_data)//3, len(clip_data)-1]
            
            for frame_idx, frame_pos in enumerate(frame_indices):
                if frame_pos < len(clip_data):
                    frame = clip_data[frame_pos]
                    
                    ax = axes[clip_idx, frame_idx]
                    ax.imshow(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                    ax.set_title(f'{phrase}\nFrame {frame_pos}', fontsize=8)
                    ax.axis('off')
                else:
                    axes[clip_idx, frame_idx].axis('off')
        else:
            # Show placeholder
            for frame_idx in range(4):
                axes[clip_idx, frame_idx].text(0.5, 0.5, f'Clip {clip_id}\nNot Found', 
                                             ha='center', va='center', 
                                             transform=axes[clip_idx, frame_idx].transAxes)
                axes[clip_idx, frame_idx].axis('off')
    
    plt.tight_layout()
    plt.savefig('qc_yolo_sam_preview.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 Saved: qc_yolo_sam_preview.png")


def generate_failures_visualization(df: pd.DataFrame):
    """Generate qc_yolo_sam_failures.png — worst 12 clips with failure reasons"""
    print("⚠️  Generating failures visualization...")
    
    # Find worst clips by mask coverage and motion
    worst_by_mask = df.nsmallest(6, 'frames_with_mask_pct') if 'frames_with_mask_pct' in df.columns else df.head(6)
    worst_by_motion = df.nsmallest(6, 'motion_score') if 'motion_score' in df.columns else df.head(6)
    
    # Combine and deduplicate
    worst_clips = pd.concat([worst_by_mask, worst_by_motion]).drop_duplicates(subset=['clip_id'])
    worst_clips = worst_clips.head(12)  # Limit to 12
    
    # Create visualization
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    fig.suptitle('YOLO+SAM Failures - Worst 12 Clips by Mask Coverage/Motion', 
                 fontsize=16, fontweight='bold')
    
    axes = axes.flatten()
    
    for idx, (_, clip_row) in enumerate(worst_clips.iterrows()):
        if idx >= 12:
            break
            
        clip_id = clip_row['clip_id']
        phrase = clip_row['phrase']
        mask_pct = clip_row.get('frames_with_mask_pct', 0)
        motion = clip_row.get('motion_score', 0)
        error = clip_row.get('error', 'unknown')
        
        ax = axes[idx]
        
        # Try to load and show middle frame
        clip_file = f'data_processed_yolo_sam/clips/{clip_id}.npy'
        if os.path.exists(clip_file):
            clip_data = np.load(clip_file)
            if len(clip_data) > 0:
                middle_frame = clip_data[len(clip_data)//2]
                ax.imshow(cv2.cvtColor(middle_frame, cv2.COLOR_BGR2RGB))
        
        ax.set_title(f'{phrase}\nMask: {mask_pct:.1f}%\nMotion: {motion:.4f}\n{error}', fontsize=8)
        ax.axis('off')
    
    # Hide unused subplots
    for idx in range(len(worst_clips), 12):
        axes[idx].axis('off')
    
    plt.tight_layout()
    plt.savefig('qc_yolo_sam_failures.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("💾 Saved: qc_yolo_sam_failures.png")


def main():
    parser = argparse.ArgumentParser(description='Run YOLO+SAM QC trial')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--manifest', required=True, help='Original manifest CSV file')
    parser.add_argument('--skip_processing', action='store_true', 
                       help='Skip preprocessing, only generate visualizations')
    args = parser.parse_args()
    
    print("🎯 YOLO+SAM QC Trial")
    print("=" * 50)
    
    # Create trial manifest
    trial_manifest = 'trial_yolo_sam_manifest.csv'
    if not args.skip_processing:
        create_stratified_trial_manifest(args.manifest, trial_manifest)
        
        # Run preprocessing
        run_preprocessing_trial(args.config, trial_manifest)
    
    # Generate QC visualizations
    generate_qc_visualizations()
    
    print("\n✅ YOLO+SAM QC trial completed!")
    print("📁 Generated files:")
    print("  - qc_yolo_sam_side_by_side.png")
    print("  - qc_yolo_sam_preview.png") 
    print("  - qc_yolo_sam_failures.png")
    print("  - data_processed_yolo_sam/quality_logs/quality_scores.csv")
    print("\n🔍 Review these files before proceeding to full processing.")
    print("⚠️  DO NOT proceed until side-by-side visualization is approved and CSV shows ≥85% valid clips.")


if __name__ == "__main__":
    main()
