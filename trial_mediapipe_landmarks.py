#!/usr/bin/env python3
"""
Trial run for true MediaPipe landmark-anchored lip cropping.
Must be run in Python 3.11 environment with MediaPipe.
"""

import os
import sys
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import glob
import random
from pathlib import Path
from tqdm import tqdm

# Import our MediaPipe preprocessor
from mediapipe_landmark_preprocessor import MediaPipeLandmarkPreprocessor


def create_stratified_trial_manifest(num_samples=100):
    """Create stratified trial manifest: ≥10 per phrase if possible, varied speakers"""
    print("🔍 Creating stratified trial manifest...")
    
    video_pattern = "../top 10 dataset 6.9.25/**/*.webm"
    all_videos = glob.glob(video_pattern, recursive=True)
    
    if not all_videos:
        print("❌ No video files found!")
        return None
    
    # Parse videos by phrase and speaker
    phrase_speaker_videos = {}
    for video_path in all_videos:
        try:
            path_parts = video_path.split('/')
            if len(path_parts) >= 6:
                phrase = path_parts[-6]
                filename = path_parts[-1]
                filename_parts = filename.replace('.webm', '').split('__')
                if len(filename_parts) >= 2:
                    speaker_id = filename_parts[1]
                    
                    if phrase not in phrase_speaker_videos:
                        phrase_speaker_videos[phrase] = {}
                    if speaker_id not in phrase_speaker_videos[phrase]:
                        phrase_speaker_videos[phrase][speaker_id] = []
                    
                    phrase_speaker_videos[phrase][speaker_id].append(video_path)
        except:
            continue
    
    if not phrase_speaker_videos:
        return None
    
    # Stratified sampling: ≥10 per phrase if possible
    sampled_videos = []
    target_per_phrase = max(10, num_samples // len(phrase_speaker_videos))
    
    for phrase, speaker_videos in phrase_speaker_videos.items():
        phrase_samples = []
        
        # Sample from different speakers to get variety
        speakers = list(speaker_videos.keys())
        random.shuffle(speakers)
        
        for speaker in speakers:
            if len(phrase_samples) >= target_per_phrase:
                break
            
            # Sample videos from this speaker
            speaker_vids = speaker_videos[speaker]
            num_from_speaker = min(3, len(speaker_vids))  # Max 3 per speaker per phrase
            selected = random.sample(speaker_vids, num_from_speaker)
            
            for video_path in selected:
                if len(phrase_samples) < target_per_phrase:
                    phrase_samples.append({
                        'video_path': video_path,
                        'phrase': phrase,
                        'speaker_id': speaker
                    })
        
        sampled_videos.extend(phrase_samples)
        print(f"  {phrase}: {len(phrase_samples)} videos from {len(set(s['speaker_id'] for s in phrase_samples))} speakers")
    
    # If we have too many, randomly sample down to target
    if len(sampled_videos) > num_samples:
        sampled_videos = random.sample(sampled_videos, num_samples)
    
    # Create manifest
    manifest_data = []
    for item in sampled_videos:
        manifest_data.append({
            's3_key': os.path.relpath(item['video_path'], '.'),
            'phrase': item['phrase'],
            'speaker_id': item['speaker_id']
        })
    
    # Save manifest
    manifest_df = pd.DataFrame(manifest_data)
    manifest_path = 'trial_manifest_mediapipe.csv'
    manifest_df.to_csv(manifest_path, index=False)
    
    print(f"✅ Created stratified manifest with {len(manifest_data)} videos")
    print(f"   Phrases: {len(set(m['phrase'] for m in manifest_data))}")
    print(f"   Speakers: {len(set(m['speaker_id'] for m in manifest_data))}")
    
    return manifest_path


def main():
    """Main trial run function"""
    print("🎯 True MediaPipe Landmark-Anchored Lip Cropping - Trial Run")
    print("=" * 70)
    
    # Check if we're in the right environment
    try:
        import mediapipe as mp
        print("✅ MediaPipe available - proceeding with true landmark detection")
    except ImportError:
        print("❌ MediaPipe not available! Please run in Python 3.11 environment:")
        print("   source .venv311/bin/activate")
        print("   python trial_mediapipe_landmarks.py")
        return False
    
    # Create stratified trial manifest
    manifest_path = create_stratified_trial_manifest(100)
    if not manifest_path:
        return False
    
    # Initialize preprocessor
    preprocessor = MediaPipeLandmarkPreprocessor()
    
    # Load manifest and process
    df = pd.read_csv(manifest_path)
    print(f"📊 Processing {len(df)} videos with true MediaPipe landmarks...")
    
    results = []
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing"):
        s3_key = row['s3_key']
        phrase = row['phrase']
        speaker_id = row['speaker_id']
        output_name = f"{phrase}_{speaker_id}_{idx:06d}"
        
        result = preprocessor.process_single_video(s3_key, output_name)
        result.update({
            'clip_id': output_name,
            'phrase': phrase,
            'speaker_id': speaker_id
        })
        
        # Record for CSV
        if result['success']:
            frame_metrics = result.get('frame_metrics', [])
            
            # Calculate aggregate metrics
            avg_mouth_width = np.mean([m.get('mouth_width_px', 0) for m in frame_metrics if m.get('mouth_width_px', 0) > 0]) if frame_metrics else 0
            avg_crop_w = np.mean([m.get('crop_w_px', 0) for m in frame_metrics if m.get('crop_w_px', 0) > 0]) if frame_metrics else 0
            avg_target_ratio = np.mean([m.get('target_ratio', 0) for m in frame_metrics if m.get('target_ratio', 0) > 0]) if frame_metrics else 0
            avg_coverage_before = np.mean([m.get('coverage_before', 0) for m in frame_metrics]) if frame_metrics else 0
            total_adjustments = sum([m.get('n_adjust', 0) for m in frame_metrics]) if frame_metrics else 0
            
            preprocessor.quality_records.append({
                'clip_id': output_name,
                'phrase': phrase,
                'frames_with_landmarks_pct': result.get('frames_with_landmarks_pct', 0),
                'mouth_width_px': avg_mouth_width,
                'crop_w_px': avg_crop_w,
                'target_ratio': avg_target_ratio,
                'coverage_before': avg_coverage_before,
                'coverage_after': result.get('avg_coverage_after', 0),
                'n_adjust': total_adjustments,
                'valid': result.get('valid', False)
            })
        
        results.append(result)
    
    # Save quality CSV
    if preprocessor.quality_records:
        csv_path = os.path.join(preprocessor.processed_data_dir, 'quality_logs', 'quality_scores.csv')
        pd.DataFrame(preprocessor.quality_records).to_csv(csv_path, index=False)
        print(f"💾 Quality CSV saved to: {csv_path}")
    
    # Print statistics
    successful = len([r for r in results if r['success']])
    valid_clips = len([r for r in preprocessor.quality_records if r['valid']])
    
    print(f"\n📊 Trial Results:")
    print(f"Total processed: {len(results)}")
    print(f"Successful: {successful}")
    print(f"Valid clips (≥90% landmarks): {valid_clips}")
    
    if preprocessor.coverage_values:
        coverage_array = np.array(preprocessor.coverage_values)
        print(f"Coverage stats:")
        print(f"  Mean: {np.mean(coverage_array):.3f}")
        print(f"  Std:  {np.std(coverage_array):.3f}")
        print(f"  Min:  {np.min(coverage_array):.3f}")
        print(f"  Max:  {np.max(coverage_array):.3f}")
        
        # Coverage distribution
        in_range = np.sum((coverage_array >= 0.30) & (coverage_array <= 0.75))
        below_range = np.sum(coverage_array < 0.30)
        above_range = np.sum(coverage_array > 0.75)
        
        print(f"Coverage distribution:")
        print(f"  In range [0.30-0.75]: {in_range} ({in_range/len(coverage_array)*100:.1f}%)")
        print(f"  Below 0.30: {below_range} ({below_range/len(coverage_array)*100:.1f}%)")
        print(f"  Above 0.75: {above_range} ({above_range/len(coverage_array)*100:.1f}%)")
    
    print("\n✅ Trial run completed!")
    print("📋 Next: Generate QC visualizations and side-by-side comparison")
    
    return True


if __name__ == "__main__":
    main()
